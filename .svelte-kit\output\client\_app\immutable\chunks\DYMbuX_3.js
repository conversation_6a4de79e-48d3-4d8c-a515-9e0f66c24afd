const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./CYg-Ejgf.js","./Bj2eRKwr.js","./DYELU7KU.js","./BYziMHbo.js","./B-WGcoYS.js","./Qq4DOyMz.js","./DkikMKFo.js"])))=>i.map(i=>d[i]);
var Hi=Object.defineProperty;var qi=(s,e,t)=>e in s?Hi(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var ds=(s,e,t)=>qi(s,typeof e!="symbol"?e+"":e,t);import"./CWj6FrbW.js";import{i as at}from"./CfAXL0yB.js";import{w as te,x as $i,v as Wi,h as _,W as tt,y as Vi,z as ji,A as us,B as fs,C as Rt,I as Ke,af as zi,ai as Ki,D as $s,F as Ws,G as Qi,al as Vs,ad as js,P,ay as ps,az as It,ac as ms,aA as Ji,aB as Zi,aC as er,a8 as tr,aD as sr,aE as ir,aF as rr,aG as nr,ag as ar,J as or,M as hr,aH as lr,aI as cr,aJ as dr,aK as ur,aL as fr,aM as pr,e as H,K as zs,p as ot,c as Ks,f as Qs,a as O,m as ht,l as q,n as b,Q as T,aN as lt,o as w,aO as ke,aP as ns,t as ee,q as A,aQ as kt,i as ie}from"./DAVv5PCD.js";import{l as as,b as mr,d as ne,w as $e,o as _r,c as gr,e as ue,s as fe,r as Js}from"./BT-7oyhX.js";import{_ as Ct,i as oe,b as yr,p as vr,s as Zs,a as ei}from"./DX-db2lG.js";function xr(s,e){return e}function br(s,e,t,i){for(var r=[],n=e.length,a=0;a<n;a++)er(e[a].e,r,!0);var o=n>0&&r.length===0&&t!==null;if(o){var h=t.parentNode;tr(h),h.append(t),i.clear(),Ie(s,e[0].prev,e[n-1].next)}sr(r,()=>{for(var l=0;l<n;l++){var c=e[l];o||(i.delete(c.k),Ie(s,c.prev,c.next)),ir(c.e,!o)}})}function _s(s,e,t,i,r,n=null){var a=s,o={flags:e,items:new Map,first:null};te&&$i();var h=null,l=!1,c=tt(()=>{var d=t();return js(d)?d:d==null?[]:Vs(d)});Wi(()=>{var d=_(c),u=d.length;if(l&&u===0)return;l=u===0;let f=!1;if(te){var m=Vi(a)===ji;m!==(u===0)&&(a=us(),fs(a),Rt(!1),f=!0)}if(te){for(var y=null,p,g=0;g<u;g++){if(Ke.nodeType===zi&&Ke.data===Ki){a=Ke,f=!0,Rt(!1);break}var C=d[g],x=i(C,g);p=ti(Ke,o,y,null,C,x,g,r,e,t),o.items.set(x,p),y=p}u>0&&fs(us())}te||wr(d,o,a,r,e,i,t),n!==null&&(u===0?h?$s(h):h=Ws(()=>n(a)):h!==null&&Qi(h,()=>{h=null})),f&&Rt(!0),_(c)}),te&&(a=Ke)}function wr(s,e,t,i,r,n,a){var o=s.length,h=e.items,l=e.first,c=l,d,u=null,f=[],m=[],y,p,g,C;for(C=0;C<o;C+=1){if(y=s[C],p=n(y,C),g=h.get(p),g===void 0){var x=c?c.e.nodes_start:t;u=ti(x,e,u,u===null?e.first:u.next,y,p,C,i,r,a),h.set(p,u),f=[],m=[],c=u.next;continue}if(Ar(g,y,C),(g.e.f&It)!==0&&$s(g.e),g!==c){if(d!==void 0&&d.has(g)){if(f.length<m.length){var G=m[0],S;u=G.prev;var $=f[0],X=f[f.length-1];for(S=0;S<f.length;S+=1)gs(f[S],G,t);for(S=0;S<m.length;S+=1)d.delete(m[S]);Ie(e,$.prev,X.next),Ie(e,u,$),Ie(e,X,G),c=G,u=X,C-=1,f=[],m=[]}else d.delete(g),gs(g,c,t),Ie(e,g.prev,g.next),Ie(e,g,u===null?e.first:u.next),Ie(e,u,g),u=g;continue}for(f=[],m=[];c!==null&&c.k!==p;)(c.e.f&It)===0&&(d??(d=new Set)).add(c),m.push(c),c=c.next;if(c===null)continue;g=c}f.push(g),u=g,c=g.next}if(c!==null||d!==void 0){for(var W=d===void 0?[]:Vs(d);c!==null;)(c.e.f&It)===0&&W.push(c),c=c.next;var se=W.length;if(se>0){var Z=null;br(e,W,Z,h)}}ms.first=e.first&&e.first.e,ms.last=u&&u.e}function Ar(s,e,t,i){Zi(s.v,e),s.i=t}function ti(s,e,t,i,r,n,a,o,h,l){var c=(h&rr)!==0,d=(h&nr)===0,u=c?d?P(r,!1,!1):ps(r):r,f=(h&Ji)===0?a:ps(a),m={i:f,v:u,k:n,a:null,e:null,prev:t,next:i};try{return m.e=Ws(()=>o(s,u,f,l),te),m.e.prev=t&&t.e,m.e.next=i&&i.e,t===null?e.first=m:(t.next=m,t.e.next=m.e),i!==null&&(i.prev=m,i.e.prev=m.e),m}finally{}}function gs(s,e,t){for(var i=s.next?s.next.e.nodes_start:t,r=e?e.e.nodes_start:t,n=s.e.nodes_start;n!==i;){var a=ar(n);r.before(n),n=a}}function Ie(s,e,t){e===null?s.first=t:(e.next=t,e.e.next=t&&t.e),t!==null&&(t.prev=e,t.e.prev=e&&e.e)}const ys=[...` 	
\r\f \v\uFEFF`];function kr(s,e,t){var i=s==null?"":""+s;if(t){for(var r in t)if(t[r])i=i?i+" "+r:r;else if(i.length)for(var n=r.length,a=0;(a=i.indexOf(r,a))>=0;){var o=a+n;(a===0||ys.includes(i[a-1]))&&(o===i.length||ys.includes(i[o]))?i=(a===0?"":i.substring(0,a))+i.substring(o+1):a=o}}return i===""?null:i}function Cr(s,e){return s==null?null:String(s)}function Ft(s,e,t,i,r,n){var a=s.__className;if(te||a!==t||a===void 0){var o=kr(t,i,n);(!te||o!==s.getAttribute("class"))&&(o==null?s.removeAttribute("class"):s.className=o),s.__className=t}else if(n&&r!==n)for(var h in n){var l=!!n[h];(r==null||l!==!!r[h])&&s.classList.toggle(h,l)}return n}function Mr(s,e,t,i){var r=s.__style;if(te||r!==e){var n=Cr(e);(!te||n!==s.getAttribute("style"))&&(n==null?s.removeAttribute("style"):s.style.cssText=n),s.__style=e}return i}function si(s,e,t){if(s.multiple){if(e==null)return;if(!js(e))return lr();for(var i of s.options)i.selected=e.includes(st(i));return}for(i of s.options){var r=st(i);if(cr(r,e)){i.selected=!0;return}}(!t||e!==void 0)&&(s.selectedIndex=-1)}function Tr(s){var e=new MutationObserver(()=>{si(s,s.__value)});e.observe(s,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),hr(()=>{e.disconnect()})}function Mt(s,e,t=e){var i=!0;as(s,"change",r=>{var n=r?"[selected]":":checked",a;if(s.multiple)a=[].map.call(s.querySelectorAll(n),st);else{var o=s.querySelector(n)??s.querySelector("option:not([disabled])");a=o&&st(o)}t(a)}),or(()=>{var r=e();if(si(s,r,i),i&&r===void 0){var n=s.querySelector(":checked");n!==null&&(r=st(n),t(r))}s.__value=r,i=!1}),Tr(s)}function st(s){return"__value"in s?s.__value:s.value}const Sr=Symbol("is custom element"),Er=Symbol("is html");function Fe(s){if(te){var e=!1,t=()=>{if(!e){if(e=!0,s.hasAttribute("value")){var i=s.value;vs(s,"value",null),s.value=i}if(s.hasAttribute("checked")){var r=s.checked;vs(s,"checked",null),s.checked=r}}};s.__on_r=t,dr(t),mr()}}function Pr(s,e){var t=ii(s);t.checked!==(t.checked=e??void 0)&&(s.checked=e)}function vs(s,e,t,i){var r=ii(s);te&&(r[e]=s.getAttribute(e),e==="src"||e==="srcset"||e==="href"&&s.nodeName==="LINK")||r[e]!==(r[e]=t)&&(e==="loading"&&(s[ur]=t),s.removeAttribute(e))}function ii(s){return s.__attributes??(s.__attributes={[Sr]:s.nodeName.includes("-"),[Er]:s.namespaceURI===fr})}function Be(s,e,t=e){var i=pr();as(s,"input",r=>{var n=r?s.defaultValue:s.value;if(n=Bt(s)?Dt(n):n,t(n),i&&n!==(n=e())){var a=s.selectionStart,o=s.selectionEnd;s.value=n??"",o!==null&&(s.selectionStart=a,s.selectionEnd=Math.min(o,s.value.length))}}),(te&&s.defaultValue!==s.value||H(e)==null&&s.value)&&t(Bt(s)?Dt(s.value):s.value),zs(()=>{var r=e();Bt(s)&&r===Dt(s.value)||s.type==="date"&&!r&&!s.value||r!==s.value&&(s.value=r??"")})}function Rr(s,e,t=e){as(s,"change",i=>{var r=i?s.defaultChecked:s.checked;t(r)}),(te&&s.defaultChecked!==s.checked||H(e)==null)&&t(s.checked),zs(()=>{var i=e();s.checked=!!i})}function Bt(s){var e=s.type;return e==="number"||e==="range"}function Dt(s){return s===""?null:+s}function Ir(s){return function(...e){var t=e[0];return t.preventDefault(),s==null?void 0:s.apply(this,e)}}const Fr=!1,Wa=Object.freeze(Object.defineProperty({__proto__:null,ssr:Fr},Symbol.toStringTag,{value:"Module"}));function Br(){const{subscribe:s,set:e,update:t}=$e([]);return{subscribe:s,set:e,add:i=>{const r={...i,id:crypto.randomUUID(),createdAt:new Date};return t(n=>[...n,r]),r},update:(i,r)=>{t(n=>n.map(a=>a.id===i?{...a,...r}:a))},complete:i=>{t(r=>r.map(n=>n.id===i?{...n,completed:!0,completedAt:new Date}:n))},delete:i=>{t(r=>r.filter(n=>n.id!==i))},clear:()=>e([])}}function Dr(){const{subscribe:s,set:e,update:t}=$e([]);return{subscribe:s,set:e,add:i=>{const r={...i,id:crypto.randomUUID(),createdAt:new Date};return t(n=>[...n,r]),r},update:(i,r)=>{t(n=>n.map(a=>a.id===i?{...a,...r}:a))},delete:i=>{t(r=>r.filter(n=>n.id!==i))},clear:()=>e([])}}const Te=Br();Dr();const Gr=ne(Te,s=>s.filter(e=>e.completed));ne(Te,s=>s.filter(e=>!e.completed));ne(Te,s=>{const e={};return s.forEach(t=>{const i=t.category||"Uncategorized";e[i]||(e[i]=[]),e[i].push(t)}),e});ne(Gr,s=>s.reduce((e,t)=>e+t.bananaReward,0));const Or={id:crypto.randomUUID(),bananaCount:0,totalTasksCompleted:0,totalGoalsCompleted:0,unlockedFeatures:["basic-tasks"],isPremium:!1,createdAt:new Date,lastActiveAt:new Date};function Lr(){const{subscribe:s,set:e,update:t}=$e(Or);return{subscribe:s,set:e,addBananas:i=>{t(r=>({...r,bananaCount:r.bananaCount+i,lastActiveAt:new Date}))},spendBananas:i=>{t(r=>({...r,bananaCount:Math.max(0,r.bananaCount-i),lastActiveAt:new Date}))},incrementTasksCompleted:()=>{t(i=>({...i,totalTasksCompleted:i.totalTasksCompleted+1,lastActiveAt:new Date}))},incrementGoalsCompleted:()=>{t(i=>({...i,totalGoalsCompleted:i.totalGoalsCompleted+1,lastActiveAt:new Date}))},unlockFeature:i=>{t(r=>({...r,unlockedFeatures:[...r.unlockedFeatures,i],lastActiveAt:new Date}))},setPremium:i=>{t(r=>({...r,isPremium:i,lastActiveAt:new Date}))},updateLastActive:()=>{t(i=>({...i,lastActiveAt:new Date}))}}}const ct=Lr(),Ur={categories:100,"due-dates":200,"priority-tags":300,"habit-tracking":500,analytics:750,"export-options":1e3,collaboration:1500,"calendar-sync":2e3,"custom-themes":2500};ne(ct,s=>e=>{const t=Ur[e];return s.bananaCount>=t});ne(ct,s=>e=>s.isPremium||s.unlockedFeatures.includes(e));const Nr={monkeyPosition:{x:100,y:500},currentScene:"jungle",unlockedAreas:["starting-grove"],cosmetics:{monkeySkin:"default",theme:"jungle"},upgrades:[]},xs={left:!1,right:!1,up:!1,down:!1,jump:!1,interact:!1};function Yr(){const{subscribe:s,set:e,update:t}=$e(Nr);return{subscribe:s,set:e,updateMonkeyPosition:(i,r)=>{t(n=>({...n,monkeyPosition:{x:i,y:r}}))},changeScene:i=>{t(r=>({...r,currentScene:i}))},unlockArea:i=>{t(r=>({...r,unlockedAreas:[...r.unlockedAreas,i]}))},updateCosmetics:i=>{t(r=>({...r,cosmetics:{...r.cosmetics,...i}}))},purchaseUpgrade:i=>{t(r=>({...r,upgrades:[...r.upgrades,{...i,purchased:!0}]}))}}}function Xr(){const{subscribe:s,set:e,update:t}=$e(xs);return{subscribe:s,set:e,setKey:(i,r)=>{t(n=>({...n,[i]:r}))},reset:()=>e(xs)}}const os=Yr();Xr();const Hr=[{id:"faster-monkey",name:"Faster Monkey",description:"+10% movement speed",cost:250,purchased:!1,effect:"speed_boost_10"},{id:"banana-bots",name:"Banana Bots",description:"Auto-harvest 1 banana per minute",cost:500,purchased:!1,effect:"auto_harvest_1"},{id:"double-jump",name:"Double Jump",description:"Jump twice in mid-air",cost:750,purchased:!1,effect:"double_jump"},{id:"banana-magnet",name:"Banana Magnet",description:"Automatically collect nearby bananas",cost:1e3,purchased:!1,effect:"auto_collect"}];ne(os,s=>s.upgrades.filter(e=>e.purchased));ne(os,s=>{const e=s.upgrades.map(t=>t.id);return Hr.filter(t=>!e.includes(t.id))});const qr={soundEnabled:!0,musicEnabled:!0,retroFilter:!1,highContrast:!1,animationsEnabled:!0};function $r(){const{subscribe:s,set:e,update:t}=$e(qr);return{subscribe:s,set:e,toggleSound:()=>{t(i=>({...i,soundEnabled:!i.soundEnabled}))},toggleMusic:()=>{t(i=>({...i,musicEnabled:!i.musicEnabled}))},toggleRetroFilter:()=>{t(i=>({...i,retroFilter:!i.retroFilter}))},toggleHighContrast:()=>{t(i=>({...i,highContrast:!i.highContrast}))},toggleAnimations:()=>{t(i=>({...i,animationsEnabled:!i.animationsEnabled}))},updateSetting:(i,r)=>{t(n=>({...n,[i]:r}))}}}$r();const Wr=[{id:"daily-tasks-3",title:"Daily Productivity",description:"Complete 3 tasks today",type:"daily",requirements:[{type:"complete_tasks",target:3,current:0}],bananaReward:25,completed:!1,progress:0,maxProgress:3},{id:"weekly-streak-7",title:"Consistency Champion",description:"Maintain a 7-day task completion streak",type:"weekly",requirements:[{type:"maintain_streak",target:7,current:0}],bananaReward:100,completed:!1,progress:0,maxProgress:7},{id:"achievement-100-tasks",title:"Century Club",description:"Complete 100 tasks total",type:"achievement",requirements:[{type:"complete_tasks",target:100,current:0}],bananaReward:500,completed:!1,progress:0,maxProgress:100}];function Vr(){const{subscribe:s,set:e,update:t}=$e(Wr);return{subscribe:s,set:e,updateProgress:(i,r)=>{t(n=>n.map(a=>{if(a.id===i){const o=Math.min(r,a.maxProgress),h=o>=a.maxProgress;return{...a,progress:o,completed:h,requirements:a.requirements.map(l=>({...l,current:o}))}}return a}))},completeQuest:i=>{t(r=>r.map(n=>n.id===i?{...n,completed:!0,progress:n.maxProgress}:n))},resetDailyQuests:()=>{t(i=>i.map(r=>r.type==="daily"?{...r,completed:!1,progress:0,requirements:r.requirements.map(n=>({...n,current:0}))}:r))},resetWeeklyQuests:()=>{t(i=>i.map(r=>r.type==="weekly"?{...r,completed:!1,progress:0,requirements:r.requirements.map(n=>({...n,current:0}))}:r))}}}const dt=Vr();ne(dt,s=>s.filter(e=>!e.completed));ne(dt,s=>s.filter(e=>e.completed));ne(dt,s=>s.filter(e=>e.type==="daily"));ne(dt,s=>s.filter(e=>e.type==="weekly"));ne(dt,s=>s.filter(e=>e.type==="achievement"));var Y=(s=>(s.Application="application",s.WebGLPipes="webgl-pipes",s.WebGLPipesAdaptor="webgl-pipes-adaptor",s.WebGLSystem="webgl-system",s.WebGPUPipes="webgpu-pipes",s.WebGPUPipesAdaptor="webgpu-pipes-adaptor",s.WebGPUSystem="webgpu-system",s.CanvasSystem="canvas-system",s.CanvasPipesAdaptor="canvas-pipes-adaptor",s.CanvasPipes="canvas-pipes",s.Asset="asset",s.LoadParser="load-parser",s.ResolveParser="resolve-parser",s.CacheParser="cache-parser",s.DetectionParser="detection-parser",s.MaskEffect="mask-effect",s.BlendMode="blend-mode",s.TextureSource="texture-source",s.Environment="environment",s.ShapeBuilder="shape-builder",s.Batcher="batcher",s))(Y||{});const zt=s=>{if(typeof s=="function"||typeof s=="object"&&s.extension){if(!s.extension)throw new Error("Extension class must have an extension object");s={...typeof s.extension!="object"?{type:s.extension}:s.extension,ref:s}}if(typeof s=="object")s={...s};else throw new Error("Invalid extension type");return typeof s.type=="string"&&(s.type=[s.type]),s},ft=(s,e)=>zt(s).priority??e,xe={_addHandlers:{},_removeHandlers:{},_queue:{},remove(...s){return s.map(zt).forEach(e=>{e.type.forEach(t=>{var i,r;return(r=(i=this._removeHandlers)[t])==null?void 0:r.call(i,e)})}),this},add(...s){return s.map(zt).forEach(e=>{e.type.forEach(t=>{var n,a;const i=this._addHandlers,r=this._queue;i[t]?(a=i[t])==null||a.call(i,e):(r[t]=r[t]||[],(n=r[t])==null||n.push(e))})}),this},handle(s,e,t){var a;const i=this._addHandlers,r=this._removeHandlers;if(i[s]||r[s])throw new Error(`Extension type ${s} already has a handler`);i[s]=e,r[s]=t;const n=this._queue;return n[s]&&((a=n[s])==null||a.forEach(o=>e(o)),delete n[s]),this},handleByMap(s,e){return this.handle(s,t=>{t.name&&(e[t.name]=t.ref)},t=>{t.name&&delete e[t.name]})},handleByNamedList(s,e,t=-1){return this.handle(s,i=>{e.findIndex(n=>n.name===i.name)>=0||(e.push({name:i.name,value:i.ref}),e.sort((n,a)=>ft(a.value,t)-ft(n.value,t)))},i=>{const r=e.findIndex(n=>n.name===i.name);r!==-1&&e.splice(r,1)})},handleByList(s,e,t=-1){return this.handle(s,i=>{e.includes(i.ref)||(e.push(i.ref),e.sort((r,n)=>ft(n,t)-ft(r,t)))},i=>{const r=e.indexOf(i.ref);r!==-1&&e.splice(r,1)})},mixin(s,...e){for(const t of e)Object.defineProperties(s.prototype,Object.getOwnPropertyDescriptors(t))}},jr={extension:{type:Y.Environment,name:"browser",priority:-1},test:()=>!0,load:async()=>{await Ct(()=>import("./CYg-Ejgf.js"),__vite__mapDeps([0,1,2,3]),import.meta.url)}},zr={extension:{type:Y.Environment,name:"webworker",priority:0},test:()=>typeof self<"u"&&self.WorkerGlobalScope!==void 0,load:async()=>{await Ct(()=>import("./Bj2eRKwr.js"),__vite__mapDeps([1,2,3]),import.meta.url)}};class Q{constructor(e,t,i){this._x=t||0,this._y=i||0,this._observer=e}clone(e){return new Q(e??this._observer,this._x,this._y)}set(e=0,t=e){return(this._x!==e||this._y!==t)&&(this._x=e,this._y=t,this._observer._onUpdate(this)),this}copyFrom(e){return(this._x!==e.x||this._y!==e.y)&&(this._x=e.x,this._y=e.y,this._observer._onUpdate(this)),this}copyTo(e){return e.set(this._x,this._y),e}equals(e){return e.x===this._x&&e.y===this._y}toString(){return`[pixi.js/math:ObservablePoint x=${this._x} y=${this._y} scope=${this._observer}]`}get x(){return this._x}set x(e){this._x!==e&&(this._x=e,this._observer._onUpdate(this))}get y(){return this._y}set y(e){this._y!==e&&(this._y=e,this._observer._onUpdate(this))}}function Kr(s){return s&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s}var Gt={exports:{}},bs;function Qr(){return bs||(bs=1,function(s){var e=Object.prototype.hasOwnProperty,t="~";function i(){}Object.create&&(i.prototype=Object.create(null),new i().__proto__||(t=!1));function r(h,l,c){this.fn=h,this.context=l,this.once=c||!1}function n(h,l,c,d,u){if(typeof c!="function")throw new TypeError("The listener must be a function");var f=new r(c,d||h,u),m=t?t+l:l;return h._events[m]?h._events[m].fn?h._events[m]=[h._events[m],f]:h._events[m].push(f):(h._events[m]=f,h._eventsCount++),h}function a(h,l){--h._eventsCount===0?h._events=new i:delete h._events[l]}function o(){this._events=new i,this._eventsCount=0}o.prototype.eventNames=function(){var l=[],c,d;if(this._eventsCount===0)return l;for(d in c=this._events)e.call(c,d)&&l.push(t?d.slice(1):d);return Object.getOwnPropertySymbols?l.concat(Object.getOwnPropertySymbols(c)):l},o.prototype.listeners=function(l){var c=t?t+l:l,d=this._events[c];if(!d)return[];if(d.fn)return[d.fn];for(var u=0,f=d.length,m=new Array(f);u<f;u++)m[u]=d[u].fn;return m},o.prototype.listenerCount=function(l){var c=t?t+l:l,d=this._events[c];return d?d.fn?1:d.length:0},o.prototype.emit=function(l,c,d,u,f,m){var y=t?t+l:l;if(!this._events[y])return!1;var p=this._events[y],g=arguments.length,C,x;if(p.fn){switch(p.once&&this.removeListener(l,p.fn,void 0,!0),g){case 1:return p.fn.call(p.context),!0;case 2:return p.fn.call(p.context,c),!0;case 3:return p.fn.call(p.context,c,d),!0;case 4:return p.fn.call(p.context,c,d,u),!0;case 5:return p.fn.call(p.context,c,d,u,f),!0;case 6:return p.fn.call(p.context,c,d,u,f,m),!0}for(x=1,C=new Array(g-1);x<g;x++)C[x-1]=arguments[x];p.fn.apply(p.context,C)}else{var G=p.length,S;for(x=0;x<G;x++)switch(p[x].once&&this.removeListener(l,p[x].fn,void 0,!0),g){case 1:p[x].fn.call(p[x].context);break;case 2:p[x].fn.call(p[x].context,c);break;case 3:p[x].fn.call(p[x].context,c,d);break;case 4:p[x].fn.call(p[x].context,c,d,u);break;default:if(!C)for(S=1,C=new Array(g-1);S<g;S++)C[S-1]=arguments[S];p[x].fn.apply(p[x].context,C)}}return!0},o.prototype.on=function(l,c,d){return n(this,l,c,d,!1)},o.prototype.once=function(l,c,d){return n(this,l,c,d,!0)},o.prototype.removeListener=function(l,c,d,u){var f=t?t+l:l;if(!this._events[f])return this;if(!c)return a(this,f),this;var m=this._events[f];if(m.fn)m.fn===c&&(!u||m.once)&&(!d||m.context===d)&&a(this,f);else{for(var y=0,p=[],g=m.length;y<g;y++)(m[y].fn!==c||u&&!m[y].once||d&&m[y].context!==d)&&p.push(m[y]);p.length?this._events[f]=p.length===1?p[0]:p:a(this,f)}return this},o.prototype.removeAllListeners=function(l){var c;return l?(c=t?t+l:l,this._events[c]&&a(this,c)):(this._events=new i,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=t,o.EventEmitter=o,s.exports=o}(Gt)),Gt.exports}var Jr=Qr();const ut=Kr(Jr),Zr=Math.PI*2,en=180/Math.PI,tn=Math.PI/180;class ye{constructor(e=0,t=0){this.x=0,this.y=0,this.x=e,this.y=t}clone(){return new ye(this.x,this.y)}copyFrom(e){return this.set(e.x,e.y),this}copyTo(e){return e.set(this.x,this.y),e}equals(e){return e.x===this.x&&e.y===this.y}set(e=0,t=e){return this.x=e,this.y=t,this}toString(){return`[pixi.js/math:Point x=${this.x} y=${this.y}]`}static get shared(){return Ot.x=0,Ot.y=0,Ot}}const Ot=new ye;class D{constructor(e=1,t=0,i=0,r=1,n=0,a=0){this.array=null,this.a=e,this.b=t,this.c=i,this.d=r,this.tx=n,this.ty=a}fromArray(e){this.a=e[0],this.b=e[1],this.c=e[3],this.d=e[4],this.tx=e[2],this.ty=e[5]}set(e,t,i,r,n,a){return this.a=e,this.b=t,this.c=i,this.d=r,this.tx=n,this.ty=a,this}toArray(e,t){this.array||(this.array=new Float32Array(9));const i=t||this.array;return e?(i[0]=this.a,i[1]=this.b,i[2]=0,i[3]=this.c,i[4]=this.d,i[5]=0,i[6]=this.tx,i[7]=this.ty,i[8]=1):(i[0]=this.a,i[1]=this.c,i[2]=this.tx,i[3]=this.b,i[4]=this.d,i[5]=this.ty,i[6]=0,i[7]=0,i[8]=1),i}apply(e,t){t=t||new ye;const i=e.x,r=e.y;return t.x=this.a*i+this.c*r+this.tx,t.y=this.b*i+this.d*r+this.ty,t}applyInverse(e,t){t=t||new ye;const i=this.a,r=this.b,n=this.c,a=this.d,o=this.tx,h=this.ty,l=1/(i*a+n*-r),c=e.x,d=e.y;return t.x=a*l*c+-n*l*d+(h*n-o*a)*l,t.y=i*l*d+-r*l*c+(-h*i+o*r)*l,t}translate(e,t){return this.tx+=e,this.ty+=t,this}scale(e,t){return this.a*=e,this.d*=t,this.c*=e,this.b*=t,this.tx*=e,this.ty*=t,this}rotate(e){const t=Math.cos(e),i=Math.sin(e),r=this.a,n=this.c,a=this.tx;return this.a=r*t-this.b*i,this.b=r*i+this.b*t,this.c=n*t-this.d*i,this.d=n*i+this.d*t,this.tx=a*t-this.ty*i,this.ty=a*i+this.ty*t,this}append(e){const t=this.a,i=this.b,r=this.c,n=this.d;return this.a=e.a*t+e.b*r,this.b=e.a*i+e.b*n,this.c=e.c*t+e.d*r,this.d=e.c*i+e.d*n,this.tx=e.tx*t+e.ty*r+this.tx,this.ty=e.tx*i+e.ty*n+this.ty,this}appendFrom(e,t){const i=e.a,r=e.b,n=e.c,a=e.d,o=e.tx,h=e.ty,l=t.a,c=t.b,d=t.c,u=t.d;return this.a=i*l+r*d,this.b=i*c+r*u,this.c=n*l+a*d,this.d=n*c+a*u,this.tx=o*l+h*d+t.tx,this.ty=o*c+h*u+t.ty,this}setTransform(e,t,i,r,n,a,o,h,l){return this.a=Math.cos(o+l)*n,this.b=Math.sin(o+l)*n,this.c=-Math.sin(o-h)*a,this.d=Math.cos(o-h)*a,this.tx=e-(i*this.a+r*this.c),this.ty=t-(i*this.b+r*this.d),this}prepend(e){const t=this.tx;if(e.a!==1||e.b!==0||e.c!==0||e.d!==1){const i=this.a,r=this.c;this.a=i*e.a+this.b*e.c,this.b=i*e.b+this.b*e.d,this.c=r*e.a+this.d*e.c,this.d=r*e.b+this.d*e.d}return this.tx=t*e.a+this.ty*e.c+e.tx,this.ty=t*e.b+this.ty*e.d+e.ty,this}decompose(e){const t=this.a,i=this.b,r=this.c,n=this.d,a=e.pivot,o=-Math.atan2(-r,n),h=Math.atan2(i,t),l=Math.abs(o+h);return l<1e-5||Math.abs(Zr-l)<1e-5?(e.rotation=h,e.skew.x=e.skew.y=0):(e.rotation=0,e.skew.x=o,e.skew.y=h),e.scale.x=Math.sqrt(t*t+i*i),e.scale.y=Math.sqrt(r*r+n*n),e.position.x=this.tx+(a.x*t+a.y*r),e.position.y=this.ty+(a.x*i+a.y*n),e}invert(){const e=this.a,t=this.b,i=this.c,r=this.d,n=this.tx,a=e*r-t*i;return this.a=r/a,this.b=-t/a,this.c=-i/a,this.d=e/a,this.tx=(i*this.ty-r*n)/a,this.ty=-(e*this.ty-t*n)/a,this}isIdentity(){return this.a===1&&this.b===0&&this.c===0&&this.d===1&&this.tx===0&&this.ty===0}identity(){return this.a=1,this.b=0,this.c=0,this.d=1,this.tx=0,this.ty=0,this}clone(){const e=new D;return e.a=this.a,e.b=this.b,e.c=this.c,e.d=this.d,e.tx=this.tx,e.ty=this.ty,e}copyTo(e){return e.a=this.a,e.b=this.b,e.c=this.c,e.d=this.d,e.tx=this.tx,e.ty=this.ty,e}copyFrom(e){return this.a=e.a,this.b=e.b,this.c=e.c,this.d=e.d,this.tx=e.tx,this.ty=e.ty,this}equals(e){return e.a===this.a&&e.b===this.b&&e.c===this.c&&e.d===this.d&&e.tx===this.tx&&e.ty===this.ty}toString(){return`[pixi.js:Matrix a=${this.a} b=${this.b} c=${this.c} d=${this.d} tx=${this.tx} ty=${this.ty}]`}static get IDENTITY(){return rn.identity()}static get shared(){return sn.identity()}}const sn=new D,rn=new D,Ne=[1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1,0,1],Ye=[0,1,1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1],Xe=[0,-1,-1,-1,0,1,1,1,0,1,1,1,0,-1,-1,-1],He=[1,1,0,-1,-1,-1,0,1,-1,-1,0,1,1,1,0,-1],Kt=[],ri=[],pt=Math.sign;function nn(){for(let s=0;s<16;s++){const e=[];Kt.push(e);for(let t=0;t<16;t++){const i=pt(Ne[s]*Ne[t]+Xe[s]*Ye[t]),r=pt(Ye[s]*Ne[t]+He[s]*Ye[t]),n=pt(Ne[s]*Xe[t]+Xe[s]*He[t]),a=pt(Ye[s]*Xe[t]+He[s]*He[t]);for(let o=0;o<16;o++)if(Ne[o]===i&&Ye[o]===r&&Xe[o]===n&&He[o]===a){e.push(o);break}}}for(let s=0;s<16;s++){const e=new D;e.set(Ne[s],Ye[s],Xe[s],He[s],0,0),ri.push(e)}}nn();const F={E:0,SE:1,S:2,SW:3,W:4,NW:5,N:6,NE:7,MIRROR_VERTICAL:8,MAIN_DIAGONAL:10,MIRROR_HORIZONTAL:12,REVERSE_DIAGONAL:14,uX:s=>Ne[s],uY:s=>Ye[s],vX:s=>Xe[s],vY:s=>He[s],inv:s=>s&8?s&15:-s&7,add:(s,e)=>Kt[s][e],sub:(s,e)=>Kt[s][F.inv(e)],rotate180:s=>s^4,isVertical:s=>(s&3)===2,byDirection:(s,e)=>Math.abs(s)*2<=Math.abs(e)?e>=0?F.S:F.N:Math.abs(e)*2<=Math.abs(s)?s>0?F.E:F.W:e>0?s>0?F.SE:F.SW:s>0?F.NE:F.NW,matrixAppendRotationInv:(s,e,t=0,i=0)=>{const r=ri[F.inv(e)];r.tx=t,r.ty=i,s.append(r)}},mt=[new ye,new ye,new ye,new ye];class ve{constructor(e=0,t=0,i=0,r=0){this.type="rectangle",this.x=Number(e),this.y=Number(t),this.width=Number(i),this.height=Number(r)}get left(){return this.x}get right(){return this.x+this.width}get top(){return this.y}get bottom(){return this.y+this.height}isEmpty(){return this.left===this.right||this.top===this.bottom}static get EMPTY(){return new ve(0,0,0,0)}clone(){return new ve(this.x,this.y,this.width,this.height)}copyFromBounds(e){return this.x=e.minX,this.y=e.minY,this.width=e.maxX-e.minX,this.height=e.maxY-e.minY,this}copyFrom(e){return this.x=e.x,this.y=e.y,this.width=e.width,this.height=e.height,this}copyTo(e){return e.copyFrom(this),e}contains(e,t){return this.width<=0||this.height<=0?!1:e>=this.x&&e<this.x+this.width&&t>=this.y&&t<this.y+this.height}strokeContains(e,t,i,r=.5){const{width:n,height:a}=this;if(n<=0||a<=0)return!1;const o=this.x,h=this.y,l=i*(1-r),c=i-l,d=o-l,u=o+n+l,f=h-l,m=h+a+l,y=o+c,p=o+n-c,g=h+c,C=h+a-c;return e>=d&&e<=u&&t>=f&&t<=m&&!(e>y&&e<p&&t>g&&t<C)}intersects(e,t){if(!t){const W=this.x<e.x?e.x:this.x;if((this.right>e.right?e.right:this.right)<=W)return!1;const Z=this.y<e.y?e.y:this.y;return(this.bottom>e.bottom?e.bottom:this.bottom)>Z}const i=this.left,r=this.right,n=this.top,a=this.bottom;if(r<=i||a<=n)return!1;const o=mt[0].set(e.left,e.top),h=mt[1].set(e.left,e.bottom),l=mt[2].set(e.right,e.top),c=mt[3].set(e.right,e.bottom);if(l.x<=o.x||h.y<=o.y)return!1;const d=Math.sign(t.a*t.d-t.b*t.c);if(d===0||(t.apply(o,o),t.apply(h,h),t.apply(l,l),t.apply(c,c),Math.max(o.x,h.x,l.x,c.x)<=i||Math.min(o.x,h.x,l.x,c.x)>=r||Math.max(o.y,h.y,l.y,c.y)<=n||Math.min(o.y,h.y,l.y,c.y)>=a))return!1;const u=d*(h.y-o.y),f=d*(o.x-h.x),m=u*i+f*n,y=u*r+f*n,p=u*i+f*a,g=u*r+f*a;if(Math.max(m,y,p,g)<=u*o.x+f*o.y||Math.min(m,y,p,g)>=u*c.x+f*c.y)return!1;const C=d*(o.y-l.y),x=d*(l.x-o.x),G=C*i+x*n,S=C*r+x*n,$=C*i+x*a,X=C*r+x*a;return!(Math.max(G,S,$,X)<=C*o.x+x*o.y||Math.min(G,S,$,X)>=C*c.x+x*c.y)}pad(e=0,t=e){return this.x-=e,this.y-=t,this.width+=e*2,this.height+=t*2,this}fit(e){const t=Math.max(this.x,e.x),i=Math.min(this.x+this.width,e.x+e.width),r=Math.max(this.y,e.y),n=Math.min(this.y+this.height,e.y+e.height);return this.x=t,this.width=Math.max(i-t,0),this.y=r,this.height=Math.max(n-r,0),this}ceil(e=1,t=.001){const i=Math.ceil((this.x+this.width-t)*e)/e,r=Math.ceil((this.y+this.height-t)*e)/e;return this.x=Math.floor((this.x+t)*e)/e,this.y=Math.floor((this.y+t)*e)/e,this.width=i-this.x,this.height=r-this.y,this}enlarge(e){const t=Math.min(this.x,e.x),i=Math.max(this.x+this.width,e.x+e.width),r=Math.min(this.y,e.y),n=Math.max(this.y+this.height,e.y+e.height);return this.x=t,this.width=i-t,this.y=r,this.height=n-r,this}getBounds(e){return e||(e=new ve),e.copyFrom(this),e}containsRect(e){if(this.width<=0||this.height<=0)return!1;const t=e.x,i=e.y,r=e.x+e.width,n=e.y+e.height;return t>=this.x&&t<this.x+this.width&&i>=this.y&&i<this.y+this.height&&r>=this.x&&r<this.x+this.width&&n>=this.y&&n<this.y+this.height}set(e,t,i,r){return this.x=e,this.y=t,this.width=i,this.height=r,this}toString(){return`[pixi.js/math:Rectangle x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`}}const Lt={default:-1};function Ce(s="default"){return Lt[s]===void 0&&(Lt[s]=-1),++Lt[s]}const ws={},Ee="8.0.0",Va="8.3.4";function le(s,e,t=3){if(ws[e])return;let i=new Error().stack;typeof i>"u"?console.warn("PixiJS Deprecation Warning: ",`${e}
Deprecated since v${s}`):(i=i.split(`
`).splice(t).join(`
`),console.groupCollapsed?(console.groupCollapsed("%cPixiJS Deprecation Warning: %c%s","color:#614108;background:#fffbe6","font-weight:normal;color:#614108;background:#fffbe6",`${e}
Deprecated since v${s}`),console.warn(i),console.groupEnd()):(console.warn("PixiJS Deprecation Warning: ",`${e}
Deprecated since v${s}`),console.warn(i))),ws[e]=!0}const ni=()=>{};function As(s){return s+=s===0?1:0,--s,s|=s>>>1,s|=s>>>2,s|=s>>>4,s|=s>>>8,s|=s>>>16,s+1}function ks(s){return!(s&s-1)&&!!s}function an(s){const e={};for(const t in s)s[t]!==void 0&&(e[t]=s[t]);return e}const Cs=Object.create(null);function on(s){const e=Cs[s];return e===void 0&&(Cs[s]=Ce("resource")),e}const ai=class oi extends ut{constructor(e={}){super(),this._resourceType="textureSampler",this._touched=0,this._maxAnisotropy=1,this.destroyed=!1,e={...oi.defaultOptions,...e},this.addressMode=e.addressMode,this.addressModeU=e.addressModeU??this.addressModeU,this.addressModeV=e.addressModeV??this.addressModeV,this.addressModeW=e.addressModeW??this.addressModeW,this.scaleMode=e.scaleMode,this.magFilter=e.magFilter??this.magFilter,this.minFilter=e.minFilter??this.minFilter,this.mipmapFilter=e.mipmapFilter??this.mipmapFilter,this.lodMinClamp=e.lodMinClamp,this.lodMaxClamp=e.lodMaxClamp,this.compare=e.compare,this.maxAnisotropy=e.maxAnisotropy??1}set addressMode(e){this.addressModeU=e,this.addressModeV=e,this.addressModeW=e}get addressMode(){return this.addressModeU}set wrapMode(e){le(Ee,"TextureStyle.wrapMode is now TextureStyle.addressMode"),this.addressMode=e}get wrapMode(){return this.addressMode}set scaleMode(e){this.magFilter=e,this.minFilter=e,this.mipmapFilter=e}get scaleMode(){return this.magFilter}set maxAnisotropy(e){this._maxAnisotropy=Math.min(e,16),this._maxAnisotropy>1&&(this.scaleMode="linear")}get maxAnisotropy(){return this._maxAnisotropy}get _resourceId(){return this._sharedResourceId||this._generateResourceId()}update(){this.emit("change",this),this._sharedResourceId=null}_generateResourceId(){const e=`${this.addressModeU}-${this.addressModeV}-${this.addressModeW}-${this.magFilter}-${this.minFilter}-${this.mipmapFilter}-${this.lodMinClamp}-${this.lodMaxClamp}-${this.compare}-${this._maxAnisotropy}`;return this._sharedResourceId=on(e),this._resourceId}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this.removeAllListeners()}};ai.defaultOptions={addressMode:"clamp-to-edge",scaleMode:"linear"};let hi=ai;const li=class ci extends ut{constructor(e={}){super(),this.options=e,this.uid=Ce("textureSource"),this._resourceType="textureSource",this._resourceId=Ce("resource"),this.uploadMethodId="unknown",this._resolution=1,this.pixelWidth=1,this.pixelHeight=1,this.width=1,this.height=1,this.sampleCount=1,this.mipLevelCount=1,this.autoGenerateMipmaps=!1,this.format="rgba8unorm",this.dimension="2d",this.antialias=!1,this._touched=0,this._batchTick=-1,this._textureBindLocation=-1,e={...ci.defaultOptions,...e},this.label=e.label??"",this.resource=e.resource,this.autoGarbageCollect=e.autoGarbageCollect,this._resolution=e.resolution,e.width?this.pixelWidth=e.width*this._resolution:this.pixelWidth=this.resource?this.resourceWidth??1:1,e.height?this.pixelHeight=e.height*this._resolution:this.pixelHeight=this.resource?this.resourceHeight??1:1,this.width=this.pixelWidth/this._resolution,this.height=this.pixelHeight/this._resolution,this.format=e.format,this.dimension=e.dimensions,this.mipLevelCount=e.mipLevelCount,this.autoGenerateMipmaps=e.autoGenerateMipmaps,this.sampleCount=e.sampleCount,this.antialias=e.antialias,this.alphaMode=e.alphaMode,this.style=new hi(an(e)),this.destroyed=!1,this._refreshPOT()}get source(){return this}get style(){return this._style}set style(e){var t,i;this.style!==e&&((t=this._style)==null||t.off("change",this._onStyleChange,this),this._style=e,(i=this._style)==null||i.on("change",this._onStyleChange,this),this._onStyleChange())}get addressMode(){return this._style.addressMode}set addressMode(e){this._style.addressMode=e}get repeatMode(){return this._style.addressMode}set repeatMode(e){this._style.addressMode=e}get magFilter(){return this._style.magFilter}set magFilter(e){this._style.magFilter=e}get minFilter(){return this._style.minFilter}set minFilter(e){this._style.minFilter=e}get mipmapFilter(){return this._style.mipmapFilter}set mipmapFilter(e){this._style.mipmapFilter=e}get lodMinClamp(){return this._style.lodMinClamp}set lodMinClamp(e){this._style.lodMinClamp=e}get lodMaxClamp(){return this._style.lodMaxClamp}set lodMaxClamp(e){this._style.lodMaxClamp=e}_onStyleChange(){this.emit("styleChange",this)}update(){if(this.resource){const e=this._resolution;if(this.resize(this.resourceWidth/e,this.resourceHeight/e))return}this.emit("update",this)}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this._style&&(this._style.destroy(),this._style=null),this.uploadMethodId=null,this.resource=null,this.removeAllListeners()}unload(){this._resourceId=Ce("resource"),this.emit("change",this),this.emit("unload",this)}get resourceWidth(){const{resource:e}=this;return e.naturalWidth||e.videoWidth||e.displayWidth||e.width}get resourceHeight(){const{resource:e}=this;return e.naturalHeight||e.videoHeight||e.displayHeight||e.height}get resolution(){return this._resolution}set resolution(e){this._resolution!==e&&(this._resolution=e,this.width=this.pixelWidth/e,this.height=this.pixelHeight/e)}resize(e,t,i){i||(i=this._resolution),e||(e=this.width),t||(t=this.height);const r=Math.round(e*i),n=Math.round(t*i);return this.width=r/i,this.height=n/i,this._resolution=i,this.pixelWidth===r&&this.pixelHeight===n?!1:(this._refreshPOT(),this.pixelWidth=r,this.pixelHeight=n,this.emit("resize",this),this._resourceId=Ce("resource"),this.emit("change",this),!0)}updateMipmaps(){this.autoGenerateMipmaps&&this.mipLevelCount>1&&this.emit("updateMipmaps",this)}set wrapMode(e){this._style.wrapMode=e}get wrapMode(){return this._style.wrapMode}set scaleMode(e){this._style.scaleMode=e}get scaleMode(){return this._style.scaleMode}_refreshPOT(){this.isPowerOfTwo=ks(this.pixelWidth)&&ks(this.pixelHeight)}static test(e){throw new Error("Unimplemented")}};li.defaultOptions={resolution:1,format:"bgra8unorm",alphaMode:"premultiply-alpha-on-upload",dimensions:"2d",mipLevelCount:1,autoGenerateMipmaps:!1,sampleCount:1,antialias:!1,autoGarbageCollect:!1};let pe=li;class hs extends pe{constructor(e){const t=e.resource||new Float32Array(e.width*e.height*4);let i=e.format;i||(t instanceof Float32Array?i="rgba32float":t instanceof Int32Array||t instanceof Uint32Array?i="rgba32uint":t instanceof Int16Array||t instanceof Uint16Array?i="rgba16uint":(t instanceof Int8Array,i="bgra8unorm")),super({...e,resource:t,format:i}),this.uploadMethodId="buffer"}static test(e){return e instanceof Int8Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array}}hs.extension=Y.TextureSource;const Ms=new D;class hn{constructor(e,t){this.mapCoord=new D,this.uClampFrame=new Float32Array(4),this.uClampOffset=new Float32Array(2),this._textureID=-1,this._updateID=0,this.clampOffset=0,typeof t>"u"?this.clampMargin=e.width<10?0:.5:this.clampMargin=t,this.isSimple=!1,this.texture=e}get texture(){return this._texture}set texture(e){var t;this.texture!==e&&((t=this._texture)==null||t.removeListener("update",this.update,this),this._texture=e,this._texture.addListener("update",this.update,this),this.update())}multiplyUvs(e,t){t===void 0&&(t=e);const i=this.mapCoord;for(let r=0;r<e.length;r+=2){const n=e[r],a=e[r+1];t[r]=n*i.a+a*i.c+i.tx,t[r+1]=n*i.b+a*i.d+i.ty}return t}update(){const e=this._texture;this._updateID++;const t=e.uvs;this.mapCoord.set(t.x1-t.x0,t.y1-t.y0,t.x3-t.x0,t.y3-t.y0,t.x0,t.y0);const i=e.orig,r=e.trim;r&&(Ms.set(i.width/r.width,0,0,i.height/r.height,-r.x/r.width,-r.y/r.height),this.mapCoord.append(Ms));const n=e.source,a=this.uClampFrame,o=this.clampMargin/n._resolution,h=this.clampOffset/n._resolution;return a[0]=(e.frame.x+o+h)/n.width,a[1]=(e.frame.y+o+h)/n.height,a[2]=(e.frame.x+e.frame.width-o+h)/n.width,a[3]=(e.frame.y+e.frame.height-o+h)/n.height,this.uClampOffset[0]=this.clampOffset/n.pixelWidth,this.uClampOffset[1]=this.clampOffset/n.pixelHeight,this.isSimple=e.frame.width===n.width&&e.frame.height===n.height&&e.rotate===0,!0}}class B extends ut{constructor({source:e,label:t,frame:i,orig:r,trim:n,defaultAnchor:a,defaultBorders:o,rotate:h,dynamic:l}={}){if(super(),this.uid=Ce("texture"),this.uvs={x0:0,y0:0,x1:0,y1:0,x2:0,y2:0,x3:0,y3:0},this.frame=new ve,this.noFrame=!1,this.dynamic=!1,this.isTexture=!0,this.label=t,this.source=(e==null?void 0:e.source)??new pe,this.noFrame=!i,i)this.frame.copyFrom(i);else{const{width:c,height:d}=this._source;this.frame.width=c,this.frame.height=d}this.orig=r||this.frame,this.trim=n,this.rotate=h??0,this.defaultAnchor=a,this.defaultBorders=o,this.destroyed=!1,this.dynamic=l||!1,this.updateUvs()}set source(e){this._source&&this._source.off("resize",this.update,this),this._source=e,e.on("resize",this.update,this),this.emit("update",this)}get source(){return this._source}get textureMatrix(){return this._textureMatrix||(this._textureMatrix=new hn(this)),this._textureMatrix}get width(){return this.orig.width}get height(){return this.orig.height}updateUvs(){const{uvs:e,frame:t}=this,{width:i,height:r}=this._source,n=t.x/i,a=t.y/r,o=t.width/i,h=t.height/r;let l=this.rotate;if(l){const c=o/2,d=h/2,u=n+c,f=a+d;l=F.add(l,F.NW),e.x0=u+c*F.uX(l),e.y0=f+d*F.uY(l),l=F.add(l,2),e.x1=u+c*F.uX(l),e.y1=f+d*F.uY(l),l=F.add(l,2),e.x2=u+c*F.uX(l),e.y2=f+d*F.uY(l),l=F.add(l,2),e.x3=u+c*F.uX(l),e.y3=f+d*F.uY(l)}else e.x0=n,e.y0=a,e.x1=n+o,e.y1=a,e.x2=n+o,e.y2=a+h,e.x3=n,e.y3=a+h}destroy(e=!1){this._source&&e&&(this._source.destroy(),this._source=null),this._textureMatrix=null,this.destroyed=!0,this.emit("destroy",this),this.removeAllListeners()}update(){this.noFrame&&(this.frame.width=this._source.width,this.frame.height=this._source.height),this.updateUvs(),this.emit("update",this)}get baseTexture(){return le(Ee,"Texture.baseTexture is now Texture.source"),this._source}}B.EMPTY=new B({label:"EMPTY",source:new pe({label:"EMPTY"})});B.EMPTY.destroy=ni;B.WHITE=new B({source:new hs({resource:new Uint8Array([255,255,255,255]),width:1,height:1,alphaMode:"premultiply-alpha-on-upload",label:"WHITE"}),label:"WHITE"});B.WHITE.destroy=ni;function ln(s,e,t){const{width:i,height:r}=t.orig,n=t.trim;if(n){const a=n.width,o=n.height;s.minX=n.x-e._x*i,s.maxX=s.minX+a,s.minY=n.y-e._y*r,s.maxY=s.minY+o}else s.minX=-e._x*i,s.maxX=s.minX+i,s.minY=-e._y*r,s.maxY=s.minY+r}const Ts=new D;class Ge{constructor(e=1/0,t=1/0,i=-1/0,r=-1/0){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.matrix=Ts,this.minX=e,this.minY=t,this.maxX=i,this.maxY=r}isEmpty(){return this.minX>this.maxX||this.minY>this.maxY}get rectangle(){this._rectangle||(this._rectangle=new ve);const e=this._rectangle;return this.minX>this.maxX||this.minY>this.maxY?(e.x=0,e.y=0,e.width=0,e.height=0):e.copyFromBounds(this),e}clear(){return this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.matrix=Ts,this}set(e,t,i,r){this.minX=e,this.minY=t,this.maxX=i,this.maxY=r}addFrame(e,t,i,r,n){n||(n=this.matrix);const a=n.a,o=n.b,h=n.c,l=n.d,c=n.tx,d=n.ty;let u=this.minX,f=this.minY,m=this.maxX,y=this.maxY,p=a*e+h*t+c,g=o*e+l*t+d;p<u&&(u=p),g<f&&(f=g),p>m&&(m=p),g>y&&(y=g),p=a*i+h*t+c,g=o*i+l*t+d,p<u&&(u=p),g<f&&(f=g),p>m&&(m=p),g>y&&(y=g),p=a*e+h*r+c,g=o*e+l*r+d,p<u&&(u=p),g<f&&(f=g),p>m&&(m=p),g>y&&(y=g),p=a*i+h*r+c,g=o*i+l*r+d,p<u&&(u=p),g<f&&(f=g),p>m&&(m=p),g>y&&(y=g),this.minX=u,this.minY=f,this.maxX=m,this.maxY=y}addRect(e,t){this.addFrame(e.x,e.y,e.x+e.width,e.y+e.height,t)}addBounds(e,t){this.addFrame(e.minX,e.minY,e.maxX,e.maxY,t)}addBoundsMask(e){this.minX=this.minX>e.minX?this.minX:e.minX,this.minY=this.minY>e.minY?this.minY:e.minY,this.maxX=this.maxX<e.maxX?this.maxX:e.maxX,this.maxY=this.maxY<e.maxY?this.maxY:e.maxY}applyMatrix(e){const t=this.minX,i=this.minY,r=this.maxX,n=this.maxY,{a,b:o,c:h,d:l,tx:c,ty:d}=e;let u=a*t+h*i+c,f=o*t+l*i+d;this.minX=u,this.minY=f,this.maxX=u,this.maxY=f,u=a*r+h*i+c,f=o*r+l*i+d,this.minX=u<this.minX?u:this.minX,this.minY=f<this.minY?f:this.minY,this.maxX=u>this.maxX?u:this.maxX,this.maxY=f>this.maxY?f:this.maxY,u=a*t+h*n+c,f=o*t+l*n+d,this.minX=u<this.minX?u:this.minX,this.minY=f<this.minY?f:this.minY,this.maxX=u>this.maxX?u:this.maxX,this.maxY=f>this.maxY?f:this.maxY,u=a*r+h*n+c,f=o*r+l*n+d,this.minX=u<this.minX?u:this.minX,this.minY=f<this.minY?f:this.minY,this.maxX=u>this.maxX?u:this.maxX,this.maxY=f>this.maxY?f:this.maxY}fit(e){return this.minX<e.left&&(this.minX=e.left),this.maxX>e.right&&(this.maxX=e.right),this.minY<e.top&&(this.minY=e.top),this.maxY>e.bottom&&(this.maxY=e.bottom),this}fitBounds(e,t,i,r){return this.minX<e&&(this.minX=e),this.maxX>t&&(this.maxX=t),this.minY<i&&(this.minY=i),this.maxY>r&&(this.maxY=r),this}pad(e,t=e){return this.minX-=e,this.maxX+=e,this.minY-=t,this.maxY+=t,this}ceil(){return this.minX=Math.floor(this.minX),this.minY=Math.floor(this.minY),this.maxX=Math.ceil(this.maxX),this.maxY=Math.ceil(this.maxY),this}clone(){return new Ge(this.minX,this.minY,this.maxX,this.maxY)}scale(e,t=e){return this.minX*=e,this.minY*=t,this.maxX*=e,this.maxY*=t,this}get x(){return this.minX}set x(e){const t=this.maxX-this.minX;this.minX=e,this.maxX=e+t}get y(){return this.minY}set y(e){const t=this.maxY-this.minY;this.minY=e,this.maxY=e+t}get width(){return this.maxX-this.minX}set width(e){this.maxX=this.minX+e}get height(){return this.maxY-this.minY}set height(e){this.maxY=this.minY+e}get left(){return this.minX}get right(){return this.maxX}get top(){return this.minY}get bottom(){return this.maxY}get isPositive(){return this.maxX-this.minX>0&&this.maxY-this.minY>0}get isValid(){return this.minX+this.minY!==1/0}addVertexData(e,t,i,r){let n=this.minX,a=this.minY,o=this.maxX,h=this.maxY;r||(r=this.matrix);const l=r.a,c=r.b,d=r.c,u=r.d,f=r.tx,m=r.ty;for(let y=t;y<i;y+=2){const p=e[y],g=e[y+1],C=l*p+d*g+f,x=c*p+u*g+m;n=C<n?C:n,a=x<a?x:a,o=C>o?C:o,h=x>h?x:h}this.minX=n,this.minY=a,this.maxX=o,this.maxY=h}containsPoint(e,t){return this.minX<=e&&this.minY<=t&&this.maxX>=e&&this.maxY>=t}toString(){return`[pixi.js:Bounds minX=${this.minX} minY=${this.minY} maxX=${this.maxX} maxY=${this.maxY} width=${this.width} height=${this.height}]`}copyFrom(e){return this.minX=e.minX,this.minY=e.minY,this.maxX=e.maxX,this.maxY=e.maxY,this}}var cn={grad:.9,turn:360,rad:360/(2*Math.PI)},Ae=function(s){return typeof s=="string"?s.length>0:typeof s=="number"},j=function(s,e,t){return e===void 0&&(e=0),t===void 0&&(t=Math.pow(10,e)),Math.round(t*s)/t+0},he=function(s,e,t){return e===void 0&&(e=0),t===void 0&&(t=1),s>t?t:s>e?s:e},di=function(s){return(s=isFinite(s)?s%360:0)>0?s:s+360},Ss=function(s){return{r:he(s.r,0,255),g:he(s.g,0,255),b:he(s.b,0,255),a:he(s.a)}},Ut=function(s){return{r:j(s.r),g:j(s.g),b:j(s.b),a:j(s.a,3)}},dn=/^#([0-9a-f]{3,8})$/i,_t=function(s){var e=s.toString(16);return e.length<2?"0"+e:e},ui=function(s){var e=s.r,t=s.g,i=s.b,r=s.a,n=Math.max(e,t,i),a=n-Math.min(e,t,i),o=a?n===e?(t-i)/a:n===t?2+(i-e)/a:4+(e-t)/a:0;return{h:60*(o<0?o+6:o),s:n?a/n*100:0,v:n/255*100,a:r}},fi=function(s){var e=s.h,t=s.s,i=s.v,r=s.a;e=e/360*6,t/=100,i/=100;var n=Math.floor(e),a=i*(1-t),o=i*(1-(e-n)*t),h=i*(1-(1-e+n)*t),l=n%6;return{r:255*[i,o,a,a,h,i][l],g:255*[h,i,i,o,a,a][l],b:255*[a,a,h,i,i,o][l],a:r}},Es=function(s){return{h:di(s.h),s:he(s.s,0,100),l:he(s.l,0,100),a:he(s.a)}},Ps=function(s){return{h:j(s.h),s:j(s.s),l:j(s.l),a:j(s.a,3)}},Rs=function(s){return fi((t=(e=s).s,{h:e.h,s:(t*=((i=e.l)<50?i:100-i)/100)>0?2*t/(i+t)*100:0,v:i+t,a:e.a}));var e,t,i},it=function(s){return{h:(e=ui(s)).h,s:(r=(200-(t=e.s))*(i=e.v)/100)>0&&r<200?t*i/100/(r<=100?r:200-r)*100:0,l:r/2,a:e.a};var e,t,i,r},un=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s*,\s*([+-]?\d*\.?\d+)%\s*,\s*([+-]?\d*\.?\d+)%\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,fn=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s+([+-]?\d*\.?\d+)%\s+([+-]?\d*\.?\d+)%\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,pn=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,mn=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Qt={string:[[function(s){var e=dn.exec(s);return e?(s=e[1]).length<=4?{r:parseInt(s[0]+s[0],16),g:parseInt(s[1]+s[1],16),b:parseInt(s[2]+s[2],16),a:s.length===4?j(parseInt(s[3]+s[3],16)/255,2):1}:s.length===6||s.length===8?{r:parseInt(s.substr(0,2),16),g:parseInt(s.substr(2,2),16),b:parseInt(s.substr(4,2),16),a:s.length===8?j(parseInt(s.substr(6,2),16)/255,2):1}:null:null},"hex"],[function(s){var e=pn.exec(s)||mn.exec(s);return e?e[2]!==e[4]||e[4]!==e[6]?null:Ss({r:Number(e[1])/(e[2]?100/255:1),g:Number(e[3])/(e[4]?100/255:1),b:Number(e[5])/(e[6]?100/255:1),a:e[7]===void 0?1:Number(e[7])/(e[8]?100:1)}):null},"rgb"],[function(s){var e=un.exec(s)||fn.exec(s);if(!e)return null;var t,i,r=Es({h:(t=e[1],i=e[2],i===void 0&&(i="deg"),Number(t)*(cn[i]||1)),s:Number(e[3]),l:Number(e[4]),a:e[5]===void 0?1:Number(e[5])/(e[6]?100:1)});return Rs(r)},"hsl"]],object:[[function(s){var e=s.r,t=s.g,i=s.b,r=s.a,n=r===void 0?1:r;return Ae(e)&&Ae(t)&&Ae(i)?Ss({r:Number(e),g:Number(t),b:Number(i),a:Number(n)}):null},"rgb"],[function(s){var e=s.h,t=s.s,i=s.l,r=s.a,n=r===void 0?1:r;if(!Ae(e)||!Ae(t)||!Ae(i))return null;var a=Es({h:Number(e),s:Number(t),l:Number(i),a:Number(n)});return Rs(a)},"hsl"],[function(s){var e=s.h,t=s.s,i=s.v,r=s.a,n=r===void 0?1:r;if(!Ae(e)||!Ae(t)||!Ae(i))return null;var a=function(o){return{h:di(o.h),s:he(o.s,0,100),v:he(o.v,0,100),a:he(o.a)}}({h:Number(e),s:Number(t),v:Number(i),a:Number(n)});return fi(a)},"hsv"]]},Is=function(s,e){for(var t=0;t<e.length;t++){var i=e[t][0](s);if(i)return[i,e[t][1]]}return[null,void 0]},_n=function(s){return typeof s=="string"?Is(s.trim(),Qt.string):typeof s=="object"&&s!==null?Is(s,Qt.object):[null,void 0]},Nt=function(s,e){var t=it(s);return{h:t.h,s:he(t.s+100*e,0,100),l:t.l,a:t.a}},Yt=function(s){return(299*s.r+587*s.g+114*s.b)/1e3/255},Fs=function(s,e){var t=it(s);return{h:t.h,s:t.s,l:he(t.l+100*e,0,100),a:t.a}},Jt=function(){function s(e){this.parsed=_n(e)[0],this.rgba=this.parsed||{r:0,g:0,b:0,a:1}}return s.prototype.isValid=function(){return this.parsed!==null},s.prototype.brightness=function(){return j(Yt(this.rgba),2)},s.prototype.isDark=function(){return Yt(this.rgba)<.5},s.prototype.isLight=function(){return Yt(this.rgba)>=.5},s.prototype.toHex=function(){return e=Ut(this.rgba),t=e.r,i=e.g,r=e.b,a=(n=e.a)<1?_t(j(255*n)):"","#"+_t(t)+_t(i)+_t(r)+a;var e,t,i,r,n,a},s.prototype.toRgb=function(){return Ut(this.rgba)},s.prototype.toRgbString=function(){return e=Ut(this.rgba),t=e.r,i=e.g,r=e.b,(n=e.a)<1?"rgba("+t+", "+i+", "+r+", "+n+")":"rgb("+t+", "+i+", "+r+")";var e,t,i,r,n},s.prototype.toHsl=function(){return Ps(it(this.rgba))},s.prototype.toHslString=function(){return e=Ps(it(this.rgba)),t=e.h,i=e.s,r=e.l,(n=e.a)<1?"hsla("+t+", "+i+"%, "+r+"%, "+n+")":"hsl("+t+", "+i+"%, "+r+"%)";var e,t,i,r,n},s.prototype.toHsv=function(){return e=ui(this.rgba),{h:j(e.h),s:j(e.s),v:j(e.v),a:j(e.a,3)};var e},s.prototype.invert=function(){return ge({r:255-(e=this.rgba).r,g:255-e.g,b:255-e.b,a:e.a});var e},s.prototype.saturate=function(e){return e===void 0&&(e=.1),ge(Nt(this.rgba,e))},s.prototype.desaturate=function(e){return e===void 0&&(e=.1),ge(Nt(this.rgba,-e))},s.prototype.grayscale=function(){return ge(Nt(this.rgba,-1))},s.prototype.lighten=function(e){return e===void 0&&(e=.1),ge(Fs(this.rgba,e))},s.prototype.darken=function(e){return e===void 0&&(e=.1),ge(Fs(this.rgba,-e))},s.prototype.rotate=function(e){return e===void 0&&(e=15),this.hue(this.hue()+e)},s.prototype.alpha=function(e){return typeof e=="number"?ge({r:(t=this.rgba).r,g:t.g,b:t.b,a:e}):j(this.rgba.a,3);var t},s.prototype.hue=function(e){var t=it(this.rgba);return typeof e=="number"?ge({h:e,s:t.s,l:t.l,a:t.a}):j(t.h)},s.prototype.isEqual=function(e){return this.toHex()===ge(e).toHex()},s}(),ge=function(s){return s instanceof Jt?s:new Jt(s)},Bs=[],gn=function(s){s.forEach(function(e){Bs.indexOf(e)<0&&(e(Jt,Qt),Bs.push(e))})};function yn(s,e){var t={white:"#ffffff",bisque:"#ffe4c4",blue:"#0000ff",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",antiquewhite:"#faebd7",aqua:"#00ffff",azure:"#f0ffff",whitesmoke:"#f5f5f5",papayawhip:"#ffefd5",plum:"#dda0dd",blanchedalmond:"#ffebcd",black:"#000000",gold:"#ffd700",goldenrod:"#daa520",gainsboro:"#dcdcdc",cornsilk:"#fff8dc",cornflowerblue:"#6495ed",burlywood:"#deb887",aquamarine:"#7fffd4",beige:"#f5f5dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkkhaki:"#bdb76b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",peachpuff:"#ffdab9",darkmagenta:"#8b008b",darkred:"#8b0000",darkorchid:"#9932cc",darkorange:"#ff8c00",darkslateblue:"#483d8b",gray:"#808080",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",deeppink:"#ff1493",deepskyblue:"#00bfff",wheat:"#f5deb3",firebrick:"#b22222",floralwhite:"#fffaf0",ghostwhite:"#f8f8ff",darkviolet:"#9400d3",magenta:"#ff00ff",green:"#008000",dodgerblue:"#1e90ff",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",blueviolet:"#8a2be2",forestgreen:"#228b22",lawngreen:"#7cfc00",indianred:"#cd5c5c",indigo:"#4b0082",fuchsia:"#ff00ff",brown:"#a52a2a",maroon:"#800000",mediumblue:"#0000cd",lightcoral:"#f08080",darkturquoise:"#00ced1",lightcyan:"#e0ffff",ivory:"#fffff0",lightyellow:"#ffffe0",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",linen:"#faf0e6",mediumaquamarine:"#66cdaa",lemonchiffon:"#fffacd",lime:"#00ff00",khaki:"#f0e68c",mediumseagreen:"#3cb371",limegreen:"#32cd32",mediumspringgreen:"#00fa9a",lightskyblue:"#87cefa",lightblue:"#add8e6",midnightblue:"#191970",lightpink:"#ffb6c1",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",mintcream:"#f5fffa",lightslategray:"#778899",lightslategrey:"#778899",navajowhite:"#ffdead",navy:"#000080",mediumvioletred:"#c71585",powderblue:"#b0e0e6",palegoldenrod:"#eee8aa",oldlace:"#fdf5e6",paleturquoise:"#afeeee",mediumturquoise:"#48d1cc",mediumorchid:"#ba55d3",rebeccapurple:"#663399",lightsteelblue:"#b0c4de",mediumslateblue:"#7b68ee",thistle:"#d8bfd8",tan:"#d2b48c",orchid:"#da70d6",mediumpurple:"#9370db",purple:"#800080",pink:"#ffc0cb",skyblue:"#87ceeb",springgreen:"#00ff7f",palegreen:"#98fb98",red:"#ff0000",yellow:"#ffff00",slateblue:"#6a5acd",lavenderblush:"#fff0f5",peru:"#cd853f",palevioletred:"#db7093",violet:"#ee82ee",teal:"#008080",slategray:"#708090",slategrey:"#708090",aliceblue:"#f0f8ff",darkseagreen:"#8fbc8f",darkolivegreen:"#556b2f",greenyellow:"#adff2f",seagreen:"#2e8b57",seashell:"#fff5ee",tomato:"#ff6347",silver:"#c0c0c0",sienna:"#a0522d",lavender:"#e6e6fa",lightgreen:"#90ee90",orange:"#ffa500",orangered:"#ff4500",steelblue:"#4682b4",royalblue:"#4169e1",turquoise:"#40e0d0",yellowgreen:"#9acd32",salmon:"#fa8072",saddlebrown:"#8b4513",sandybrown:"#f4a460",rosybrown:"#bc8f8f",darksalmon:"#e9967a",lightgoldenrodyellow:"#fafad2",snow:"#fffafa",lightgrey:"#d3d3d3",lightgray:"#d3d3d3",dimgray:"#696969",dimgrey:"#696969",olivedrab:"#6b8e23",olive:"#808000"},i={};for(var r in t)i[t[r]]=r;var n={};s.prototype.toName=function(a){if(!(this.rgba.a||this.rgba.r||this.rgba.g||this.rgba.b))return"transparent";var o,h,l=i[this.toHex()];if(l)return l;if(a!=null&&a.closest){var c=this.toRgb(),d=1/0,u="black";if(!n.length)for(var f in t)n[f]=new s(t[f]).toRgb();for(var m in t){var y=(o=c,h=n[m],Math.pow(o.r-h.r,2)+Math.pow(o.g-h.g,2)+Math.pow(o.b-h.b,2));y<d&&(d=y,u=m)}return u}},e.string.push([function(a){var o=a.toLowerCase(),h=o==="transparent"?"#0000":t[o];return h?new s(h).toRgb():null},"name"])}gn([yn]);const je=class Ze{constructor(e=16777215){this._value=null,this._components=new Float32Array(4),this._components.fill(1),this._int=16777215,this.value=e}get red(){return this._components[0]}get green(){return this._components[1]}get blue(){return this._components[2]}get alpha(){return this._components[3]}setValue(e){return this.value=e,this}set value(e){if(e instanceof Ze)this._value=this._cloneSource(e._value),this._int=e._int,this._components.set(e._components);else{if(e===null)throw new Error("Cannot set Color#value to null");(this._value===null||!this._isSourceEqual(this._value,e))&&(this._value=this._cloneSource(e),this._normalize(this._value))}}get value(){return this._value}_cloneSource(e){return typeof e=="string"||typeof e=="number"||e instanceof Number||e===null?e:Array.isArray(e)||ArrayBuffer.isView(e)?e.slice(0):typeof e=="object"&&e!==null?{...e}:e}_isSourceEqual(e,t){const i=typeof e;if(i!==typeof t)return!1;if(i==="number"||i==="string"||e instanceof Number)return e===t;if(Array.isArray(e)&&Array.isArray(t)||ArrayBuffer.isView(e)&&ArrayBuffer.isView(t))return e.length!==t.length?!1:e.every((n,a)=>n===t[a]);if(e!==null&&t!==null){const n=Object.keys(e),a=Object.keys(t);return n.length!==a.length?!1:n.every(o=>e[o]===t[o])}return e===t}toRgba(){const[e,t,i,r]=this._components;return{r:e,g:t,b:i,a:r}}toRgb(){const[e,t,i]=this._components;return{r:e,g:t,b:i}}toRgbaString(){const[e,t,i]=this.toUint8RgbArray();return`rgba(${e},${t},${i},${this.alpha})`}toUint8RgbArray(e){const[t,i,r]=this._components;return this._arrayRgb||(this._arrayRgb=[]),e||(e=this._arrayRgb),e[0]=Math.round(t*255),e[1]=Math.round(i*255),e[2]=Math.round(r*255),e}toArray(e){this._arrayRgba||(this._arrayRgba=[]),e||(e=this._arrayRgba);const[t,i,r,n]=this._components;return e[0]=t,e[1]=i,e[2]=r,e[3]=n,e}toRgbArray(e){this._arrayRgb||(this._arrayRgb=[]),e||(e=this._arrayRgb);const[t,i,r]=this._components;return e[0]=t,e[1]=i,e[2]=r,e}toNumber(){return this._int}toBgrNumber(){const[e,t,i]=this.toUint8RgbArray();return(i<<16)+(t<<8)+e}toLittleEndianNumber(){const e=this._int;return(e>>16)+(e&65280)+((e&255)<<16)}multiply(e){const[t,i,r,n]=Ze._temp.setValue(e)._components;return this._components[0]*=t,this._components[1]*=i,this._components[2]*=r,this._components[3]*=n,this._refreshInt(),this._value=null,this}premultiply(e,t=!0){return t&&(this._components[0]*=e,this._components[1]*=e,this._components[2]*=e),this._components[3]=e,this._refreshInt(),this._value=null,this}toPremultiplied(e,t=!0){if(e===1)return(255<<24)+this._int;if(e===0)return t?0:this._int;let i=this._int>>16&255,r=this._int>>8&255,n=this._int&255;return t&&(i=i*e+.5|0,r=r*e+.5|0,n=n*e+.5|0),(e*255<<24)+(i<<16)+(r<<8)+n}toHex(){const e=this._int.toString(16);return`#${"000000".substring(0,6-e.length)+e}`}toHexa(){const t=Math.round(this._components[3]*255).toString(16);return this.toHex()+"00".substring(0,2-t.length)+t}setAlpha(e){return this._components[3]=this._clamp(e),this}_normalize(e){let t,i,r,n;if((typeof e=="number"||e instanceof Number)&&e>=0&&e<=16777215){const a=e;t=(a>>16&255)/255,i=(a>>8&255)/255,r=(a&255)/255,n=1}else if((Array.isArray(e)||e instanceof Float32Array)&&e.length>=3&&e.length<=4)e=this._clamp(e),[t,i,r,n=1]=e;else if((e instanceof Uint8Array||e instanceof Uint8ClampedArray)&&e.length>=3&&e.length<=4)e=this._clamp(e,0,255),[t,i,r,n=255]=e,t/=255,i/=255,r/=255,n/=255;else if(typeof e=="string"||typeof e=="object"){if(typeof e=="string"){const o=Ze.HEX_PATTERN.exec(e);o&&(e=`#${o[2]}`)}const a=ge(e);a.isValid()&&({r:t,g:i,b:r,a:n}=a.rgba,t/=255,i/=255,r/=255)}if(t!==void 0)this._components[0]=t,this._components[1]=i,this._components[2]=r,this._components[3]=n,this._refreshInt();else throw new Error(`Unable to convert color ${e}`)}_refreshInt(){this._clamp(this._components);const[e,t,i]=this._components;this._int=(e*255<<16)+(t*255<<8)+(i*255|0)}_clamp(e,t=0,i=1){return typeof e=="number"?Math.min(Math.max(e,t),i):(e.forEach((r,n)=>{e[n]=Math.min(Math.max(r,t),i)}),e)}static isColorLike(e){return typeof e=="number"||typeof e=="string"||e instanceof Number||e instanceof Ze||Array.isArray(e)||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Float32Array||e.r!==void 0&&e.g!==void 0&&e.b!==void 0||e.r!==void 0&&e.g!==void 0&&e.b!==void 0&&e.a!==void 0||e.h!==void 0&&e.s!==void 0&&e.l!==void 0||e.h!==void 0&&e.s!==void 0&&e.l!==void 0&&e.a!==void 0||e.h!==void 0&&e.s!==void 0&&e.v!==void 0||e.h!==void 0&&e.s!==void 0&&e.v!==void 0&&e.a!==void 0}};je.shared=new je;je._temp=new je;je.HEX_PATTERN=/^(#|0x)?(([a-f0-9]{3}){1,2}([a-f0-9]{2})?)$/i;let wt=je;const vn={cullArea:null,cullable:!1,cullableChildren:!0};let Xt=0;const Ds=500;function De(...s){Xt!==Ds&&(Xt++,Xt===Ds?console.warn("PixiJS Warning: too many warnings, no more warnings will be reported to the console by PixiJS."):console.warn("PixiJS Warning: ",...s))}class ls{constructor(e,t){this._pool=[],this._count=0,this._index=0,this._classType=e,t&&this.prepopulate(t)}prepopulate(e){for(let t=0;t<e;t++)this._pool[this._index++]=new this._classType;this._count+=e}get(e){var i;let t;return this._index>0?t=this._pool[--this._index]:t=new this._classType,(i=t.init)==null||i.call(t,e),t}return(e){var t;(t=e.reset)==null||t.call(e),this._pool[this._index++]=e}get totalSize(){return this._count}get totalFree(){return this._index}get totalUsed(){return this._count-this._index}clear(){this._pool.length=0,this._index=0}}class xn{constructor(){this._poolsByClass=new Map}prepopulate(e,t){this.getPool(e).prepopulate(t)}get(e,t){return this.getPool(e).get(t)}return(e){this.getPool(e.constructor).return(e)}getPool(e){return this._poolsByClass.has(e)||this._poolsByClass.set(e,new ls(e)),this._poolsByClass.get(e)}stats(){const e={};return this._poolsByClass.forEach(t=>{const i=e[t._classType.name]?t._classType.name+t._classType.ID:t._classType.name;e[i]={free:t.totalFree,used:t.totalUsed,size:t.totalSize}}),e}}const Tt=new xn,bn={get isCachedAsTexture(){var s;return!!((s=this.renderGroup)!=null&&s.isCachedAsTexture)},cacheAsTexture(s){typeof s=="boolean"&&s===!1?this.disableRenderGroup():(this.enableRenderGroup(),this.renderGroup.enableCacheAsTexture(s===!0?{}:s))},updateCacheTexture(){var s;(s=this.renderGroup)==null||s.updateCacheTexture()},get cacheAsBitmap(){return this.isCachedAsTexture},set cacheAsBitmap(s){le("v8.6.0","cacheAsBitmap is deprecated, use cacheAsTexture instead."),this.cacheAsTexture(s)}};function wn(s,e,t){const i=s.length;let r;if(e>=i||t===0)return;t=e+t>i?i-e:t;const n=i-t;for(r=e;r<n;++r)s[r]=s[r+t];s.length=n}const An={allowChildren:!0,removeChildren(s=0,e){var n;const t=e??this.children.length,i=t-s,r=[];if(i>0&&i<=t){for(let o=t-1;o>=s;o--){const h=this.children[o];h&&(r.push(h),h.parent=null)}wn(this.children,s,t);const a=this.renderGroup||this.parentRenderGroup;a&&a.removeChildren(r);for(let o=0;o<r.length;++o){const h=r[o];(n=h.parentRenderLayer)==null||n.detach(h),this.emit("childRemoved",h,this,o),r[o].emit("removed",this)}return r.length>0&&this._didViewChangeTick++,r}else if(i===0&&this.children.length===0)return r;throw new RangeError("removeChildren: numeric values are outside the acceptable range.")},removeChildAt(s){const e=this.getChildAt(s);return this.removeChild(e)},getChildAt(s){if(s<0||s>=this.children.length)throw new Error(`getChildAt: Index (${s}) does not exist.`);return this.children[s]},setChildIndex(s,e){if(e<0||e>=this.children.length)throw new Error(`The index ${e} supplied is out of bounds ${this.children.length}`);this.getChildIndex(s),this.addChildAt(s,e)},getChildIndex(s){const e=this.children.indexOf(s);if(e===-1)throw new Error("The supplied Container must be a child of the caller");return e},addChildAt(s,e){this.allowChildren||le(Ee,"addChildAt: Only Containers will be allowed to add children in v8.0.0");const{children:t}=this;if(e<0||e>t.length)throw new Error(`${s}addChildAt: The index ${e} supplied is out of bounds ${t.length}`);if(s.parent){const r=s.parent.children.indexOf(s);if(s.parent===this&&r===e)return s;r!==-1&&s.parent.children.splice(r,1)}e===t.length?t.push(s):t.splice(e,0,s),s.parent=this,s.didChange=!0,s._updateFlags=15;const i=this.renderGroup||this.parentRenderGroup;return i&&i.addChild(s),this.sortableChildren&&(this.sortDirty=!0),this.emit("childAdded",s,this,e),s.emit("added",this),s},swapChildren(s,e){if(s===e)return;const t=this.getChildIndex(s),i=this.getChildIndex(e);this.children[t]=e,this.children[i]=s;const r=this.renderGroup||this.parentRenderGroup;r&&(r.structureDidChange=!0),this._didContainerChangeTick++},removeFromParent(){var s;(s=this.parent)==null||s.removeChild(this)},reparentChild(...s){return s.length===1?this.reparentChildAt(s[0],this.children.length):(s.forEach(e=>this.reparentChildAt(e,this.children.length)),s[0])},reparentChildAt(s,e){if(s.parent===this)return this.setChildIndex(s,e),s;const t=s.worldTransform.clone();s.removeFromParent(),this.addChildAt(s,e);const i=this.worldTransform.clone();return i.invert(),t.prepend(i),s.setFromMatrix(t),s},replaceChild(s,e){s.updateLocalTransform(),this.addChildAt(e,this.getChildIndex(s)),e.setFromMatrix(s.localTransform),e.updateLocalTransform(),this.removeChild(s)}},kn={collectRenderables(s,e,t){this.parentRenderLayer&&this.parentRenderLayer!==t||this.globalDisplayStatus<7||!this.includeInBuild||(this.sortableChildren&&this.sortChildren(),this.isSimple?this.collectRenderablesSimple(s,e,t):this.renderGroup?e.renderPipes.renderGroup.addRenderGroup(this.renderGroup,s):this.collectRenderablesWithEffects(s,e,t))},collectRenderablesSimple(s,e,t){const i=this.children,r=i.length;for(let n=0;n<r;n++)i[n].collectRenderables(s,e,t)},collectRenderablesWithEffects(s,e,t){const{renderPipes:i}=e;for(let r=0;r<this.effects.length;r++){const n=this.effects[r];i[n.pipe].push(n,this,s)}this.collectRenderablesSimple(s,e,t);for(let r=this.effects.length-1;r>=0;r--){const n=this.effects[r];i[n.pipe].pop(n,this,s)}}};class Gs{constructor(){this.pipe="filter",this.priority=1}destroy(){for(let e=0;e<this.filters.length;e++)this.filters[e].destroy();this.filters=null,this.filterArea=null}}class Cn{constructor(){this._effectClasses=[],this._tests=[],this._initialized=!1}init(){this._initialized||(this._initialized=!0,this._effectClasses.forEach(e=>{this.add({test:e.test,maskClass:e})}))}add(e){this._tests.push(e)}getMaskEffect(e){this._initialized||this.init();for(let t=0;t<this._tests.length;t++){const i=this._tests[t];if(i.test(e))return Tt.get(i.maskClass,e)}return e}returnMaskEffect(e){Tt.return(e)}}const Zt=new Cn;xe.handleByList(Y.MaskEffect,Zt._effectClasses);const Mn={_maskEffect:null,_maskOptions:{inverse:!1},_filterEffect:null,effects:[],_markStructureAsChanged(){const s=this.renderGroup||this.parentRenderGroup;s&&(s.structureDidChange=!0)},addEffect(s){this.effects.indexOf(s)===-1&&(this.effects.push(s),this.effects.sort((t,i)=>t.priority-i.priority),this._markStructureAsChanged(),this._updateIsSimple())},removeEffect(s){const e=this.effects.indexOf(s);e!==-1&&(this.effects.splice(e,1),this._markStructureAsChanged(),this._updateIsSimple())},set mask(s){const e=this._maskEffect;(e==null?void 0:e.mask)!==s&&(e&&(this.removeEffect(e),Zt.returnMaskEffect(e),this._maskEffect=null),s!=null&&(this._maskEffect=Zt.getMaskEffect(s),this.addEffect(this._maskEffect)))},get mask(){var s;return(s=this._maskEffect)==null?void 0:s.mask},setMask(s){this._maskOptions={...this._maskOptions,...s},s.mask&&(this.mask=s.mask),this._markStructureAsChanged()},set filters(s){var n;!Array.isArray(s)&&s&&(s=[s]);const e=this._filterEffect||(this._filterEffect=new Gs);s=s;const t=(s==null?void 0:s.length)>0,i=((n=e.filters)==null?void 0:n.length)>0,r=t!==i;s=Array.isArray(s)?s.slice(0):s,e.filters=Object.freeze(s),r&&(t?this.addEffect(e):(this.removeEffect(e),e.filters=s??null))},get filters(){var s;return(s=this._filterEffect)==null?void 0:s.filters},set filterArea(s){this._filterEffect||(this._filterEffect=new Gs),this._filterEffect.filterArea=s},get filterArea(){var s;return(s=this._filterEffect)==null?void 0:s.filterArea}},Tn={label:null,get name(){return le(Ee,"Container.name property has been removed, use Container.label instead"),this.label},set name(s){le(Ee,"Container.name property has been removed, use Container.label instead"),this.label=s},getChildByName(s,e=!1){return this.getChildByLabel(s,e)},getChildByLabel(s,e=!1){const t=this.children;for(let i=0;i<t.length;i++){const r=t[i];if(r.label===s||s instanceof RegExp&&s.test(r.label))return r}if(e)for(let i=0;i<t.length;i++){const n=t[i].getChildByLabel(s,!0);if(n)return n}return null},getChildrenByLabel(s,e=!1,t=[]){const i=this.children;for(let r=0;r<i.length;r++){const n=i[r];(n.label===s||s instanceof RegExp&&s.test(n.label))&&t.push(n)}if(e)for(let r=0;r<i.length;r++)i[r].getChildrenByLabel(s,!0,t);return t}},J=new ls(D),Se=new ls(Ge),Sn=new D,En={getFastGlobalBounds(s,e){e||(e=new Ge),e.clear(),this._getGlobalBoundsRecursive(!!s,e,this.parentRenderLayer),e.isValid||e.set(0,0,0,0);const t=this.renderGroup||this.parentRenderGroup;return e.applyMatrix(t.worldTransform),e},_getGlobalBoundsRecursive(s,e,t){let i=e;if(s&&this.parentRenderLayer&&this.parentRenderLayer!==t||this.localDisplayStatus!==7||!this.measurable)return;const r=!!this.effects.length;if((this.renderGroup||r)&&(i=Se.get().clear()),this.boundsArea)e.addRect(this.boundsArea,this.worldTransform);else{if(this.renderPipeId){const a=this.bounds;i.addFrame(a.minX,a.minY,a.maxX,a.maxY,this.groupTransform)}const n=this.children;for(let a=0;a<n.length;a++)n[a]._getGlobalBoundsRecursive(s,i,t)}if(r){let n=!1;const a=this.renderGroup||this.parentRenderGroup;for(let o=0;o<this.effects.length;o++)this.effects[o].addBounds&&(n||(n=!0,i.applyMatrix(a.worldTransform)),this.effects[o].addBounds(i,!0));n&&(i.applyMatrix(a.worldTransform.copyTo(Sn).invert()),e.addBounds(i,this.relativeGroupTransform)),e.addBounds(i),Se.return(i)}else this.renderGroup&&(e.addBounds(i,this.relativeGroupTransform),Se.return(i))}};function pi(s,e,t){t.clear();let i,r;return s.parent?e?i=s.parent.worldTransform:(r=J.get().identity(),i=cs(s,r)):i=D.IDENTITY,mi(s,t,i,e),r&&J.return(r),t.isValid||t.set(0,0,0,0),t}function mi(s,e,t,i){var o,h;if(!s.visible||!s.measurable)return;let r;i?r=s.worldTransform:(s.updateLocalTransform(),r=J.get(),r.appendFrom(s.localTransform,t));const n=e,a=!!s.effects.length;if(a&&(e=Se.get().clear()),s.boundsArea)e.addRect(s.boundsArea,r);else{s.bounds&&(e.matrix=r,e.addBounds(s.bounds));for(let l=0;l<s.children.length;l++)mi(s.children[l],e,r,i)}if(a){for(let l=0;l<s.effects.length;l++)(h=(o=s.effects[l]).addBounds)==null||h.call(o,e);n.addBounds(e,D.IDENTITY),Se.return(e)}i||J.return(r)}function cs(s,e){const t=s.parent;return t&&(cs(t,e),t.updateLocalTransform(),e.append(t.localTransform)),e}function Pn(s,e){if(s===16777215||!e)return e;if(e===16777215||!s)return s;const t=s>>16&255,i=s>>8&255,r=s&255,n=e>>16&255,a=e>>8&255,o=e&255,h=t*n/255|0,l=i*a/255|0,c=r*o/255|0;return(h<<16)+(l<<8)+c}const Os=16777215;function Ls(s,e){return s===Os?e:e===Os?s:Pn(s,e)}function At(s){return((s&255)<<16)+(s&65280)+(s>>16&255)}const Rn={getGlobalAlpha(s){if(s)return this.renderGroup?this.renderGroup.worldAlpha:this.parentRenderGroup?this.parentRenderGroup.worldAlpha*this.alpha:this.alpha;let e=this.alpha,t=this.parent;for(;t;)e*=t.alpha,t=t.parent;return e},getGlobalTransform(s,e){if(e)return s.copyFrom(this.worldTransform);this.updateLocalTransform();const t=cs(this,J.get().identity());return s.appendFrom(this.localTransform,t),J.return(t),s},getGlobalTint(s){if(s)return this.renderGroup?At(this.renderGroup.worldColor):this.parentRenderGroup?At(Ls(this.localColor,this.parentRenderGroup.worldColor)):this.tint;let e=this.localColor,t=this.parent;for(;t;)e=Ls(e,t.localColor),t=t.parent;return At(e)}};function _i(s,e,t){return e.clear(),t||(t=D.IDENTITY),gi(s,e,t,s,!0),e.isValid||e.set(0,0,0,0),e}function gi(s,e,t,i,r){var h,l;let n;if(r)n=J.get(),n=t.copyTo(n);else{if(!s.visible||!s.measurable)return;s.updateLocalTransform();const c=s.localTransform;n=J.get(),n.appendFrom(c,t)}const a=e,o=!!s.effects.length;if(o&&(e=Se.get().clear()),s.boundsArea)e.addRect(s.boundsArea,n);else{s.renderPipeId&&(e.matrix=n,e.addBounds(s.bounds));const c=s.children;for(let d=0;d<c.length;d++)gi(c[d],e,n,i,!1)}if(o){for(let c=0;c<s.effects.length;c++)(l=(h=s.effects[c]).addLocalBounds)==null||l.call(h,e,i);a.addBounds(e,D.IDENTITY),Se.return(e)}J.return(n)}function yi(s,e){const t=s.children;for(let i=0;i<t.length;i++){const r=t[i],n=r.uid,a=(r._didViewChangeTick&65535)<<16|r._didContainerChangeTick&65535,o=e.index;(e.data[o]!==n||e.data[o+1]!==a)&&(e.data[e.index]=n,e.data[e.index+1]=a,e.didChange=!0),e.index=o+2,r.children.length&&yi(r,e)}return e.didChange}const In=new D,Fn={_localBoundsCacheId:-1,_localBoundsCacheData:null,_setWidth(s,e){const t=Math.sign(this.scale.x)||1;e!==0?this.scale.x=s/e*t:this.scale.x=t},_setHeight(s,e){const t=Math.sign(this.scale.y)||1;e!==0?this.scale.y=s/e*t:this.scale.y=t},getLocalBounds(){this._localBoundsCacheData||(this._localBoundsCacheData={data:[],index:1,didChange:!1,localBounds:new Ge});const s=this._localBoundsCacheData;return s.index=1,s.didChange=!1,s.data[0]!==this._didViewChangeTick&&(s.didChange=!0,s.data[0]=this._didViewChangeTick),yi(this,s),s.didChange&&_i(this,s.localBounds,In),s.localBounds},getBounds(s,e){return pi(this,s,e||new Ge)}},Bn={_onRender:null,set onRender(s){const e=this.renderGroup||this.parentRenderGroup;if(!s){this._onRender&&(e==null||e.removeOnRender(this)),this._onRender=null;return}this._onRender||e==null||e.addOnRender(this),this._onRender=s},get onRender(){return this._onRender}},Dn={_zIndex:0,sortDirty:!1,sortableChildren:!1,get zIndex(){return this._zIndex},set zIndex(s){this._zIndex!==s&&(this._zIndex=s,this.depthOfChildModified())},depthOfChildModified(){this.parent&&(this.parent.sortableChildren=!0,this.parent.sortDirty=!0),this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0)},sortChildren(){this.sortDirty&&(this.sortDirty=!1,this.children.sort(Gn))}};function Gn(s,e){return s._zIndex-e._zIndex}const On={getGlobalPosition(s=new ye,e=!1){return this.parent?this.parent.toGlobal(this._position,s,e):(s.x=this._position.x,s.y=this._position.y),s},toGlobal(s,e,t=!1){const i=this.getGlobalTransform(J.get(),t);return e=i.apply(s,e),J.return(i),e},toLocal(s,e,t,i){e&&(s=e.toGlobal(s,t,i));const r=this.getGlobalTransform(J.get(),i);return t=r.applyInverse(s,t),J.return(r),t}};class Ln{constructor(){this.uid=Ce("instructionSet"),this.instructions=[],this.instructionSize=0,this.renderables=[],this.gcTick=0}reset(){this.instructionSize=0}add(e){this.instructions[this.instructionSize++]=e}log(){this.instructions.length=this.instructionSize,console.table(this.instructions,["type","action"])}}let Un=0;class Nn{constructor(e){this._poolKeyHash=Object.create(null),this._texturePool={},this.textureOptions=e||{},this.enableFullScreen=!1,this.textureStyle=new hi(this.textureOptions)}createTexture(e,t,i){const r=new pe({...this.textureOptions,width:e,height:t,resolution:1,antialias:i,autoGarbageCollect:!1});return new B({source:r,label:`texturePool_${Un++}`})}getOptimalTexture(e,t,i=1,r){let n=Math.ceil(e*i-1e-6),a=Math.ceil(t*i-1e-6);n=As(n),a=As(a);const o=(n<<17)+(a<<1)+(r?1:0);this._texturePool[o]||(this._texturePool[o]=[]);let h=this._texturePool[o].pop();return h||(h=this.createTexture(n,a,r)),h.source._resolution=i,h.source.width=n/i,h.source.height=a/i,h.source.pixelWidth=n,h.source.pixelHeight=a,h.frame.x=0,h.frame.y=0,h.frame.width=e,h.frame.height=t,h.updateUvs(),this._poolKeyHash[h.uid]=o,h}getSameSizeTexture(e,t=!1){const i=e.source;return this.getOptimalTexture(e.width,e.height,i._resolution,t)}returnTexture(e,t=!1){const i=this._poolKeyHash[e.uid];t&&(e.source.style=this.textureStyle),this._texturePool[i].push(e)}clear(e){if(e=e!==!1,e)for(const t in this._texturePool){const i=this._texturePool[t];if(i)for(let r=0;r<i.length;r++)i[r].destroy(!0)}this._texturePool={}}}const Yn=new Nn;class Xn{constructor(){this.renderPipeId="renderGroup",this.root=null,this.canBundle=!1,this.renderGroupParent=null,this.renderGroupChildren=[],this.worldTransform=new D,this.worldColorAlpha=4294967295,this.worldColor=16777215,this.worldAlpha=1,this.childrenToUpdate=Object.create(null),this.updateTick=0,this.gcTick=0,this.childrenRenderablesToUpdate={list:[],index:0},this.structureDidChange=!0,this.instructionSet=new Ln,this._onRenderContainers=[],this.textureNeedsUpdate=!0,this.isCachedAsTexture=!1,this._matrixDirty=7}init(e){this.root=e,e._onRender&&this.addOnRender(e),e.didChange=!0;const t=e.children;for(let i=0;i<t.length;i++){const r=t[i];r._updateFlags=15,this.addChild(r)}}enableCacheAsTexture(e={}){this.textureOptions=e,this.isCachedAsTexture=!0,this.textureNeedsUpdate=!0}disableCacheAsTexture(){this.isCachedAsTexture=!1,this.texture&&(Yn.returnTexture(this.texture),this.texture=null)}updateCacheTexture(){this.textureNeedsUpdate=!0}reset(){this.renderGroupChildren.length=0;for(const e in this.childrenToUpdate){const t=this.childrenToUpdate[e];t.list.fill(null),t.index=0}this.childrenRenderablesToUpdate.index=0,this.childrenRenderablesToUpdate.list.fill(null),this.root=null,this.updateTick=0,this.structureDidChange=!0,this._onRenderContainers.length=0,this.renderGroupParent=null,this.disableCacheAsTexture()}get localTransform(){return this.root.localTransform}addRenderGroupChild(e){e.renderGroupParent&&e.renderGroupParent._removeRenderGroupChild(e),e.renderGroupParent=this,this.renderGroupChildren.push(e)}_removeRenderGroupChild(e){const t=this.renderGroupChildren.indexOf(e);t>-1&&this.renderGroupChildren.splice(t,1),e.renderGroupParent=null}addChild(e){if(this.structureDidChange=!0,e.parentRenderGroup=this,e.updateTick=-1,e.parent===this.root?e.relativeRenderGroupDepth=1:e.relativeRenderGroupDepth=e.parent.relativeRenderGroupDepth+1,e.didChange=!0,this.onChildUpdate(e),e.renderGroup){this.addRenderGroupChild(e.renderGroup);return}e._onRender&&this.addOnRender(e);const t=e.children;for(let i=0;i<t.length;i++)this.addChild(t[i])}removeChild(e){if(this.structureDidChange=!0,e._onRender&&(e.renderGroup||this.removeOnRender(e)),e.parentRenderGroup=null,e.renderGroup){this._removeRenderGroupChild(e.renderGroup);return}const t=e.children;for(let i=0;i<t.length;i++)this.removeChild(t[i])}removeChildren(e){for(let t=0;t<e.length;t++)this.removeChild(e[t])}onChildUpdate(e){let t=this.childrenToUpdate[e.relativeRenderGroupDepth];t||(t=this.childrenToUpdate[e.relativeRenderGroupDepth]={index:0,list:[]}),t.list[t.index++]=e}updateRenderable(e){e.globalDisplayStatus<7||(this.instructionSet.renderPipes[e.renderPipeId].updateRenderable(e),e.didViewUpdate=!1)}onChildViewUpdate(e){this.childrenRenderablesToUpdate.list[this.childrenRenderablesToUpdate.index++]=e}get isRenderable(){return this.root.localDisplayStatus===7&&this.worldAlpha>0}addOnRender(e){this._onRenderContainers.push(e)}removeOnRender(e){this._onRenderContainers.splice(this._onRenderContainers.indexOf(e),1)}runOnRender(e){for(let t=0;t<this._onRenderContainers.length;t++)this._onRenderContainers[t]._onRender(e)}destroy(){this.disableCacheAsTexture(),this.renderGroupParent=null,this.root=null,this.childrenRenderablesToUpdate=null,this.childrenToUpdate=null,this.renderGroupChildren=null,this._onRenderContainers=null,this.instructionSet=null}getChildren(e=[]){const t=this.root.children;for(let i=0;i<t.length;i++)this._getChildren(t[i],e);return e}_getChildren(e,t=[]){if(t.push(e),e.renderGroup)return t;const i=e.children;for(let r=0;r<i.length;r++)this._getChildren(i[r],t);return t}invalidateMatrices(){this._matrixDirty=7}get inverseWorldTransform(){return(this._matrixDirty&1)===0?this._inverseWorldTransform:(this._matrixDirty&=-2,this._inverseWorldTransform||(this._inverseWorldTransform=new D),this._inverseWorldTransform.copyFrom(this.worldTransform).invert())}get textureOffsetInverseTransform(){return(this._matrixDirty&2)===0?this._textureOffsetInverseTransform:(this._matrixDirty&=-3,this._textureOffsetInverseTransform||(this._textureOffsetInverseTransform=new D),this._textureOffsetInverseTransform.copyFrom(this.inverseWorldTransform).translate(-this._textureBounds.x,-this._textureBounds.y))}get inverseParentTextureTransform(){if((this._matrixDirty&4)===0)return this._inverseParentTextureTransform;this._matrixDirty&=-5;const e=this._parentCacheAsTextureRenderGroup;return e?(this._inverseParentTextureTransform||(this._inverseParentTextureTransform=new D),this._inverseParentTextureTransform.copyFrom(this.worldTransform).prepend(e.inverseWorldTransform).translate(-e._textureBounds.x,-e._textureBounds.y)):this.worldTransform}get cacheToLocalTransform(){return this._parentCacheAsTextureRenderGroup?this._parentCacheAsTextureRenderGroup.textureOffsetInverseTransform:null}}function Hn(s,e,t={}){for(const i in e)!t[i]&&e[i]!==void 0&&(s[i]=e[i])}const Ht=new Q(null),gt=new Q(null),qt=new Q(null,1,1),yt=new Q(null),Us=1,qn=2,$t=4;class Oe extends ut{constructor(e={}){var t,i;super(),this.uid=Ce("renderable"),this._updateFlags=15,this.renderGroup=null,this.parentRenderGroup=null,this.parentRenderGroupIndex=0,this.didChange=!1,this.didViewUpdate=!1,this.relativeRenderGroupDepth=0,this.children=[],this.parent=null,this.includeInBuild=!0,this.measurable=!0,this.isSimple=!0,this.updateTick=-1,this.localTransform=new D,this.relativeGroupTransform=new D,this.groupTransform=this.relativeGroupTransform,this.destroyed=!1,this._position=new Q(this,0,0),this._scale=qt,this._pivot=gt,this._origin=yt,this._skew=Ht,this._cx=1,this._sx=0,this._cy=0,this._sy=1,this._rotation=0,this.localColor=16777215,this.localAlpha=1,this.groupAlpha=1,this.groupColor=16777215,this.groupColorAlpha=4294967295,this.localBlendMode="inherit",this.groupBlendMode="normal",this.localDisplayStatus=7,this.globalDisplayStatus=7,this._didContainerChangeTick=0,this._didViewChangeTick=0,this._didLocalTransformChangeId=-1,this.effects=[],Hn(this,e,{children:!0,parent:!0,effects:!0}),(t=e.children)==null||t.forEach(r=>this.addChild(r)),(i=e.parent)==null||i.addChild(this)}static mixin(e){le("8.8.0","Container.mixin is deprecated, please use extensions.mixin instead."),xe.mixin(Oe,e)}set _didChangeId(e){this._didViewChangeTick=e>>12&4095,this._didContainerChangeTick=e&4095}get _didChangeId(){return this._didContainerChangeTick&4095|(this._didViewChangeTick&4095)<<12}addChild(...e){if(this.allowChildren||le(Ee,"addChild: Only Containers will be allowed to add children in v8.0.0"),e.length>1){for(let r=0;r<e.length;r++)this.addChild(e[r]);return e[0]}const t=e[0],i=this.renderGroup||this.parentRenderGroup;return t.parent===this?(this.children.splice(this.children.indexOf(t),1),this.children.push(t),i&&(i.structureDidChange=!0),t):(t.parent&&t.parent.removeChild(t),this.children.push(t),this.sortableChildren&&(this.sortDirty=!0),t.parent=this,t.didChange=!0,t._updateFlags=15,i&&i.addChild(t),this.emit("childAdded",t,this,this.children.length-1),t.emit("added",this),this._didViewChangeTick++,t._zIndex!==0&&t.depthOfChildModified(),t)}removeChild(...e){if(e.length>1){for(let r=0;r<e.length;r++)this.removeChild(e[r]);return e[0]}const t=e[0],i=this.children.indexOf(t);return i>-1&&(this._didViewChangeTick++,this.children.splice(i,1),this.renderGroup?this.renderGroup.removeChild(t):this.parentRenderGroup&&this.parentRenderGroup.removeChild(t),t.parentRenderLayer&&t.parentRenderLayer.detach(t),t.parent=null,this.emit("childRemoved",t,this,i),t.emit("removed",this)),t}_onUpdate(e){e&&e===this._skew&&this._updateSkew(),this._didContainerChangeTick++,!this.didChange&&(this.didChange=!0,this.parentRenderGroup&&this.parentRenderGroup.onChildUpdate(this))}set isRenderGroup(e){!!this.renderGroup!==e&&(e?this.enableRenderGroup():this.disableRenderGroup())}get isRenderGroup(){return!!this.renderGroup}enableRenderGroup(){if(this.renderGroup)return;const e=this.parentRenderGroup;e==null||e.removeChild(this),this.renderGroup=Tt.get(Xn,this),this.groupTransform=D.IDENTITY,e==null||e.addChild(this),this._updateIsSimple()}disableRenderGroup(){if(!this.renderGroup)return;const e=this.parentRenderGroup;e==null||e.removeChild(this),Tt.return(this.renderGroup),this.renderGroup=null,this.groupTransform=this.relativeGroupTransform,e==null||e.addChild(this),this._updateIsSimple()}_updateIsSimple(){this.isSimple=!this.renderGroup&&this.effects.length===0}get worldTransform(){return this._worldTransform||(this._worldTransform=new D),this.renderGroup?this._worldTransform.copyFrom(this.renderGroup.worldTransform):this.parentRenderGroup&&this._worldTransform.appendFrom(this.relativeGroupTransform,this.parentRenderGroup.worldTransform),this._worldTransform}get x(){return this._position.x}set x(e){this._position.x=e}get y(){return this._position.y}set y(e){this._position.y=e}get position(){return this._position}set position(e){this._position.copyFrom(e)}get rotation(){return this._rotation}set rotation(e){this._rotation!==e&&(this._rotation=e,this._onUpdate(this._skew))}get angle(){return this.rotation*en}set angle(e){this.rotation=e*tn}get pivot(){return this._pivot===gt&&(this._pivot=new Q(this,0,0)),this._pivot}set pivot(e){this._pivot===gt&&(this._pivot=new Q(this,0,0),this._origin!==yt&&De("Setting both a pivot and origin on a Container is not recommended. This can lead to unexpected behavior if not handled carefully.")),typeof e=="number"?this._pivot.set(e):this._pivot.copyFrom(e)}get skew(){return this._skew===Ht&&(this._skew=new Q(this,0,0)),this._skew}set skew(e){this._skew===Ht&&(this._skew=new Q(this,0,0)),this._skew.copyFrom(e)}get scale(){return this._scale===qt&&(this._scale=new Q(this,1,1)),this._scale}set scale(e){this._scale===qt&&(this._scale=new Q(this,0,0)),typeof e=="string"&&(e=parseFloat(e)),typeof e=="number"?this._scale.set(e):this._scale.copyFrom(e)}get origin(){return this._origin===yt&&(this._origin=new Q(this,0,0)),this._origin}set origin(e){this._origin===yt&&(this._origin=new Q(this,0,0),this._pivot!==gt&&De("Setting both a pivot and origin on a Container is not recommended. This can lead to unexpected behavior if not handled carefully.")),typeof e=="number"?this._origin.set(e):this._origin.copyFrom(e)}get width(){return Math.abs(this.scale.x*this.getLocalBounds().width)}set width(e){const t=this.getLocalBounds().width;this._setWidth(e,t)}get height(){return Math.abs(this.scale.y*this.getLocalBounds().height)}set height(e){const t=this.getLocalBounds().height;this._setHeight(e,t)}getSize(e){e||(e={});const t=this.getLocalBounds();return e.width=Math.abs(this.scale.x*t.width),e.height=Math.abs(this.scale.y*t.height),e}setSize(e,t){const i=this.getLocalBounds();typeof e=="object"?(t=e.height??e.width,e=e.width):t??(t=e),e!==void 0&&this._setWidth(e,i.width),t!==void 0&&this._setHeight(t,i.height)}_updateSkew(){const e=this._rotation,t=this._skew;this._cx=Math.cos(e+t._y),this._sx=Math.sin(e+t._y),this._cy=-Math.sin(e-t._x),this._sy=Math.cos(e-t._x)}updateTransform(e){return this.position.set(typeof e.x=="number"?e.x:this.position.x,typeof e.y=="number"?e.y:this.position.y),this.scale.set(typeof e.scaleX=="number"?e.scaleX||1:this.scale.x,typeof e.scaleY=="number"?e.scaleY||1:this.scale.y),this.rotation=typeof e.rotation=="number"?e.rotation:this.rotation,this.skew.set(typeof e.skewX=="number"?e.skewX:this.skew.x,typeof e.skewY=="number"?e.skewY:this.skew.y),this.pivot.set(typeof e.pivotX=="number"?e.pivotX:this.pivot.x,typeof e.pivotY=="number"?e.pivotY:this.pivot.y),this.origin.set(typeof e.originX=="number"?e.originX:this.origin.x,typeof e.originY=="number"?e.originY:this.origin.y),this}setFromMatrix(e){e.decompose(this)}updateLocalTransform(){const e=this._didContainerChangeTick;if(this._didLocalTransformChangeId===e)return;this._didLocalTransformChangeId=e;const t=this.localTransform,i=this._scale,r=this._pivot,n=this._origin,a=this._position,o=i._x,h=i._y,l=r._x,c=r._y,d=-n._x,u=-n._y;t.a=this._cx*o,t.b=this._sx*o,t.c=this._cy*h,t.d=this._sy*h,t.tx=a._x-(l*t.a+c*t.c)+(d*t.a+u*t.c)-d*o,t.ty=a._y-(l*t.b+c*t.d)+(d*t.b+u*t.d)-u*h}set alpha(e){e!==this.localAlpha&&(this.localAlpha=e,this._updateFlags|=Us,this._onUpdate())}get alpha(){return this.localAlpha}set tint(e){const i=wt.shared.setValue(e??16777215).toBgrNumber();i!==this.localColor&&(this.localColor=i,this._updateFlags|=Us,this._onUpdate())}get tint(){return At(this.localColor)}set blendMode(e){this.localBlendMode!==e&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=qn,this.localBlendMode=e,this._onUpdate())}get blendMode(){return this.localBlendMode}get visible(){return!!(this.localDisplayStatus&2)}set visible(e){const t=e?2:0;(this.localDisplayStatus&2)!==t&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=$t,this.localDisplayStatus^=2,this._onUpdate())}get culled(){return!(this.localDisplayStatus&4)}set culled(e){const t=e?0:4;(this.localDisplayStatus&4)!==t&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=$t,this.localDisplayStatus^=4,this._onUpdate())}get renderable(){return!!(this.localDisplayStatus&1)}set renderable(e){const t=e?1:0;(this.localDisplayStatus&1)!==t&&(this._updateFlags|=$t,this.localDisplayStatus^=1,this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._onUpdate())}get isRenderable(){return this.localDisplayStatus===7&&this.groupAlpha>0}destroy(e=!1){var r;if(this.destroyed)return;this.destroyed=!0;let t;if(this.children.length&&(t=this.removeChildren(0,this.children.length)),this.removeFromParent(),this.parent=null,this._maskEffect=null,this._filterEffect=null,this.effects=null,this._position=null,this._scale=null,this._pivot=null,this._origin=null,this._skew=null,this.emit("destroyed",this),this.removeAllListeners(),(typeof e=="boolean"?e:e==null?void 0:e.children)&&t)for(let n=0;n<t.length;++n)t[n].destroy(e);(r=this.renderGroup)==null||r.destroy(),this.renderGroup=null}}xe.mixin(Oe,An,En,On,Bn,Fn,Mn,Tn,Dn,vn,bn,Rn,kn);class $n extends Oe{constructor(e){super(e),this.canBundle=!0,this.allowChildren=!1,this._roundPixels=0,this._lastUsed=-1,this._gpuData=Object.create(null),this._bounds=new Ge(0,1,0,0),this._boundsDirty=!0}get bounds(){return this._boundsDirty?(this.updateBounds(),this._boundsDirty=!1,this._bounds):this._bounds}get roundPixels(){return!!this._roundPixels}set roundPixels(e){this._roundPixels=e?1:0}containsPoint(e){const t=this.bounds,{x:i,y:r}=e;return i>=t.minX&&i<=t.maxX&&r>=t.minY&&r<=t.maxY}onViewUpdate(){if(this._didViewChangeTick++,this._boundsDirty=!0,this.didViewUpdate)return;this.didViewUpdate=!0;const e=this.renderGroup||this.parentRenderGroup;e&&e.onChildViewUpdate(this)}destroy(e){var t,i;super.destroy(e),this._bounds=null;for(const r in this._gpuData)(i=(t=this._gpuData[r]).destroy)==null||i.call(t);this._gpuData=null}collectRenderablesSimple(e,t,i){const{renderPipes:r}=t;r.blendMode.setBlendMode(this,this.groupBlendMode,e),r[this.renderPipeId].addRenderable(this,e),this.didViewUpdate=!1;const a=this.children,o=a.length;for(let h=0;h<o;h++)a[h].collectRenderables(e,t,i)}}class Me extends $n{constructor(e=B.EMPTY){e instanceof B&&(e={texture:e});const{texture:t=B.EMPTY,anchor:i,roundPixels:r,width:n,height:a,...o}=e;super({label:"Sprite",...o}),this.renderPipeId="sprite",this.batched=!0,this._visualBounds={minX:0,maxX:1,minY:0,maxY:0},this._anchor=new Q({_onUpdate:()=>{this.onViewUpdate()}}),i?this.anchor=i:t.defaultAnchor&&(this.anchor=t.defaultAnchor),this.texture=t,this.allowChildren=!1,this.roundPixels=r??!1,n!==void 0&&(this.width=n),a!==void 0&&(this.height=a)}static from(e,t=!1){return e instanceof B?new Me(e):new Me(B.from(e,t))}set texture(e){e||(e=B.EMPTY);const t=this._texture;t!==e&&(t&&t.dynamic&&t.off("update",this.onViewUpdate,this),e.dynamic&&e.on("update",this.onViewUpdate,this),this._texture=e,this._width&&this._setWidth(this._width,this._texture.orig.width),this._height&&this._setHeight(this._height,this._texture.orig.height),this.onViewUpdate())}get texture(){return this._texture}get visualBounds(){return ln(this._visualBounds,this._anchor,this._texture),this._visualBounds}get sourceBounds(){return le("8.6.1","Sprite.sourceBounds is deprecated, use visualBounds instead."),this.visualBounds}updateBounds(){const e=this._anchor,t=this._texture,i=this._bounds,{width:r,height:n}=t.orig;i.minX=-e._x*r,i.maxX=i.minX+r,i.minY=-e._y*n,i.maxY=i.minY+n}destroy(e=!1){if(super.destroy(e),typeof e=="boolean"?e:e==null?void 0:e.texture){const i=typeof e=="boolean"?e:e==null?void 0:e.textureSource;this._texture.destroy(i)}this._texture=null,this._visualBounds=null,this._bounds=null,this._anchor=null,this._gpuData=null}get anchor(){return this._anchor}set anchor(e){typeof e=="number"?this._anchor.set(e):this._anchor.copyFrom(e)}get width(){return Math.abs(this.scale.x)*this._texture.orig.width}set width(e){this._setWidth(e,this._texture.orig.width),this._width=e}get height(){return Math.abs(this.scale.y)*this._texture.orig.height}set height(e){this._setHeight(e,this._texture.orig.height),this._height=e}getSize(e){return e||(e={}),e.width=Math.abs(this.scale.x)*this._texture.orig.width,e.height=Math.abs(this.scale.y)*this._texture.orig.height,e}setSize(e,t){typeof e=="object"?(t=e.height??e.width,e=e.width):t??(t=e),e!==void 0&&this._setWidth(e,this._texture.orig.width),t!==void 0&&this._setHeight(t,this._texture.orig.height)}}const Wn=new Ge;function vi(s,e,t){const i=Wn;s.measurable=!0,pi(s,t,i),e.addBoundsMask(i),s.measurable=!1}function xi(s,e,t){const i=Se.get();s.measurable=!0;const r=J.get().identity(),n=bi(s,t,r);_i(s,i,n),s.measurable=!1,e.addBoundsMask(i),J.return(r),Se.return(i)}function bi(s,e,t){return s?(s!==e&&(bi(s.parent,e,t),s.updateLocalTransform(),t.append(s.localTransform)),t):(De("Mask bounds, renderable is not inside the root container"),t)}class wi{constructor(e){this.priority=0,this.inverse=!1,this.pipe="alphaMask",e!=null&&e.mask&&this.init(e.mask)}init(e){this.mask=e,this.renderMaskToTexture=!(e instanceof Me),this.mask.renderable=this.renderMaskToTexture,this.mask.includeInBuild=!this.renderMaskToTexture,this.mask.measurable=!1}reset(){this.mask.measurable=!0,this.mask=null}addBounds(e,t){this.inverse||vi(this.mask,e,t)}addLocalBounds(e,t){xi(this.mask,e,t)}containsPoint(e,t){const i=this.mask;return t(i,e)}destroy(){this.reset()}static test(e){return e instanceof Me}}wi.extension=Y.MaskEffect;class Ai{constructor(e){this.priority=0,this.pipe="colorMask",e!=null&&e.mask&&this.init(e.mask)}init(e){this.mask=e}destroy(){}static test(e){return typeof e=="number"}}Ai.extension=Y.MaskEffect;class ki{constructor(e){this.priority=0,this.pipe="stencilMask",e!=null&&e.mask&&this.init(e.mask)}init(e){this.mask=e,this.mask.includeInBuild=!1,this.mask.measurable=!1}reset(){this.mask.measurable=!0,this.mask.includeInBuild=!0,this.mask=null}addBounds(e,t){vi(this.mask,e,t)}addLocalBounds(e,t){xi(this.mask,e,t)}containsPoint(e,t){const i=this.mask;return t(i,e)}destroy(){this.reset()}static test(e){return e instanceof Oe}}ki.extension=Y.MaskEffect;const Vn={createCanvas:(s,e)=>{const t=document.createElement("canvas");return t.width=s,t.height=e,t},getCanvasRenderingContext2D:()=>CanvasRenderingContext2D,getWebGLRenderingContext:()=>WebGLRenderingContext,getNavigator:()=>navigator,getBaseUrl:()=>document.baseURI??window.location.href,getFontFaceSet:()=>document.fonts,fetch:(s,e)=>fetch(s,e),parseXML:s=>new DOMParser().parseFromString(s,"text/xml")};let Ns=Vn;const rt={get(){return Ns},set(s){Ns=s}};class Ci extends pe{constructor(e){e.resource||(e.resource=rt.get().createCanvas()),e.width||(e.width=e.resource.width,e.autoDensity||(e.width/=e.resolution)),e.height||(e.height=e.resource.height,e.autoDensity||(e.height/=e.resolution)),super(e),this.uploadMethodId="image",this.autoDensity=e.autoDensity,this.resizeCanvas(),this.transparent=!!e.transparent}resizeCanvas(){this.autoDensity&&"style"in this.resource&&(this.resource.style.width=`${this.width}px`,this.resource.style.height=`${this.height}px`),(this.resource.width!==this.pixelWidth||this.resource.height!==this.pixelHeight)&&(this.resource.width=this.pixelWidth,this.resource.height=this.pixelHeight)}resize(e=this.width,t=this.height,i=this._resolution){const r=super.resize(e,t,i);return r&&this.resizeCanvas(),r}static test(e){return globalThis.HTMLCanvasElement&&e instanceof HTMLCanvasElement||globalThis.OffscreenCanvas&&e instanceof OffscreenCanvas}get context2D(){return this._context2D||(this._context2D=this.resource.getContext("2d"))}}Ci.extension=Y.TextureSource;class Mi extends pe{constructor(e){super(e),this.uploadMethodId="image",this.autoGarbageCollect=!0}static test(e){return globalThis.HTMLImageElement&&e instanceof HTMLImageElement||typeof ImageBitmap<"u"&&e instanceof ImageBitmap||globalThis.VideoFrame&&e instanceof VideoFrame}}Mi.extension=Y.TextureSource;var es=(s=>(s[s.INTERACTION=50]="INTERACTION",s[s.HIGH=25]="HIGH",s[s.NORMAL=0]="NORMAL",s[s.LOW=-25]="LOW",s[s.UTILITY=-50]="UTILITY",s))(es||{});class Wt{constructor(e,t=null,i=0,r=!1){this.next=null,this.previous=null,this._destroyed=!1,this._fn=e,this._context=t,this.priority=i,this._once=r}match(e,t=null){return this._fn===e&&this._context===t}emit(e){this._fn&&(this._context?this._fn.call(this._context,e):this._fn(e));const t=this.next;return this._once&&this.destroy(!0),this._destroyed&&(this.next=null),t}connect(e){this.previous=e,e.next&&(e.next.previous=this),this.next=e.next,e.next=this}destroy(e=!1){this._destroyed=!0,this._fn=null,this._context=null,this.previous&&(this.previous.next=this.next),this.next&&(this.next.previous=this.previous);const t=this.next;return this.next=e?null:t,this.previous=null,t}}const Ti=class re{constructor(){this.autoStart=!1,this.deltaTime=1,this.lastTime=-1,this.speed=1,this.started=!1,this._requestId=null,this._maxElapsedMS=100,this._minElapsedMS=0,this._protected=!1,this._lastFrame=-1,this._head=new Wt(null,null,1/0),this.deltaMS=1/re.targetFPMS,this.elapsedMS=1/re.targetFPMS,this._tick=e=>{this._requestId=null,this.started&&(this.update(e),this.started&&this._requestId===null&&this._head.next&&(this._requestId=requestAnimationFrame(this._tick)))}}_requestIfNeeded(){this._requestId===null&&this._head.next&&(this.lastTime=performance.now(),this._lastFrame=this.lastTime,this._requestId=requestAnimationFrame(this._tick))}_cancelIfNeeded(){this._requestId!==null&&(cancelAnimationFrame(this._requestId),this._requestId=null)}_startIfPossible(){this.started?this._requestIfNeeded():this.autoStart&&this.start()}add(e,t,i=es.NORMAL){return this._addListener(new Wt(e,t,i))}addOnce(e,t,i=es.NORMAL){return this._addListener(new Wt(e,t,i,!0))}_addListener(e){let t=this._head.next,i=this._head;if(!t)e.connect(i);else{for(;t;){if(e.priority>t.priority){e.connect(i);break}i=t,t=t.next}e.previous||e.connect(i)}return this._startIfPossible(),this}remove(e,t){let i=this._head.next;for(;i;)i.match(e,t)?i=i.destroy():i=i.next;return this._head.next||this._cancelIfNeeded(),this}get count(){if(!this._head)return 0;let e=0,t=this._head;for(;t=t.next;)e++;return e}start(){this.started||(this.started=!0,this._requestIfNeeded())}stop(){this.started&&(this.started=!1,this._cancelIfNeeded())}destroy(){if(!this._protected){this.stop();let e=this._head.next;for(;e;)e=e.destroy(!0);this._head.destroy(),this._head=null}}update(e=performance.now()){let t;if(e>this.lastTime){if(t=this.elapsedMS=e-this.lastTime,t>this._maxElapsedMS&&(t=this._maxElapsedMS),t*=this.speed,this._minElapsedMS){const n=e-this._lastFrame|0;if(n<this._minElapsedMS)return;this._lastFrame=e-n%this._minElapsedMS}this.deltaMS=t,this.deltaTime=this.deltaMS*re.targetFPMS;const i=this._head;let r=i.next;for(;r;)r=r.emit(this);i.next||this._cancelIfNeeded()}else this.deltaTime=this.deltaMS=this.elapsedMS=0;this.lastTime=e}get FPS(){return 1e3/this.elapsedMS}get minFPS(){return 1e3/this._maxElapsedMS}set minFPS(e){const t=Math.min(this.maxFPS,e),i=Math.min(Math.max(0,t)/1e3,re.targetFPMS);this._maxElapsedMS=1/i}get maxFPS(){return this._minElapsedMS?Math.round(1e3/this._minElapsedMS):0}set maxFPS(e){if(e===0)this._minElapsedMS=0;else{const t=Math.max(this.minFPS,e);this._minElapsedMS=1/(t/1e3)}}static get shared(){if(!re._shared){const e=re._shared=new re;e.autoStart=!0,e._protected=!0}return re._shared}static get system(){if(!re._system){const e=re._system=new re;e.autoStart=!0,e._protected=!0}return re._system}};Ti.targetFPMS=.06;let vt=Ti,Vt;async function jn(){return Vt??(Vt=(async()=>{var a;const e=document.createElement("canvas").getContext("webgl");if(!e)return"premultiply-alpha-on-upload";const t=await new Promise(o=>{const h=document.createElement("video");h.onloadeddata=()=>o(h),h.onerror=()=>o(null),h.autoplay=!1,h.crossOrigin="anonymous",h.preload="auto",h.src="data:video/webm;base64,GkXfo59ChoEBQveBAULygQRC84EIQoKEd2VibUKHgQJChYECGFOAZwEAAAAAAAHTEU2bdLpNu4tTq4QVSalmU6yBoU27i1OrhBZUrmtTrIHGTbuMU6uEElTDZ1OsggEXTbuMU6uEHFO7a1OsggG97AEAAAAAAABZAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVSalmoCrXsYMPQkBNgIRMYXZmV0GETGF2ZkSJiEBEAAAAAAAAFlSua8yuAQAAAAAAAEPXgQFzxYgAAAAAAAAAAZyBACK1nIN1bmSIgQCGhVZfVlA5g4EBI+ODhAJiWgDglLCBArqBApqBAlPAgQFVsIRVuYEBElTDZ9Vzc9JjwItjxYgAAAAAAAAAAWfInEWjh0VOQ09ERVJEh49MYXZjIGxpYnZweC12cDlnyKJFo4hEVVJBVElPTkSHlDAwOjAwOjAwLjA0MDAwMDAwMAAAH0O2dcfngQCgwqGggQAAAIJJg0IAABAAFgA4JBwYSgAAICAAEb///4r+AAB1oZ2mm+6BAaWWgkmDQgAAEAAWADgkHBhKAAAgIABIQBxTu2uRu4+zgQC3iveBAfGCAXHwgQM=",h.load()});if(!t)return"premultiply-alpha-on-upload";const i=e.createTexture();e.bindTexture(e.TEXTURE_2D,i);const r=e.createFramebuffer();e.bindFramebuffer(e.FRAMEBUFFER,r),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,i,0),e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),e.pixelStorei(e.UNPACK_COLORSPACE_CONVERSION_WEBGL,e.NONE),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,t);const n=new Uint8Array(4);return e.readPixels(0,0,1,1,e.RGBA,e.UNSIGNED_BYTE,n),e.deleteFramebuffer(r),e.deleteTexture(i),(a=e.getExtension("WEBGL_lose_context"))==null||a.loseContext(),n[0]<=n[3]?"premultiplied-alpha":"premultiply-alpha-on-upload"})()),Vt}const St=class Si extends pe{constructor(e){super(e),this.isReady=!1,this.uploadMethodId="video",e={...Si.defaultOptions,...e},this._autoUpdate=!0,this._isConnectedToTicker=!1,this._updateFPS=e.updateFPS||0,this._msToNextUpdate=0,this.autoPlay=e.autoPlay!==!1,this.alphaMode=e.alphaMode??"premultiply-alpha-on-upload",this._videoFrameRequestCallback=this._videoFrameRequestCallback.bind(this),this._videoFrameRequestCallbackHandle=null,this._load=null,this._resolve=null,this._reject=null,this._onCanPlay=this._onCanPlay.bind(this),this._onCanPlayThrough=this._onCanPlayThrough.bind(this),this._onError=this._onError.bind(this),this._onPlayStart=this._onPlayStart.bind(this),this._onPlayStop=this._onPlayStop.bind(this),this._onSeeked=this._onSeeked.bind(this),e.autoLoad!==!1&&this.load()}updateFrame(){if(!this.destroyed){if(this._updateFPS){const e=vt.shared.elapsedMS*this.resource.playbackRate;this._msToNextUpdate=Math.floor(this._msToNextUpdate-e)}(!this._updateFPS||this._msToNextUpdate<=0)&&(this._msToNextUpdate=this._updateFPS?Math.floor(1e3/this._updateFPS):0),this.isValid&&this.update()}}_videoFrameRequestCallback(){this.updateFrame(),this.destroyed?this._videoFrameRequestCallbackHandle=null:this._videoFrameRequestCallbackHandle=this.resource.requestVideoFrameCallback(this._videoFrameRequestCallback)}get isValid(){return!!this.resource.videoWidth&&!!this.resource.videoHeight}async load(){if(this._load)return this._load;const e=this.resource,t=this.options;return(e.readyState===e.HAVE_ENOUGH_DATA||e.readyState===e.HAVE_FUTURE_DATA)&&e.width&&e.height&&(e.complete=!0),e.addEventListener("play",this._onPlayStart),e.addEventListener("pause",this._onPlayStop),e.addEventListener("seeked",this._onSeeked),this._isSourceReady()?this._mediaReady():(t.preload||e.addEventListener("canplay",this._onCanPlay),e.addEventListener("canplaythrough",this._onCanPlayThrough),e.addEventListener("error",this._onError,!0)),this.alphaMode=await jn(),this._load=new Promise((i,r)=>{this.isValid?i(this):(this._resolve=i,this._reject=r,t.preloadTimeoutMs!==void 0&&(this._preloadTimeout=setTimeout(()=>{this._onError(new ErrorEvent(`Preload exceeded timeout of ${t.preloadTimeoutMs}ms`))})),e.load())}),this._load}_onError(e){this.resource.removeEventListener("error",this._onError,!0),this.emit("error",e),this._reject&&(this._reject(e),this._reject=null,this._resolve=null)}_isSourcePlaying(){const e=this.resource;return!e.paused&&!e.ended}_isSourceReady(){return this.resource.readyState>2}_onPlayStart(){this.isValid||this._mediaReady(),this._configureAutoUpdate()}_onPlayStop(){this._configureAutoUpdate()}_onSeeked(){this._autoUpdate&&!this._isSourcePlaying()&&(this._msToNextUpdate=0,this.updateFrame(),this._msToNextUpdate=0)}_onCanPlay(){this.resource.removeEventListener("canplay",this._onCanPlay),this._mediaReady()}_onCanPlayThrough(){this.resource.removeEventListener("canplaythrough",this._onCanPlay),this._preloadTimeout&&(clearTimeout(this._preloadTimeout),this._preloadTimeout=void 0),this._mediaReady()}_mediaReady(){const e=this.resource;this.isValid&&(this.isReady=!0,this.resize(e.videoWidth,e.videoHeight)),this._msToNextUpdate=0,this.updateFrame(),this._msToNextUpdate=0,this._resolve&&(this._resolve(this),this._resolve=null,this._reject=null),this._isSourcePlaying()?this._onPlayStart():this.autoPlay&&this.resource.play()}destroy(){this._configureAutoUpdate();const e=this.resource;e&&(e.removeEventListener("play",this._onPlayStart),e.removeEventListener("pause",this._onPlayStop),e.removeEventListener("seeked",this._onSeeked),e.removeEventListener("canplay",this._onCanPlay),e.removeEventListener("canplaythrough",this._onCanPlayThrough),e.removeEventListener("error",this._onError,!0),e.pause(),e.src="",e.load()),super.destroy()}get autoUpdate(){return this._autoUpdate}set autoUpdate(e){e!==this._autoUpdate&&(this._autoUpdate=e,this._configureAutoUpdate())}get updateFPS(){return this._updateFPS}set updateFPS(e){e!==this._updateFPS&&(this._updateFPS=e,this._configureAutoUpdate())}_configureAutoUpdate(){this._autoUpdate&&this._isSourcePlaying()?!this._updateFPS&&this.resource.requestVideoFrameCallback?(this._isConnectedToTicker&&(vt.shared.remove(this.updateFrame,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0),this._videoFrameRequestCallbackHandle===null&&(this._videoFrameRequestCallbackHandle=this.resource.requestVideoFrameCallback(this._videoFrameRequestCallback))):(this._videoFrameRequestCallbackHandle!==null&&(this.resource.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker||(vt.shared.add(this.updateFrame,this),this._isConnectedToTicker=!0,this._msToNextUpdate=0)):(this._videoFrameRequestCallbackHandle!==null&&(this.resource.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker&&(vt.shared.remove(this.updateFrame,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0))}static test(e){return globalThis.HTMLVideoElement&&e instanceof HTMLVideoElement}};St.extension=Y.TextureSource;St.defaultOptions={...pe.defaultOptions,autoLoad:!0,autoPlay:!0,updateFPS:0,crossorigin:!0,loop:!1,muted:!0,playsinline:!0,preload:!1};St.MIME_TYPES={ogv:"video/ogg",mov:"video/quicktime",m4v:"video/mp4"};let zn=St;const We=(s,e,t=!1)=>(Array.isArray(s)||(s=[s]),e?s.map(i=>typeof i=="string"||t?e(i):i):s);class Kn{constructor(){this._parsers=[],this._cache=new Map,this._cacheMap=new Map}reset(){this._cacheMap.clear(),this._cache.clear()}has(e){return this._cache.has(e)}get(e){const t=this._cache.get(e);return t||De(`[Assets] Asset id ${e} was not found in the Cache`),t}set(e,t){const i=We(e);let r;for(let h=0;h<this.parsers.length;h++){const l=this.parsers[h];if(l.test(t)){r=l.getCacheableAssets(i,t);break}}const n=new Map(Object.entries(r||{}));r||i.forEach(h=>{n.set(h,t)});const a=[...n.keys()],o={cacheKeys:a,keys:i};i.forEach(h=>{this._cacheMap.set(h,o)}),a.forEach(h=>{const l=r?r[h]:t;this._cache.has(h)&&this._cache.get(h)!==l&&De("[Cache] already has key:",h),this._cache.set(h,n.get(h))})}remove(e){if(!this._cacheMap.has(e)){De(`[Assets] Asset id ${e} was not found in the Cache`);return}const t=this._cacheMap.get(e);t.cacheKeys.forEach(r=>{this._cache.delete(r)}),t.keys.forEach(r=>{this._cacheMap.delete(r)})}get parsers(){return this._parsers}}const Ve=new Kn,ts=[];xe.handleByList(Y.TextureSource,ts);function Ei(s={}){const e=s&&s.resource,t=e?s.resource:s,i=e?s:{resource:s};for(let r=0;r<ts.length;r++){const n=ts[r];if(n.test(t))return new n(i)}throw new Error(`Could not find a source type for resource: ${i.resource}`)}function Qn(s={},e=!1){const t=s&&s.resource,i=t?s.resource:s,r=t?s:{resource:s};if(!e&&Ve.has(i))return Ve.get(i);const n=new B({source:Ei(r)});return n.on("destroy",()=>{Ve.has(i)&&Ve.remove(i)}),e||Ve.set(i,n),n}function Jn(s,e=!1){return typeof s=="string"?Ve.get(s):s instanceof pe?new B({source:s}):Qn(s,e)}B.from=Jn;pe.from=Ei;xe.add(wi,Ai,ki,zn,Mi,Ci,hs);var Pi=(s=>(s[s.Low=0]="Low",s[s.Normal=1]="Normal",s[s.High=2]="High",s))(Pi||{});function de(s){if(typeof s!="string")throw new TypeError(`Path must be a string. Received ${JSON.stringify(s)}`)}function Qe(s){return s.split("?")[0].split("#")[0]}function Zn(s){return s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function ea(s,e,t){return s.replace(new RegExp(Zn(e),"g"),t)}function ta(s,e){let t="",i=0,r=-1,n=0,a=-1;for(let o=0;o<=s.length;++o){if(o<s.length)a=s.charCodeAt(o);else{if(a===47)break;a=47}if(a===47){if(!(r===o-1||n===1))if(r!==o-1&&n===2){if(t.length<2||i!==2||t.charCodeAt(t.length-1)!==46||t.charCodeAt(t.length-2)!==46){if(t.length>2){const h=t.lastIndexOf("/");if(h!==t.length-1){h===-1?(t="",i=0):(t=t.slice(0,h),i=t.length-1-t.lastIndexOf("/")),r=o,n=0;continue}}else if(t.length===2||t.length===1){t="",i=0,r=o,n=0;continue}}}else t.length>0?t+=`/${s.slice(r+1,o)}`:t=s.slice(r+1,o),i=o-r-1;r=o,n=0}else a===46&&n!==-1?++n:n=-1}return t}const nt={toPosix(s){return ea(s,"\\","/")},isUrl(s){return/^https?:/.test(this.toPosix(s))},isDataUrl(s){return/^data:([a-z]+\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s<>]*?)$/i.test(s)},isBlobUrl(s){return s.startsWith("blob:")},hasProtocol(s){return/^[^/:]+:/.test(this.toPosix(s))},getProtocol(s){de(s),s=this.toPosix(s);const e=/^file:\/\/\//.exec(s);if(e)return e[0];const t=/^[^/:]+:\/{0,2}/.exec(s);return t?t[0]:""},toAbsolute(s,e,t){if(de(s),this.isDataUrl(s)||this.isBlobUrl(s))return s;const i=Qe(this.toPosix(e??rt.get().getBaseUrl())),r=Qe(this.toPosix(t??this.rootname(i)));return s=this.toPosix(s),s.startsWith("/")?nt.join(r,s.slice(1)):this.isAbsolute(s)?s:this.join(i,s)},normalize(s){if(de(s),s.length===0)return".";if(this.isDataUrl(s)||this.isBlobUrl(s))return s;s=this.toPosix(s);let e="";const t=s.startsWith("/");this.hasProtocol(s)&&(e=this.rootname(s),s=s.slice(e.length));const i=s.endsWith("/");return s=ta(s),s.length>0&&i&&(s+="/"),t?`/${s}`:e+s},isAbsolute(s){return de(s),s=this.toPosix(s),this.hasProtocol(s)?!0:s.startsWith("/")},join(...s){if(s.length===0)return".";let e;for(let t=0;t<s.length;++t){const i=s[t];if(de(i),i.length>0)if(e===void 0)e=i;else{const r=s[t-1]??"";this.joinExtensions.includes(this.extname(r).toLowerCase())?e+=`/../${i}`:e+=`/${i}`}}return e===void 0?".":this.normalize(e)},dirname(s){if(de(s),s.length===0)return".";s=this.toPosix(s);let e=s.charCodeAt(0);const t=e===47;let i=-1,r=!0;const n=this.getProtocol(s),a=s;s=s.slice(n.length);for(let o=s.length-1;o>=1;--o)if(e=s.charCodeAt(o),e===47){if(!r){i=o;break}}else r=!1;return i===-1?t?"/":this.isUrl(a)?n+s:n:t&&i===1?"//":n+s.slice(0,i)},rootname(s){de(s),s=this.toPosix(s);let e="";if(s.startsWith("/")?e="/":e=this.getProtocol(s),this.isUrl(s)){const t=s.indexOf("/",e.length);t!==-1?e=s.slice(0,t):e=s,e.endsWith("/")||(e+="/")}return e},basename(s,e){de(s),e&&de(e),s=Qe(this.toPosix(s));let t=0,i=-1,r=!0,n;if(e!==void 0&&e.length>0&&e.length<=s.length){if(e.length===s.length&&e===s)return"";let a=e.length-1,o=-1;for(n=s.length-1;n>=0;--n){const h=s.charCodeAt(n);if(h===47){if(!r){t=n+1;break}}else o===-1&&(r=!1,o=n+1),a>=0&&(h===e.charCodeAt(a)?--a===-1&&(i=n):(a=-1,i=o))}return t===i?i=o:i===-1&&(i=s.length),s.slice(t,i)}for(n=s.length-1;n>=0;--n)if(s.charCodeAt(n)===47){if(!r){t=n+1;break}}else i===-1&&(r=!1,i=n+1);return i===-1?"":s.slice(t,i)},extname(s){de(s),s=Qe(this.toPosix(s));let e=-1,t=0,i=-1,r=!0,n=0;for(let a=s.length-1;a>=0;--a){const o=s.charCodeAt(a);if(o===47){if(!r){t=a+1;break}continue}i===-1&&(r=!1,i=a+1),o===46?e===-1?e=a:n!==1&&(n=1):e!==-1&&(n=-1)}return e===-1||i===-1||n===0||n===1&&e===i-1&&e===t+1?"":s.slice(e,i)},parse(s){de(s);const e={root:"",dir:"",base:"",ext:"",name:""};if(s.length===0)return e;s=Qe(this.toPosix(s));let t=s.charCodeAt(0);const i=this.isAbsolute(s);let r;e.root=this.rootname(s),i||this.hasProtocol(s)?r=1:r=0;let n=-1,a=0,o=-1,h=!0,l=s.length-1,c=0;for(;l>=r;--l){if(t=s.charCodeAt(l),t===47){if(!h){a=l+1;break}continue}o===-1&&(h=!1,o=l+1),t===46?n===-1?n=l:c!==1&&(c=1):n!==-1&&(c=-1)}return n===-1||o===-1||c===0||c===1&&n===o-1&&n===a+1?o!==-1&&(a===0&&i?e.base=e.name=s.slice(1,o):e.base=e.name=s.slice(a,o)):(a===0&&i?(e.name=s.slice(1,n),e.base=s.slice(1,o)):(e.name=s.slice(a,n),e.base=s.slice(a,o)),e.ext=s.slice(n,o)),e.dir=this.dirname(s),e},sep:"/",delimiter:":",joinExtensions:[".html"]};function Ri(s,e,t,i,r){const n=e[t];for(let a=0;a<n.length;a++){const o=n[a];t<e.length-1?Ri(s.replace(i[t],o),e,t+1,i,r):r.push(s.replace(i[t],o))}}function sa(s){const e=/\{(.*?)\}/g,t=s.match(e),i=[];if(t){const r=[];t.forEach(n=>{const a=n.substring(1,n.length-1).split(",");r.push(a)}),Ri(s,r,0,t,i)}else i.push(s);return i}const Ys=s=>!Array.isArray(s);class Ii{constructor(){this._defaultBundleIdentifierOptions={connector:"-",createBundleAssetId:(e,t)=>`${e}${this._bundleIdConnector}${t}`,extractAssetIdFromBundle:(e,t)=>t.replace(`${e}${this._bundleIdConnector}`,"")},this._bundleIdConnector=this._defaultBundleIdentifierOptions.connector,this._createBundleAssetId=this._defaultBundleIdentifierOptions.createBundleAssetId,this._extractAssetIdFromBundle=this._defaultBundleIdentifierOptions.extractAssetIdFromBundle,this._assetMap={},this._preferredOrder=[],this._parsers=[],this._resolverHash={},this._bundles={}}setBundleIdentifier(e){if(this._bundleIdConnector=e.connector??this._bundleIdConnector,this._createBundleAssetId=e.createBundleAssetId??this._createBundleAssetId,this._extractAssetIdFromBundle=e.extractAssetIdFromBundle??this._extractAssetIdFromBundle,this._extractAssetIdFromBundle("foo",this._createBundleAssetId("foo","bar"))!=="bar")throw new Error("[Resolver] GenerateBundleAssetId are not working correctly")}prefer(...e){e.forEach(t=>{this._preferredOrder.push(t),t.priority||(t.priority=Object.keys(t.params))}),this._resolverHash={}}set basePath(e){this._basePath=e}get basePath(){return this._basePath}set rootPath(e){this._rootPath=e}get rootPath(){return this._rootPath}get parsers(){return this._parsers}reset(){this.setBundleIdentifier(this._defaultBundleIdentifierOptions),this._assetMap={},this._preferredOrder=[],this._resolverHash={},this._rootPath=null,this._basePath=null,this._manifest=null,this._bundles={},this._defaultSearchParams=null}setDefaultSearchParams(e){if(typeof e=="string")this._defaultSearchParams=e;else{const t=e;this._defaultSearchParams=Object.keys(t).map(i=>`${encodeURIComponent(i)}=${encodeURIComponent(t[i])}`).join("&")}}getAlias(e){const{alias:t,src:i}=e;return We(t||i,n=>typeof n=="string"?n:Array.isArray(n)?n.map(a=>(a==null?void 0:a.src)??a):n!=null&&n.src?n.src:n,!0)}addManifest(e){this._manifest&&De("[Resolver] Manifest already exists, this will be overwritten"),this._manifest=e,e.bundles.forEach(t=>{this.addBundle(t.name,t.assets)})}addBundle(e,t){const i=[];let r=t;Array.isArray(t)||(r=Object.entries(t).map(([n,a])=>typeof a=="string"||Array.isArray(a)?{alias:n,src:a}:{alias:n,...a})),r.forEach(n=>{const a=n.src,o=n.alias;let h;if(typeof o=="string"){const l=this._createBundleAssetId(e,o);i.push(l),h=[o,l]}else{const l=o.map(c=>this._createBundleAssetId(e,c));i.push(...l),h=[...o,...l]}this.add({...n,alias:h,src:a})}),this._bundles[e]=i}add(e){const t=[];Array.isArray(e)?t.push(...e):t.push(e);let i;i=n=>{this.hasKey(n)&&De(`[Resolver] already has key: ${n} overwriting`)},We(t).forEach(n=>{const{src:a}=n;let{data:o,format:h,loadParser:l}=n;const c=We(a).map(f=>typeof f=="string"?sa(f):Array.isArray(f)?f:[f]),d=this.getAlias(n);Array.isArray(d)?d.forEach(i):i(d);const u=[];c.forEach(f=>{f.forEach(m=>{let y={};if(typeof m!="object"){y.src=m;for(let p=0;p<this._parsers.length;p++){const g=this._parsers[p];if(g.test(m)){y=g.parse(m);break}}}else o=m.data??o,h=m.format??h,l=m.loadParser??l,y={...y,...m};if(!d)throw new Error(`[Resolver] alias is undefined for this asset: ${y.src}`);y=this._buildResolvedAsset(y,{aliases:d,data:o,format:h,loadParser:l}),u.push(y)})}),d.forEach(f=>{this._assetMap[f]=u})})}resolveBundle(e){const t=Ys(e);e=We(e);const i={};return e.forEach(r=>{const n=this._bundles[r];if(n){const a=this.resolve(n),o={};for(const h in a){const l=a[h];o[this._extractAssetIdFromBundle(r,h)]=l}i[r]=o}}),t?i[e[0]]:i}resolveUrl(e){const t=this.resolve(e);if(typeof e!="string"){const i={};for(const r in t)i[r]=t[r].src;return i}return t.src}resolve(e){const t=Ys(e);e=We(e);const i={};return e.forEach(r=>{if(!this._resolverHash[r])if(this._assetMap[r]){let n=this._assetMap[r];const a=this._getPreferredOrder(n);a==null||a.priority.forEach(o=>{a.params[o].forEach(h=>{const l=n.filter(c=>c[o]?c[o]===h:!1);l.length&&(n=l)})}),this._resolverHash[r]=n[0]}else this._resolverHash[r]=this._buildResolvedAsset({alias:[r],src:r},{});i[r]=this._resolverHash[r]}),t?i[e[0]]:i}hasKey(e){return!!this._assetMap[e]}hasBundle(e){return!!this._bundles[e]}_getPreferredOrder(e){for(let t=0;t<e.length;t++){const i=e[t],r=this._preferredOrder.find(n=>n.params.format.includes(i.format));if(r)return r}return this._preferredOrder[0]}_appendDefaultSearchParams(e){if(!this._defaultSearchParams)return e;const t=/\?/.test(e)?"&":"?";return`${e}${t}${this._defaultSearchParams}`}_buildResolvedAsset(e,t){const{aliases:i,data:r,loadParser:n,format:a}=t;return(this._basePath||this._rootPath)&&(e.src=nt.toAbsolute(e.src,this._basePath,this._rootPath)),e.alias=i??e.alias??[e.src],e.src=this._appendDefaultSearchParams(e.src),e.data={...r||{},...e.data},e.loadParser=n??e.loadParser,e.format=a??e.format??ia(e.src),e}}Ii.RETINA_PREFIX=/@([0-9\.]+)x/;function ia(s){return s.split(".").pop().split("?").shift().split("#").shift()}const Xs=(s,e)=>{const t=e.split("?")[1];return t&&(s+=`?${t}`),s},Fi=class et{constructor(e,t){this.linkedSheets=[];let i=e;(e==null?void 0:e.source)instanceof pe&&(i={texture:e,data:t});const{texture:r,data:n,cachePrefix:a=""}=i;this.cachePrefix=a,this._texture=r instanceof B?r:null,this.textureSource=r.source,this.textures={},this.animations={},this.data=n;const o=parseFloat(n.meta.scale);o?(this.resolution=o,r.source.resolution=this.resolution):this.resolution=r.source._resolution,this._frames=this.data.frames,this._frameKeys=Object.keys(this._frames),this._batchIndex=0,this._callback=null}parse(){return new Promise(e=>{this._callback=e,this._batchIndex=0,this._frameKeys.length<=et.BATCH_SIZE?(this._processFrames(0),this._processAnimations(),this._parseComplete()):this._nextBatch()})}_processFrames(e){let t=e;const i=et.BATCH_SIZE;for(;t-e<i&&t<this._frameKeys.length;){const r=this._frameKeys[t],n=this._frames[r],a=n.frame;if(a){let o=null,h=null;const l=n.trimmed!==!1&&n.sourceSize?n.sourceSize:n.frame,c=new ve(0,0,Math.floor(l.w)/this.resolution,Math.floor(l.h)/this.resolution);n.rotated?o=new ve(Math.floor(a.x)/this.resolution,Math.floor(a.y)/this.resolution,Math.floor(a.h)/this.resolution,Math.floor(a.w)/this.resolution):o=new ve(Math.floor(a.x)/this.resolution,Math.floor(a.y)/this.resolution,Math.floor(a.w)/this.resolution,Math.floor(a.h)/this.resolution),n.trimmed!==!1&&n.spriteSourceSize&&(h=new ve(Math.floor(n.spriteSourceSize.x)/this.resolution,Math.floor(n.spriteSourceSize.y)/this.resolution,Math.floor(a.w)/this.resolution,Math.floor(a.h)/this.resolution)),this.textures[r]=new B({source:this.textureSource,frame:o,orig:c,trim:h,rotate:n.rotated?2:0,defaultAnchor:n.anchor,defaultBorders:n.borders,label:r.toString()})}t++}}_processAnimations(){const e=this.data.animations||{};for(const t in e){this.animations[t]=[];for(let i=0;i<e[t].length;i++){const r=e[t][i];this.animations[t].push(this.textures[r])}}}_parseComplete(){const e=this._callback;this._callback=null,this._batchIndex=0,e.call(this,this.textures)}_nextBatch(){this._processFrames(this._batchIndex*et.BATCH_SIZE),this._batchIndex++,setTimeout(()=>{this._batchIndex*et.BATCH_SIZE<this._frameKeys.length?this._nextBatch():(this._processAnimations(),this._parseComplete())},0)}destroy(e=!1){var t;for(const i in this.textures)this.textures[i].destroy();this._frames=null,this._frameKeys=null,this.data=null,this.textures=null,e&&((t=this._texture)==null||t.destroy(),this.textureSource.destroy()),this._texture=null,this.textureSource=null,this.linkedSheets=[]}};Fi.BATCH_SIZE=1e3;let Hs=Fi;const ra=["jpg","png","jpeg","avif","webp","basis","etc2","bc7","bc6h","bc5","bc4","bc3","bc2","bc1","eac","astc"];function Bi(s,e,t){const i={};if(s.forEach(r=>{i[r]=e}),Object.keys(e.textures).forEach(r=>{i[`${e.cachePrefix}${r}`]=e.textures[r]}),!t){const r=nt.dirname(s[0]);e.linkedSheets.forEach((n,a)=>{const o=Bi([`${r}/${e.data.meta.related_multi_packs[a]}`],n,!0);Object.assign(i,o)})}return i}const na={extension:Y.Asset,cache:{test:s=>s instanceof Hs,getCacheableAssets:(s,e)=>Bi(s,e,!1)},resolver:{extension:{type:Y.ResolveParser,name:"resolveSpritesheet"},test:s=>{const t=s.split("?")[0].split("."),i=t.pop(),r=t.pop();return i==="json"&&ra.includes(r)},parse:s=>{var t;const e=s.split(".");return{resolution:parseFloat(((t=Ii.RETINA_PREFIX.exec(s))==null?void 0:t[1])??"1"),format:e[e.length-2],src:s}}},loader:{name:"spritesheetLoader",extension:{type:Y.LoadParser,priority:Pi.Normal,name:"spritesheetLoader"},async testParse(s,e){return nt.extname(e.src).toLowerCase()===".json"&&!!s.frames},async parse(s,e,t){var d,u;const{texture:i,imageFilename:r,textureOptions:n,cachePrefix:a}=(e==null?void 0:e.data)??{};let o=nt.dirname(e.src);o&&o.lastIndexOf("/")!==o.length-1&&(o+="/");let h;if(i instanceof B)h=i;else{const f=Xs(o+(r??s.meta.image),e.src);h=(await t.load([{src:f,data:n}]))[f]}const l=new Hs({texture:h.source,data:s,cachePrefix:a});await l.parse();const c=(d=s==null?void 0:s.meta)==null?void 0:d.related_multi_packs;if(Array.isArray(c)){const f=[];for(const y of c){if(typeof y!="string")continue;let p=o+y;(u=e.data)!=null&&u.ignoreMultiPack||(p=Xs(p,e.src),f.push(t.load({src:p,data:{textureOptions:n,ignoreMultiPack:!0}})))}const m=await Promise.all(f);l.linkedSheets=m,m.forEach(y=>{y.linkedSheets=[l].concat(l.linkedSheets.filter(p=>p!==y))})}return l},async unload(s,e,t){await t.unload(s.textureSource._sourceOrigin),s.destroy(!1)}}};xe.add(na);const ss=[];xe.handleByNamedList(Y.Environment,ss);async function aa(s){if(!s)for(let e=0;e<ss.length;e++){const t=ss[e];if(t.value.test()){await t.value.load();return}}}let Je;function oa(){if(typeof Je=="boolean")return Je;try{Je=new Function("param1","param2","param3","return param1[param2] === param3;")({a:"b"},"a","b")===!0}catch{Je=!1}return Je}var Di=(s=>(s[s.NONE=0]="NONE",s[s.COLOR=16384]="COLOR",s[s.STENCIL=1024]="STENCIL",s[s.DEPTH=256]="DEPTH",s[s.COLOR_DEPTH=16640]="COLOR_DEPTH",s[s.COLOR_STENCIL=17408]="COLOR_STENCIL",s[s.DEPTH_STENCIL=1280]="DEPTH_STENCIL",s[s.ALL=17664]="ALL",s))(Di||{});class ha{constructor(e){this.items=[],this._name=e}emit(e,t,i,r,n,a,o,h){const{name:l,items:c}=this;for(let d=0,u=c.length;d<u;d++)c[d][l](e,t,i,r,n,a,o,h);return this}add(e){return e[this._name]&&(this.remove(e),this.items.push(e)),this}remove(e){const t=this.items.indexOf(e);return t!==-1&&this.items.splice(t,1),this}contains(e){return this.items.indexOf(e)!==-1}removeAll(){return this.items.length=0,this}destroy(){this.removeAll(),this.items=null,this._name=null}get empty(){return this.items.length===0}get name(){return this._name}}const la=["init","destroy","contextChange","resolutionChange","resetState","renderEnd","renderStart","render","update","postrender","prerender"],Gi=class Oi extends ut{constructor(e){super(),this.uid=Ce("renderer"),this.runners=Object.create(null),this.renderPipes=Object.create(null),this._initOptions={},this._systemsHash=Object.create(null),this.type=e.type,this.name=e.name,this.config=e;const t=[...la,...this.config.runners??[]];this._addRunners(...t),this._unsafeEvalCheck()}async init(e={}){const t=e.skipExtensionImports===!0?!0:e.manageImports===!1;await aa(t),this._addSystems(this.config.systems),this._addPipes(this.config.renderPipes,this.config.renderPipeAdaptors);for(const i in this._systemsHash)e={...this._systemsHash[i].constructor.defaultOptions,...e};e={...Oi.defaultOptions,...e},this._roundPixels=e.roundPixels?1:0;for(let i=0;i<this.runners.init.items.length;i++)await this.runners.init.items[i].init(e);this._initOptions=e}render(e,t){let i=e;if(i instanceof Oe&&(i={container:i},t&&(le(Ee,"passing a second argument is deprecated, please use render options instead"),i.target=t.renderTexture)),i.target||(i.target=this.view.renderTarget),i.target===this.view.renderTarget&&(this._lastObjectRendered=i.container,i.clearColor??(i.clearColor=this.background.colorRgba),i.clear??(i.clear=this.background.clearBeforeRender)),i.clearColor){const r=Array.isArray(i.clearColor)&&i.clearColor.length===4;i.clearColor=r?i.clearColor:wt.shared.setValue(i.clearColor).toArray()}i.transform||(i.container.updateLocalTransform(),i.transform=i.container.localTransform),i.container.enableRenderGroup(),this.runners.prerender.emit(i),this.runners.renderStart.emit(i),this.runners.render.emit(i),this.runners.renderEnd.emit(i),this.runners.postrender.emit(i)}resize(e,t,i){const r=this.view.resolution;this.view.resize(e,t,i),this.emit("resize",this.view.screen.width,this.view.screen.height,this.view.resolution),i!==void 0&&i!==r&&this.runners.resolutionChange.emit(i)}clear(e={}){const t=this;e.target||(e.target=t.renderTarget.renderTarget),e.clearColor||(e.clearColor=this.background.colorRgba),e.clear??(e.clear=Di.ALL);const{clear:i,clearColor:r,target:n}=e;wt.shared.setValue(r??this.background.colorRgba),t.renderTarget.clear(n,i,wt.shared.toArray())}get resolution(){return this.view.resolution}set resolution(e){this.view.resolution=e,this.runners.resolutionChange.emit(e)}get width(){return this.view.texture.frame.width}get height(){return this.view.texture.frame.height}get canvas(){return this.view.canvas}get lastObjectRendered(){return this._lastObjectRendered}get renderingToScreen(){return this.renderTarget.renderingToScreen}get screen(){return this.view.screen}_addRunners(...e){e.forEach(t=>{this.runners[t]=new ha(t)})}_addSystems(e){let t;for(t in e){const i=e[t];this._addSystem(i.value,i.name)}}_addSystem(e,t){const i=new e(this);if(this[t])throw new Error(`Whoops! The name "${t}" is already in use`);this[t]=i,this._systemsHash[t]=i;for(const r in this.runners)this.runners[r].add(i);return this}_addPipes(e,t){const i=t.reduce((r,n)=>(r[n.name]=n.value,r),{});e.forEach(r=>{const n=r.value,a=r.name,o=i[a];this.renderPipes[a]=new n(this,o?new o:null)})}destroy(e=!1){this.runners.destroy.items.reverse(),this.runners.destroy.emit(e),Object.values(this.runners).forEach(t=>{t.destroy()}),this._systemsHash=null,this.renderPipes=null}generateTexture(e){return this.textureGenerator.generateTexture(e)}get roundPixels(){return!!this._roundPixels}_unsafeEvalCheck(){if(!oa())throw new Error("Current environment does not allow unsafe-eval, please use pixi.js/unsafe-eval module to enable support.")}resetState(){this.runners.resetState.emit()}};Gi.defaultOptions={resolution:1,failIfMajorPerformanceCaveat:!1,roundPixels:!1};let Li=Gi,xt;function ca(s){return xt!==void 0||(xt=(()=>{var t;const e={stencil:!0,failIfMajorPerformanceCaveat:s??Li.defaultOptions.failIfMajorPerformanceCaveat};try{if(!rt.get().getWebGLRenderingContext())return!1;let r=rt.get().createCanvas().getContext("webgl",e);const n=!!((t=r==null?void 0:r.getContextAttributes())!=null&&t.stencil);if(r){const a=r.getExtension("WEBGL_lose_context");a&&a.loseContext()}return r=null,n}catch{return!1}})()),xt}let bt;async function da(s={}){return bt!==void 0||(bt=await(async()=>{const e=rt.get().getNavigator().gpu;if(!e)return!1;try{return await(await e.requestAdapter(s)).requestDevice(),!0}catch{return!1}})()),bt}const qs=["webgl","webgpu","canvas"];async function ua(s){let e=[];s.preference?(e.push(s.preference),qs.forEach(n=>{n!==s.preference&&e.push(n)})):e=qs.slice();let t,i={};for(let n=0;n<e.length;n++){const a=e[n];if(a==="webgpu"&&await da()){const{WebGPURenderer:o}=await Ct(async()=>{const{WebGPURenderer:h}=await import("./B-WGcoYS.js");return{WebGPURenderer:h}},__vite__mapDeps([4,2,3,5]),import.meta.url);t=o,i={...s,...s.webgpu};break}else if(a==="webgl"&&ca(s.failIfMajorPerformanceCaveat??Li.defaultOptions.failIfMajorPerformanceCaveat)){const{WebGLRenderer:o}=await Ct(async()=>{const{WebGLRenderer:h}=await import("./DkikMKFo.js");return{WebGLRenderer:h}},__vite__mapDeps([6,3,5]),import.meta.url);t=o,i={...s,...s.webgl};break}else if(a==="canvas")throw i={...s},new Error("CanvasRenderer is not yet implemented")}if(delete i.webgpu,delete i.webgl,!t)throw new Error("No available renderer for the current environment");const r=new t;return await r.init(i),r}const Ui="8.11.0";class Ni{static init(){var e;(e=globalThis.__PIXI_APP_INIT__)==null||e.call(globalThis,this,Ui)}static destroy(){}}Ni.extension=Y.Application;class fa{constructor(e){this._renderer=e}init(){var e;(e=globalThis.__PIXI_RENDERER_INIT__)==null||e.call(globalThis,this._renderer,Ui)}destroy(){this._renderer=null}}fa.extension={type:[Y.WebGLSystem,Y.WebGPUSystem],name:"initHook",priority:-10};const Yi=class is{constructor(...e){this.stage=new Oe,e[0]!==void 0&&le(Ee,"Application constructor options are deprecated, please use Application.init() instead.")}async init(e){e={...e},this.renderer=await ua(e),is._plugins.forEach(t=>{t.init.call(this,e)})}render(){this.renderer.render({container:this.stage})}get canvas(){return this.renderer.canvas}get view(){return le(Ee,"Application.view is deprecated, please use Application.canvas instead."),this.renderer.canvas}get screen(){return this.renderer.screen}destroy(e=!1,t=!1){const i=is._plugins.slice(0);i.reverse(),i.forEach(r=>{r.destroy.call(this)}),this.stage.destroy(t),this.stage=null,this.renderer.destroy(e),this.renderer=null}};Yi._plugins=[];let Xi=Yi;xe.handleByList(Y.Application,Xi._plugins);xe.add(Ni);xe.add(jr,zr);function jt(s,e){return Math.random()*(e-s)+s}function pa(s,e){return{left:e.has("ArrowLeft")||e.has("KeyA"),right:e.has("ArrowRight")||e.has("KeyD"),up:e.has("ArrowUp")||e.has("KeyW"),down:e.has("ArrowDown")||e.has("KeyS"),jump:e.has("Space"),interact:e.has("KeyE")||e.has("Enter")}}function ma(s,e,t=10){const i=[];for(let r=0;r<t;r++)i.push({x:s,y:e,vx:jt(-3,3),vy:jt(-5,-1),life:60,maxLife:60,color:16766720,scale:jt(.5,1.5)});return i}function _a(s){return s.map(e=>({...e,x:e.x+e.vx,y:e.y+e.vy,vy:e.vy+.1,life:e.life-1,scale:e.scale*.98})).filter(e=>e.life>0)}var ga=q('<div class="game-canvas-container svelte-ije5yg"><div class="canvas-wrapper svelte-ije5yg"></div> <div class="game-controls svelte-ije5yg"><p class="svelte-ije5yg"><strong class="svelte-ije5yg">Controls:</strong></p> <p class="svelte-ije5yg">Arrow Keys or WASD: Move</p> <p class="svelte-ije5yg">Space: Jump</p> <p class="svelte-ije5yg">Collect golden bananas to earn rewards!</p></div></div>');function ya(s,e){ot(e,!1);let t=P(),i,r=!1,n,a=[],o=[],h,l=new Set,c={x:0,y:0};const d=.5,u=-12,f=5,m=500;_r(async()=>{await y()}),gr(()=>{se()});async function y(){try{i=new Xi,await i.init({width:800,height:600,backgroundColor:2263842,antialias:!0}),_(t).appendChild(i.canvas),await p(),C(),i.ticker.add(S),r=!0,console.log("Game initialized successfully!")}catch(v){console.error("Failed to initialize game:",v)}}async function p(){n=new Me(B.WHITE),n.width=32,n.height=32,n.tint=9127187,n.x=100,n.y=m-32,i.stage.addChild(n),h=new Oe,i.stage.addChild(h),g();const v=new Me(B.WHITE);v.width=800,v.height=20,v.tint=6636321,v.x=0,v.y=m+12,i.stage.addChild(v)}function g(){[{x:200,y:450},{x:400,y:350},{x:600,y:400},{x:300,y:300}].forEach(k=>{const R=new Me(B.WHITE);R.width=20,R.height=20,R.tint=16766720,R.x=k.x,R.y=k.y,a.push(R),i.stage.addChild(R)})}function C(){window.addEventListener("keydown",x),window.addEventListener("keyup",G)}function x(v){l.add(v.code),v.preventDefault()}function G(v){l.delete(v.code),v.preventDefault()}function S(v){if(!r)return;const k=pa({},l);$(k,v.deltaTime),X(),W(),os.updateMonkeyPosition(n.x,n.y)}function $(v,k){v.left?c.x=-f:v.right?c.x=f:c.x*=.8,v.jump&&n.y>=m-32&&(c.y=u),c.y+=d*k,n.x+=c.x*k,n.y+=c.y*k,n.x=Math.max(0,Math.min(n.x,768)),n.y>=m-32&&(n.y=m-32,c.y=0)}function X(){a.forEach(v=>{if(v.visible&&n.x<v.x+v.width&&n.x+n.width>v.x&&n.y<v.y+v.height&&n.y+n.height>v.y){v.visible=!1,ct.addBananas(5);const k=ma(v.x+10,v.y+10,8);o.push(...k),console.log("Banana collected! +5 bananas")}})}function W(){o=_a(o),h.removeChildren(),o.forEach(v=>{const k=new Me(B.WHITE);k.width=4*v.scale,k.height=4*v.scale,k.tint=v.color,k.x=v.x,k.y=v.y,k.alpha=v.life/v.maxLife,h.addChild(k)})}function se(){i&&i.destroy(!0),window.removeEventListener("keydown",x),window.removeEventListener("keyup",G),r=!1}at();var Z=Ks(),Pe=Qs(Z);{var be=v=>{var k=ga(),R=b(k);yr(R,z=>T(t,z),()=>_(t)),lt(2),w(k),O(v,k)};oe(Pe,v=>{v(be)})}O(s,Z),ht()}const qe=class qe{constructor(){}static getInstance(){return qe.instance||(qe.instance=new qe),qe.instance}calculateTaskReward(e){let t=0;switch(e.priority){case"low":t=5;break;case"medium":t=10;break;case"high":t=15;break}return e.description&&e.description.trim().length>0&&(t+=2),e.dueDate&&(t+=1),e.category&&e.category.trim().length>0&&(t+=1),t}applyMultipliers(e,t,i=0){let r=1;if(t.forEach(n=>{if(n.purchased)switch(n.effect){case"banana_boost_25":r+=.25;break;case"banana_boost_50":r+=.5;break;case"double_bananas":r*=2;break}}),i>0){const n=Math.min(i*.05,.5);r+=n}return Math.floor(e*r)}calculateQuestReward(e){let t=e.bananaReward;switch(e.type){case"daily":break;case"weekly":t*=1.5;break;case"achievement":t*=2;break}return Math.floor(t)}calculateFeatureUnlockCost(e,t=1){const r={categories:100,"due-dates":200,"priority-tags":300,"habit-tracking":500,analytics:750,"export-options":1e3,collaboration:1500,"calendar-sync":2e3,"custom-themes":2500}[e]||100,n=1+(t-1)*.1;return Math.floor(r*n)}calculateUpgradeCost(e,t=0){const r={"faster-monkey":250,"banana-bots":500,"double-jump":750,"banana-magnet":1e3,"banana-boost-25":1250,"banana-boost-50":2e3,"auto-complete":3e3}[e]||500,n=Math.pow(1.5,t);return Math.floor(r*n)}calculatePassiveIncome(e){let t=0;return e.forEach(i=>{if(i.purchased)switch(i.effect){case"auto_harvest_1":t+=60;break;case"auto_harvest_5":t+=300;break;case"banana_tree":t+=100;break}}),t}calculateNextMilestone(e,t){const r=[{name:"categories",cost:100},{name:"due-dates",cost:200},{name:"priority-tags",cost:300},{name:"habit-tracking",cost:500},{name:"analytics",cost:750},{name:"export-options",cost:1e3},{name:"collaboration",cost:1500},{name:"calendar-sync",cost:2e3},{name:"custom-themes",cost:2500}].find(n=>!t.includes(n.name)&&n.cost>e);return r?{feature:r.name,cost:r.cost,remaining:r.cost-e}:null}};ds(qe,"instance");let rs=qe;var va=q('<form class="task-form svelte-18kkk17"><h3 class="svelte-18kkk17">Add New Task</h3> <div class="form-group svelte-18kkk17"><label for="title" class="svelte-18kkk17">Task Title *</label> <input id="title" type="text" placeholder="What needs to be done?" required class="svelte-18kkk17"/></div> <div class="form-group svelte-18kkk17"><label for="description" class="svelte-18kkk17">Description</label> <textarea id="description" placeholder="Add more details (optional)" rows="3" class="svelte-18kkk17"></textarea></div> <div class="form-row svelte-18kkk17"><div class="form-group svelte-18kkk17"><label for="priority" class="svelte-18kkk17">Priority</label> <select id="priority" class="svelte-18kkk17"><option>Low</option><option>Medium</option><option>High</option></select></div> <div class="form-group svelte-18kkk17"><label for="category" class="svelte-18kkk17">Category</label> <input id="category" type="text" placeholder="e.g., Work, Personal" class="svelte-18kkk17"/></div> <div class="form-group svelte-18kkk17"><label for="dueDate" class="svelte-18kkk17">Due Date</label> <input id="dueDate" type="date" class="svelte-18kkk17"/></div></div> <div class="reward-preview svelte-18kkk17"><span class="banana-icon svelte-18kkk17">🍌</span> <span class="reward-text svelte-18kkk17"> </span></div> <button type="submit" class="submit-btn svelte-18kkk17">Add Task</button> <p class="form-hint svelte-18kkk17">💡 Tip: Press Ctrl+Enter to quickly add the task</p></form>');function xa(s,e){ot(e,!1);const t=P();let i=P(""),r=P(""),n=P("medium"),a=P(""),o=P("");function h(){if(!_(i).trim())return;const v={title:_(i).trim(),description:_(r).trim(),priority:_(n),category:_(a).trim(),dueDate:_(o)||void 0,completed:!1,bananaReward:_(t)};Te.addTask(v),T(i,""),T(r,""),T(n,"medium"),T(a,""),T(o,"")}function l(v){v.key==="Enter"&&(v.ctrlKey||v.metaKey)&&h()}ke(()=>(_(i),_(r),_(n),_(a),_(o)),()=>{T(t,rs.getInstance().calculateTaskReward({title:_(i),description:_(r),priority:_(n),category:_(a),dueDate:_(o),completed:!1}))}),ns(),at();var c=va(),d=A(b(c),2),u=A(b(d),2);Fe(u),w(d);var f=A(d,2),m=A(b(f),2);Js(m),w(f);var y=A(f,2),p=b(y),g=A(b(p),2);ee(()=>{_(n),kt(()=>{})});var C=b(g);C.value=C.__value="low";var x=A(C);x.value=x.__value="medium";var G=A(x);G.value=G.__value="high",w(g),w(p);var S=A(p,2),$=A(b(S),2);Fe($),w(S);var X=A(S,2),W=A(b(X),2);Fe(W),w(X),w(y);var se=A(y,2),Z=A(b(se),2),Pe=b(Z);w(Z),w(se);var be=A(se,2);lt(2),w(c),ee(v=>{fe(Pe,`Reward: ${_(t)??""} bananas`),be.disabled=v},[()=>(_(i),H(()=>!_(i).trim()))],tt),Be(u,()=>_(i),v=>T(i,v)),ue("keydown",u,l),Be(m,()=>_(r),v=>T(r,v)),ue("keydown",m,l),Mt(g,()=>_(n),v=>T(n,v)),Be($,()=>_(a),v=>T(a,v)),Be(W,()=>_(o),v=>T(o,v)),ue("submit",c,Ir(h)),O(s,c),ht()}var ba=q('<div class="edit-form svelte-1faqkv3"><input type="text" placeholder="Task title" class="edit-title svelte-1faqkv3"/> <textarea placeholder="Description (optional)" class="edit-description svelte-1faqkv3" rows="2"></textarea> <div class="edit-controls svelte-1faqkv3"><select class="edit-priority svelte-1faqkv3"><option>Low</option><option>Medium</option><option>High</option></select> <input type="text" placeholder="Category" class="edit-category svelte-1faqkv3"/> <input type="date" class="edit-due-date svelte-1faqkv3"/></div> <div class="edit-actions svelte-1faqkv3"><button class="save-btn svelte-1faqkv3">Save</button> <button class="cancel-btn svelte-1faqkv3">Cancel</button></div></div>'),wa=q('<p class="task-description svelte-1faqkv3"> </p>'),Aa=q('<span class="category-badge svelte-1faqkv3"> </span>'),ka=q("<span> </span>"),Ca=q('<div class="task-info"><h4> </h4> <!> <div class="task-meta svelte-1faqkv3"><span class="priority-badge svelte-1faqkv3"> </span> <!> <!> <span class="banana-reward svelte-1faqkv3"> </span></div></div>'),Ma=q('<div class="task-actions svelte-1faqkv3"><button class="edit-btn svelte-1faqkv3" title="Edit task">✏️</button> <button class="delete-btn svelte-1faqkv3" title="Delete task">🗑️</button></div>'),Ta=q('<div><div class="task-main svelte-1faqkv3"><label class="checkbox-container svelte-1faqkv3"><input type="checkbox" class="svelte-1faqkv3"/> <span class="checkmark svelte-1faqkv3"></span></label> <div class="task-content svelte-1faqkv3"><!></div> <!></div></div>');function Sa(s,e){ot(e,!1);const t=P(),i=P(),r=P();let n=vr(e,"task",8),a=P(!1),o=P(n().title),h=P(n().description),l=P(n().priority),c=P(n().category),d=P(n().dueDate||"");function u(){n().completed||ct.addBananas(n().bananaReward),Te.updateTask(n().id,{completed:!n().completed})}function f(){T(a,!0),T(o,n().title),T(h,n().description),T(l,n().priority),T(c,n().category),T(d,n().dueDate||"")}function m(){_(o).trim()&&(Te.updateTask(n().id,{title:_(o).trim(),description:_(h).trim(),priority:_(l),category:_(c).trim(),dueDate:_(d)||void 0}),T(a,!1))}function y(){T(a,!1)}function p(){confirm("Are you sure you want to delete this task?")&&Te.deleteTask(n().id)}function g(k){k.key==="Enter"&&(k.ctrlKey||k.metaKey)?m():k.key==="Escape"&&y()}function C(k){switch(k){case"high":return"#EF4444";case"medium":return"#F59E0B";case"low":return"#10B981";default:return"#6B7280"}}ke(()=>ie(n()),()=>{T(t,n().dueDate?new Date(n().dueDate).toLocaleDateString():"")}),ke(()=>ie(n()),()=>{T(i,n().dueDate&&!n().completed&&new Date(n().dueDate)<new Date)}),ke(()=>ie(n()),()=>{T(r,n().dueDate&&!n().completed&&new Date(n().dueDate).getTime()-new Date().getTime()<24*60*60*1e3)}),ns(),at();var x=Ta();let G;var S=b(x),$=b(S),X=b($);Fe(X),lt(2),w($);var W=A($,2),se=b(W);{var Z=k=>{var R=ba(),z=b(R);Fe(z);var ae=A(z,2);Js(ae);var Le=A(ae,2),we=b(Le);ee(()=>{_(l),kt(()=>{})});var Ue=b(we);Ue.value=Ue.__value="low";var M=A(Ue);M.value=M.__value="medium";var E=A(M);E.value=E.__value="high",w(we);var L=A(we,2);Fe(L);var V=A(L,2);Fe(V),w(Le);var me=A(Le,2),K=b(me),ce=A(K,2);w(me),w(R),Be(z,()=>_(o),U=>T(o,U)),ue("keydown",z,g),Be(ae,()=>_(h),U=>T(h,U)),ue("keydown",ae,g),Mt(we,()=>_(l),U=>T(l,U)),Be(L,()=>_(c),U=>T(c,U)),Be(V,()=>_(d),U=>T(d,U)),ue("click",K,m),ue("click",ce,y),O(k,R)},Pe=k=>{var R=Ca(),z=b(R);let ae;var Le=b(z,!0);w(z);var we=A(z,2);{var Ue=I=>{var N=wa(),_e=b(N,!0);w(N),ee(()=>fe(_e,(ie(n()),H(()=>n().description)))),O(I,N)};oe(we,I=>{ie(n()),H(()=>n().description)&&I(Ue)})}var M=A(we,2),E=b(M),L=b(E,!0);w(E);var V=A(E,2);{var me=I=>{var N=Aa(),_e=b(N);w(N),ee(()=>fe(_e,`📂 ${ie(n()),H(()=>n().category)??""}`)),O(I,N)};oe(V,I=>{ie(n()),H(()=>n().category)&&I(me)})}var K=A(V,2);{var ce=I=>{var N=ka();let _e;var Et=b(N);w(N),ee(Re=>{_e=Ft(N,1,"due-date-badge svelte-1faqkv3",null,_e,Re),fe(Et,`📅 ${_(t)??""}`)},[()=>({overdue:_(i),"due-soon":_(r)})],tt),O(I,N)};oe(K,I=>{ie(n()),H(()=>n().dueDate)&&I(ce)})}var U=A(K,2),ze=b(U);w(U),w(M),w(R),ee((I,N)=>{ae=Ft(z,1,"task-title svelte-1faqkv3",null,ae,I),fe(Le,(ie(n()),H(()=>n().title))),Mr(E,`background-color: ${N??""}`),fe(L,(ie(n()),H(()=>n().priority))),fe(ze,`🍌 ${ie(n()),H(()=>n().bananaReward)??""}`)},[()=>({completed:n().completed}),()=>(ie(n()),H(()=>C(n().priority)))],tt),O(k,R)};oe(se,k=>{_(a)?k(Z):k(Pe,!1)})}w(W);var be=A(W,2);{var v=k=>{var R=Ma(),z=b(R),ae=A(z,2);w(R),ue("click",z,f),ue("click",ae,p),O(k,R)};oe(be,k=>{_(a)||k(v)})}w(S),w(x),ee(k=>{G=Ft(x,1,"task-item svelte-1faqkv3",null,G,k),Pr(X,(ie(n()),H(()=>n().completed)))},[()=>({completed:n().completed,overdue:_(i)})],tt),ue("change",X,u),O(s,x),ht()}var Ea=q("<option> </option>"),Pa=q('<div class="control-group svelte-1dkqmps"><label for="filterCategory" class="svelte-1dkqmps">Category:</label> <select id="filterCategory" class="svelte-1dkqmps"><option>All Categories</option><!></select></div>'),Ra=q('<button class="clear-btn svelte-1dkqmps">Clear Completed</button>'),Ia=q('<p class="svelte-1dkqmps">🎯 No tasks yet! Add your first task above to get started.</p>'),Fa=q('<p class="svelte-1dkqmps"> </p>'),Ba=q('<p class="svelte-1dkqmps">✅ No completed tasks to show.</p>'),Da=q('<p class="svelte-1dkqmps">🎉 All tasks completed! Great job!</p>'),Ga=q('<div class="empty-state svelte-1dkqmps"><!></div>'),Oa=q('<div class="task-list-container svelte-1dkqmps"><div class="task-list-header svelte-1dkqmps"><h3 class="svelte-1dkqmps">Tasks <span class="task-count svelte-1dkqmps"> </span></h3> <div class="task-controls svelte-1dkqmps"><div class="control-group svelte-1dkqmps"><label for="sortBy" class="svelte-1dkqmps">Sort by:</label> <select id="sortBy" class="svelte-1dkqmps"><option>Date Created</option><option>Priority</option><option>Due Date</option><option>Title</option></select></div> <!> <div class="control-group svelte-1dkqmps"><label class="checkbox-label svelte-1dkqmps"><input type="checkbox" class="svelte-1dkqmps"/> Show completed</label></div> <!></div></div> <div class="task-list svelte-1dkqmps"><!></div></div>');function La(s,e){ot(e,!1);const[t,i]=Zs(),r=()=>ei(Te,"$taskStore",t),n=P(),a=P(),o=P(),h=P(),l=P();let c=P(!1),d=P("created"),u=P("");function f(){_(o).forEach(M=>{Te.deleteTask(M.id)})}ke(()=>r(),()=>{T(n,r().tasks)}),ke(()=>_(n),()=>{T(a,_(n).filter(M=>!M.completed))}),ke(()=>_(n),()=>{T(o,_(n).filter(M=>M.completed))}),ke(()=>_(n),()=>{T(h,[...new Set(_(n).map(M=>M.category).filter(Boolean))])}),ke(()=>(_(c),_(n),_(a),_(u),_(d)),()=>{T(l,(()=>{let M=_(c)?_(n):_(a);return _(u)&&(M=M.filter(E=>E.category===_(u))),M.sort((E,L)=>{switch(_(d)){case"priority":const V={high:3,medium:2,low:1};return V[L.priority]-V[E.priority];case"dueDate":return!E.dueDate&&!L.dueDate?0:E.dueDate?L.dueDate?new Date(E.dueDate).getTime()-new Date(L.dueDate).getTime():-1:1;case"title":return E.title.localeCompare(L.title);case"created":default:return new Date(L.createdAt).getTime()-new Date(E.createdAt).getTime()}})})())}),ns(),at();var m=Oa(),y=b(m),p=b(y),g=A(b(p)),C=b(g);w(g),w(p);var x=A(p,2),G=b(x),S=A(b(G),2);ee(()=>{_(d),kt(()=>{})});var $=b(S);$.value=$.__value="created";var X=A($);X.value=X.__value="priority";var W=A(X);W.value=W.__value="dueDate";var se=A(W);se.value=se.__value="title",w(S),w(G);var Z=A(G,2);{var Pe=M=>{var E=Pa(),L=A(b(E),2);ee(()=>{_(u),kt(()=>{_(h)})});var V=b(L);V.value=V.__value="";var me=A(V);_s(me,1,()=>_(h),xr,(K,ce)=>{var U=Ea(),ze=b(U,!0);w(U);var I={};ee(()=>{fe(ze,_(ce)),I!==(I=_(ce))&&(U.value=(U.__value=_(ce))??"")}),O(K,U)}),w(L),w(E),Mt(L,()=>_(u),K=>T(u,K)),O(M,E)};oe(Z,M=>{_(h),H(()=>_(h).length>0)&&M(Pe)})}var be=A(Z,2),v=b(be),k=b(v);Fe(k),lt(),w(v),w(be);var R=A(be,2);{var z=M=>{var E=Ra();ue("click",E,f),O(M,E)};oe(R,M=>{_(o),H(()=>_(o).length>0)&&M(z)})}w(x),w(y);var ae=A(y,2),Le=b(ae);{var we=M=>{var E=Ga(),L=b(E);{var V=K=>{var ce=Ia();O(K,ce)},me=(K,ce)=>{{var U=I=>{var N=Fa(),_e=b(N);w(N),ee(()=>fe(_e,`📂 No tasks found in "${_(u)??""}" category.`)),O(I,N)},ze=(I,N)=>{{var _e=Re=>{var Pt=Ba();O(Re,Pt)},Et=Re=>{var Pt=Da();O(Re,Pt)};oe(I,Re=>{_(c)?Re(_e):Re(Et,!1)},N)}};oe(K,I=>{_(u)?I(U):I(ze,!1)},ce)}};oe(L,K=>{_(n),H(()=>_(n).length===0)?K(V):K(me,!1)})}w(E),O(M,E)},Ue=M=>{var E=Ks(),L=Qs(E);_s(L,1,()=>_(l),V=>V.id,(V,me)=>{Sa(V,{get task(){return _(me)}})}),O(M,E)};oe(Le,M=>{_(l),H(()=>_(l).length===0)?M(we):M(Ue,!1)})}w(ae),w(m),ee(()=>fe(C,`(${_(a),H(()=>_(a).length)??""} pending${_(o),H(()=>_(o).length>0?`, ${_(o).length} completed`:"")??""})`)),Mt(S,()=>_(d),M=>T(d,M)),Rr(k,()=>_(c),M=>T(c,M)),O(s,m),ht(),i()}var Ua=q('<main class="container svelte-1591osu"><header class="header svelte-1591osu"><h1 class="svelte-1591osu">🍌 Banana Checklist</h1> <div class="banana-counter svelte-1591osu"><span class="banana-icon svelte-1591osu">🍌</span> <span class="banana-count"> </span></div></header> <div class="app-layout svelte-1591osu"><section class="task-section svelte-1591osu"><!> <!></section> <section class="game-section svelte-1591osu"><h2 class="svelte-1591osu">🎮 Monkey Adventure</h2> <!></section></div> <footer class="footer svelte-1591osu"><p>Complete tasks to earn bananas and unlock new features!</p></footer></main>');function ja(s,e){ot(e,!1);const[t,i]=Zs(),r=()=>ei(ct,"$userStore",t);at();var n=Ua(),a=b(n),o=A(b(a),2),h=A(b(o),2),l=b(h,!0);w(h),w(o),w(a);var c=A(a,2),d=b(c),u=b(d);xa(u,{});var f=A(u,2);La(f,{}),w(d);var m=A(d,2),y=A(b(m),2);ya(y,{}),w(m),w(c),lt(2),w(n),ee(()=>fe(l,r().bananaCount)),O(s,n),ht(),i()}export{Li as A,Tt as B,Oe as C,rt as D,Y as E,Gs as F,Kr as G,Pn as H,Mi as I,Ln as J,Va as K,hi as L,D as M,Ve as N,$n as O,ye as P,ln as Q,ve as R,Me as S,B as T,es as U,Ui as V,ja as W,Wa as _,vt as a,ut as b,Di as c,Ci as d,xe as e,pe as f,hn as g,pi as h,Ge as i,Yn as j,Ce as k,ha as l,Ls as m,As as n,$t as o,Us as p,qn as q,wn as r,wt as s,_i as t,oa as u,le as v,De as w,Ee as x,fa as y,an as z};
