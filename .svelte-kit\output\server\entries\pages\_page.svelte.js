import { I as current_component, D as pop, z as push, J as attr, G as escape_html, K as maybe_selected, M as attr_class, N as attr_style, O as stringify, P as bind_props, Q as store_get, R as ensure_array_like, S as unsubscribe_stores } from "../../chunks/index.js";
import { d as derived, w as writable } from "../../chunks/index2.js";
import "clsx";
import "pixi.js";
function onDestroy(fn) {
  var context = (
    /** @type {Component} */
    current_component
  );
  (context.d ??= []).push(fn);
}
function createTaskStore() {
  const { subscribe, set, update } = writable([]);
  return {
    subscribe,
    set,
    add: (task) => {
      const newTask = {
        ...task,
        id: crypto.randomUUID(),
        createdAt: /* @__PURE__ */ new Date()
      };
      update((tasks) => [...tasks, newTask]);
      return newTask;
    },
    update: (id, updates) => {
      update(
        (tasks) => tasks.map(
          (task) => task.id === id ? { ...task, ...updates } : task
        )
      );
    },
    complete: (id) => {
      update(
        (tasks) => tasks.map(
          (task) => task.id === id ? { ...task, completed: true, completedAt: /* @__PURE__ */ new Date() } : task
        )
      );
    },
    delete: (id) => {
      update((tasks) => tasks.filter((task) => task.id !== id));
    },
    clear: () => set([])
  };
}
function createGoalStore() {
  const { subscribe, set, update } = writable([]);
  return {
    subscribe,
    set,
    add: (goal) => {
      const newGoal = {
        ...goal,
        id: crypto.randomUUID(),
        createdAt: /* @__PURE__ */ new Date()
      };
      update((goals) => [...goals, newGoal]);
      return newGoal;
    },
    update: (id, updates) => {
      update(
        (goals) => goals.map(
          (goal) => goal.id === id ? { ...goal, ...updates } : goal
        )
      );
    },
    delete: (id) => {
      update((goals) => goals.filter((goal) => goal.id !== id));
    },
    clear: () => set([])
  };
}
const taskStore = createTaskStore();
createGoalStore();
const completedTasks = derived(
  taskStore,
  ($tasks) => $tasks.filter((task) => task.completed)
);
derived(
  taskStore,
  ($tasks) => $tasks.filter((task) => !task.completed)
);
derived(
  taskStore,
  ($tasks) => {
    const categories = {};
    $tasks.forEach((task) => {
      const category = task.category || "Uncategorized";
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(task);
    });
    return categories;
  }
);
derived(
  completedTasks,
  ($completedTasks) => $completedTasks.reduce((total, task) => total + task.bananaReward, 0)
);
const defaultUser = {
  id: crypto.randomUUID(),
  bananaCount: 0,
  totalTasksCompleted: 0,
  totalGoalsCompleted: 0,
  unlockedFeatures: ["basic-tasks"],
  // Start with basic task functionality
  isPremium: false,
  createdAt: /* @__PURE__ */ new Date(),
  lastActiveAt: /* @__PURE__ */ new Date()
};
function createUserStore() {
  const { subscribe, set, update } = writable(defaultUser);
  return {
    subscribe,
    set,
    addBananas: (amount) => {
      update((user) => ({
        ...user,
        bananaCount: user.bananaCount + amount,
        lastActiveAt: /* @__PURE__ */ new Date()
      }));
    },
    spendBananas: (amount) => {
      update((user) => ({
        ...user,
        bananaCount: Math.max(0, user.bananaCount - amount),
        lastActiveAt: /* @__PURE__ */ new Date()
      }));
    },
    incrementTasksCompleted: () => {
      update((user) => ({
        ...user,
        totalTasksCompleted: user.totalTasksCompleted + 1,
        lastActiveAt: /* @__PURE__ */ new Date()
      }));
    },
    incrementGoalsCompleted: () => {
      update((user) => ({
        ...user,
        totalGoalsCompleted: user.totalGoalsCompleted + 1,
        lastActiveAt: /* @__PURE__ */ new Date()
      }));
    },
    unlockFeature: (feature) => {
      update((user) => ({
        ...user,
        unlockedFeatures: [...user.unlockedFeatures, feature],
        lastActiveAt: /* @__PURE__ */ new Date()
      }));
    },
    setPremium: (isPremium) => {
      update((user) => ({
        ...user,
        isPremium,
        lastActiveAt: /* @__PURE__ */ new Date()
      }));
    },
    updateLastActive: () => {
      update((user) => ({
        ...user,
        lastActiveAt: /* @__PURE__ */ new Date()
      }));
    }
  };
}
const userStore = createUserStore();
const FEATURE_COSTS = {
  "categories": 100,
  "due-dates": 200,
  "priority-tags": 300,
  "habit-tracking": 500,
  "analytics": 750,
  "export-options": 1e3,
  "collaboration": 1500,
  "calendar-sync": 2e3,
  "custom-themes": 2500
};
derived(
  userStore,
  ($user) => (feature) => {
    const cost = FEATURE_COSTS[feature];
    return $user.bananaCount >= cost;
  }
);
derived(
  userStore,
  ($user) => (feature) => {
    return $user.isPremium || $user.unlockedFeatures.includes(feature);
  }
);
const defaultGameState = {
  monkeyPosition: { x: 100, y: 500 },
  currentScene: "jungle",
  unlockedAreas: ["starting-grove"],
  cosmetics: {
    monkeySkin: "default",
    theme: "jungle"
  },
  upgrades: []
};
const defaultInput = {
  left: false,
  right: false,
  up: false,
  down: false,
  jump: false,
  interact: false
};
function createGameStore() {
  const { subscribe, set, update } = writable(defaultGameState);
  return {
    subscribe,
    set,
    updateMonkeyPosition: (x, y) => {
      update((state) => ({
        ...state,
        monkeyPosition: { x, y }
      }));
    },
    changeScene: (scene) => {
      update((state) => ({
        ...state,
        currentScene: scene
      }));
    },
    unlockArea: (area) => {
      update((state) => ({
        ...state,
        unlockedAreas: [...state.unlockedAreas, area]
      }));
    },
    updateCosmetics: (cosmetics) => {
      update((state) => ({
        ...state,
        cosmetics: { ...state.cosmetics, ...cosmetics }
      }));
    },
    purchaseUpgrade: (upgrade) => {
      update((state) => ({
        ...state,
        upgrades: [...state.upgrades, { ...upgrade, purchased: true }]
      }));
    }
  };
}
function createInputStore() {
  const { subscribe, set, update } = writable(defaultInput);
  return {
    subscribe,
    set,
    setKey: (key, pressed) => {
      update((input) => ({
        ...input,
        [key]: pressed
      }));
    },
    reset: () => set(defaultInput)
  };
}
const gameStore = createGameStore();
createInputStore();
const GAME_UPGRADES = [
  {
    id: "faster-monkey",
    name: "Faster Monkey",
    description: "+10% movement speed",
    cost: 250,
    purchased: false,
    effect: "speed_boost_10"
  },
  {
    id: "banana-bots",
    name: "Banana Bots",
    description: "Auto-harvest 1 banana per minute",
    cost: 500,
    purchased: false,
    effect: "auto_harvest_1"
  },
  {
    id: "double-jump",
    name: "Double Jump",
    description: "Jump twice in mid-air",
    cost: 750,
    purchased: false,
    effect: "double_jump"
  },
  {
    id: "banana-magnet",
    name: "Banana Magnet",
    description: "Automatically collect nearby bananas",
    cost: 1e3,
    purchased: false,
    effect: "auto_collect"
  }
];
derived(
  gameStore,
  ($gameState) => $gameState.upgrades.filter((upgrade) => upgrade.purchased)
);
derived(
  gameStore,
  ($gameState) => {
    const purchasedIds = $gameState.upgrades.map((u) => u.id);
    return GAME_UPGRADES.filter((upgrade) => !purchasedIds.includes(upgrade.id));
  }
);
const defaultSettings = {
  soundEnabled: true,
  musicEnabled: true,
  retroFilter: false,
  highContrast: false,
  animationsEnabled: true
};
function createSettingsStore() {
  const { subscribe, set, update } = writable(defaultSettings);
  return {
    subscribe,
    set,
    toggleSound: () => {
      update((settings) => ({
        ...settings,
        soundEnabled: !settings.soundEnabled
      }));
    },
    toggleMusic: () => {
      update((settings) => ({
        ...settings,
        musicEnabled: !settings.musicEnabled
      }));
    },
    toggleRetroFilter: () => {
      update((settings) => ({
        ...settings,
        retroFilter: !settings.retroFilter
      }));
    },
    toggleHighContrast: () => {
      update((settings) => ({
        ...settings,
        highContrast: !settings.highContrast
      }));
    },
    toggleAnimations: () => {
      update((settings) => ({
        ...settings,
        animationsEnabled: !settings.animationsEnabled
      }));
    },
    updateSetting: (key, value) => {
      update((settings) => ({
        ...settings,
        [key]: value
      }));
    }
  };
}
createSettingsStore();
const defaultQuests = [
  {
    id: "daily-tasks-3",
    title: "Daily Productivity",
    description: "Complete 3 tasks today",
    type: "daily",
    requirements: [
      {
        type: "complete_tasks",
        target: 3,
        current: 0
      }
    ],
    bananaReward: 25,
    completed: false,
    progress: 0,
    maxProgress: 3
  },
  {
    id: "weekly-streak-7",
    title: "Consistency Champion",
    description: "Maintain a 7-day task completion streak",
    type: "weekly",
    requirements: [
      {
        type: "maintain_streak",
        target: 7,
        current: 0
      }
    ],
    bananaReward: 100,
    completed: false,
    progress: 0,
    maxProgress: 7
  },
  {
    id: "achievement-100-tasks",
    title: "Century Club",
    description: "Complete 100 tasks total",
    type: "achievement",
    requirements: [
      {
        type: "complete_tasks",
        target: 100,
        current: 0
      }
    ],
    bananaReward: 500,
    completed: false,
    progress: 0,
    maxProgress: 100
  }
];
function createQuestStore() {
  const { subscribe, set, update } = writable(defaultQuests);
  return {
    subscribe,
    set,
    updateProgress: (questId, progress) => {
      update(
        (quests) => quests.map((quest) => {
          if (quest.id === questId) {
            const newProgress = Math.min(progress, quest.maxProgress);
            const completed = newProgress >= quest.maxProgress;
            return {
              ...quest,
              progress: newProgress,
              completed,
              requirements: quest.requirements.map((req) => ({
                ...req,
                current: newProgress
              }))
            };
          }
          return quest;
        })
      );
    },
    completeQuest: (questId) => {
      update(
        (quests) => quests.map(
          (quest) => quest.id === questId ? { ...quest, completed: true, progress: quest.maxProgress } : quest
        )
      );
    },
    resetDailyQuests: () => {
      update(
        (quests) => quests.map(
          (quest) => quest.type === "daily" ? {
            ...quest,
            completed: false,
            progress: 0,
            requirements: quest.requirements.map((req) => ({ ...req, current: 0 }))
          } : quest
        )
      );
    },
    resetWeeklyQuests: () => {
      update(
        (quests) => quests.map(
          (quest) => quest.type === "weekly" ? {
            ...quest,
            completed: false,
            progress: 0,
            requirements: quest.requirements.map((req) => ({ ...req, current: 0 }))
          } : quest
        )
      );
    }
  };
}
const questStore = createQuestStore();
derived(
  questStore,
  ($quests) => $quests.filter((quest) => !quest.completed)
);
derived(
  questStore,
  ($quests) => $quests.filter((quest) => quest.completed)
);
derived(
  questStore,
  ($quests) => $quests.filter((quest) => quest.type === "daily")
);
derived(
  questStore,
  ($quests) => $quests.filter((quest) => quest.type === "weekly")
);
derived(
  questStore,
  ($quests) => $quests.filter((quest) => quest.type === "achievement")
);
function GameCanvas($$payload, $$props) {
  push();
  onDestroy(() => {
  });
  {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="game-loading svelte-ije5yg"><p>🐒 Loading jungle adventure...</p></div>`;
  }
  $$payload.out += `<!--]-->`;
  pop();
}
class BananaCalculator {
  static instance;
  constructor() {
  }
  static getInstance() {
    if (!BananaCalculator.instance) {
      BananaCalculator.instance = new BananaCalculator();
    }
    return BananaCalculator.instance;
  }
  /**
   * Calculate base banana reward for a task
   */
  calculateTaskReward(task) {
    let baseReward = 0;
    switch (task.priority) {
      case "low":
        baseReward = 5;
        break;
      case "medium":
        baseReward = 10;
        break;
      case "high":
        baseReward = 15;
        break;
    }
    if (task.description && task.description.trim().length > 0) {
      baseReward += 2;
    }
    if (task.dueDate) {
      baseReward += 1;
    }
    if (task.category && task.category.trim().length > 0) {
      baseReward += 1;
    }
    return baseReward;
  }
  /**
   * Apply multipliers based on user upgrades and streaks
   */
  applyMultipliers(baseReward, upgrades, streakDays = 0) {
    let multiplier = 1;
    upgrades.forEach((upgrade) => {
      if (upgrade.purchased) {
        switch (upgrade.effect) {
          case "banana_boost_25":
            multiplier += 0.25;
            break;
          case "banana_boost_50":
            multiplier += 0.5;
            break;
          case "double_bananas":
            multiplier *= 2;
            break;
        }
      }
    });
    if (streakDays > 0) {
      const streakBonus = Math.min(streakDays * 0.05, 0.5);
      multiplier += streakBonus;
    }
    return Math.floor(baseReward * multiplier);
  }
  /**
   * Calculate quest completion reward
   */
  calculateQuestReward(quest) {
    let baseReward = quest.bananaReward;
    switch (quest.type) {
      case "daily":
        break;
      case "weekly":
        baseReward *= 1.5;
        break;
      case "achievement":
        baseReward *= 2;
        break;
    }
    return Math.floor(baseReward);
  }
  /**
   * Calculate cost for unlocking features
   */
  calculateFeatureUnlockCost(feature, userLevel = 1) {
    const baseCosts = {
      "categories": 100,
      "due-dates": 200,
      "priority-tags": 300,
      "habit-tracking": 500,
      "analytics": 750,
      "export-options": 1e3,
      "collaboration": 1500,
      "calendar-sync": 2e3,
      "custom-themes": 2500
    };
    const baseCost = baseCosts[feature] || 100;
    const levelMultiplier = 1 + (userLevel - 1) * 0.1;
    return Math.floor(baseCost * levelMultiplier);
  }
  /**
   * Calculate upgrade costs with scaling
   */
  calculateUpgradeCost(upgradeId, currentLevel = 0) {
    const baseCosts = {
      "faster-monkey": 250,
      "banana-bots": 500,
      "double-jump": 750,
      "banana-magnet": 1e3,
      "banana-boost-25": 1250,
      "banana-boost-50": 2e3,
      "auto-complete": 3e3
    };
    const baseCost = baseCosts[upgradeId] || 500;
    const scalingFactor = Math.pow(1.5, currentLevel);
    return Math.floor(baseCost * scalingFactor);
  }
  /**
   * Calculate daily banana income from passive upgrades
   */
  calculatePassiveIncome(upgrades) {
    let dailyIncome = 0;
    upgrades.forEach((upgrade) => {
      if (upgrade.purchased) {
        switch (upgrade.effect) {
          case "auto_harvest_1":
            dailyIncome += 60;
            break;
          case "auto_harvest_5":
            dailyIncome += 300;
            break;
          case "banana_tree":
            dailyIncome += 100;
            break;
        }
      }
    });
    return dailyIncome;
  }
  /**
   * Calculate total bananas needed for next major unlock
   */
  calculateNextMilestone(currentBananas, unlockedFeatures) {
    const allFeatures = [
      { name: "categories", cost: 100 },
      { name: "due-dates", cost: 200 },
      { name: "priority-tags", cost: 300 },
      { name: "habit-tracking", cost: 500 },
      { name: "analytics", cost: 750 },
      { name: "export-options", cost: 1e3 },
      { name: "collaboration", cost: 1500 },
      { name: "calendar-sync", cost: 2e3 },
      { name: "custom-themes", cost: 2500 }
    ];
    const nextFeature = allFeatures.find(
      (feature) => !unlockedFeatures.includes(feature.name) && feature.cost > currentBananas
    );
    if (!nextFeature) return null;
    return {
      feature: nextFeature.name,
      cost: nextFeature.cost,
      remaining: nextFeature.cost - currentBananas
    };
  }
}
function TaskForm($$payload, $$props) {
  push();
  let bananaReward;
  let title = "";
  let description = "";
  let priority = "medium";
  let category = "";
  let dueDate = "";
  bananaReward = BananaCalculator.getInstance().calculateTaskReward({
    title,
    description,
    priority,
    category,
    dueDate,
    completed: false
  });
  $$payload.out += `<form class="task-form svelte-18kkk17"><h3 class="svelte-18kkk17">Add New Task</h3> <div class="form-group svelte-18kkk17"><label for="title" class="svelte-18kkk17">Task Title *</label> <input id="title" type="text"${attr("value", title)} placeholder="What needs to be done?" required class="svelte-18kkk17"/></div> <div class="form-group svelte-18kkk17"><label for="description" class="svelte-18kkk17">Description</label> <textarea id="description" placeholder="Add more details (optional)" rows="3" class="svelte-18kkk17">`;
  const $$body = escape_html(description);
  if ($$body) {
    $$payload.out += `${$$body}`;
  }
  $$payload.out += `</textarea></div> <div class="form-row svelte-18kkk17"><div class="form-group svelte-18kkk17"><label for="priority" class="svelte-18kkk17">Priority</label> <select id="priority" class="svelte-18kkk17">`;
  $$payload.select_value = priority;
  $$payload.out += `<option value="low"${maybe_selected($$payload, "low")}>Low</option><option value="medium"${maybe_selected($$payload, "medium")}>Medium</option><option value="high"${maybe_selected($$payload, "high")}>High</option>`;
  $$payload.select_value = void 0;
  $$payload.out += `</select></div> <div class="form-group svelte-18kkk17"><label for="category" class="svelte-18kkk17">Category</label> <input id="category" type="text"${attr("value", category)} placeholder="e.g., Work, Personal" class="svelte-18kkk17"/></div> <div class="form-group svelte-18kkk17"><label for="dueDate" class="svelte-18kkk17">Due Date</label> <input id="dueDate" type="date"${attr("value", dueDate)} class="svelte-18kkk17"/></div></div> <div class="reward-preview svelte-18kkk17"><span class="banana-icon svelte-18kkk17">🍌</span> <span class="reward-text svelte-18kkk17">Reward: ${escape_html(bananaReward)} bananas</span></div> <button type="submit"${attr("disabled", !title.trim(), true)} class="submit-btn svelte-18kkk17">Add Task</button> <p class="form-hint svelte-18kkk17">💡 Tip: Press Ctrl+Enter to quickly add the task</p></form>`;
  pop();
}
function TaskItem($$payload, $$props) {
  push();
  let formattedDueDate, isOverdue, isDueSoon;
  let task = $$props["task"];
  task.title;
  task.description;
  task.priority;
  task.category;
  task.dueDate || "";
  function getPriorityColor(priority) {
    switch (priority) {
      case "high":
        return "#EF4444";
      case "medium":
        return "#F59E0B";
      case "low":
        return "#10B981";
      default:
        return "#6B7280";
    }
  }
  formattedDueDate = task.dueDate ? new Date(task.dueDate).toLocaleDateString() : "";
  isOverdue = task.dueDate && !task.completed && new Date(task.dueDate) < /* @__PURE__ */ new Date();
  isDueSoon = task.dueDate && !task.completed && new Date(task.dueDate).getTime() - (/* @__PURE__ */ new Date()).getTime() < 24 * 60 * 60 * 1e3;
  $$payload.out += `<div${attr_class("task-item svelte-1faqkv3", void 0, { "completed": task.completed, "overdue": isOverdue })}><div class="task-main svelte-1faqkv3"><label class="checkbox-container svelte-1faqkv3"><input type="checkbox"${attr("checked", task.completed, true)} class="svelte-1faqkv3"/> <span class="checkmark svelte-1faqkv3"></span></label> <div class="task-content svelte-1faqkv3">`;
  {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="task-info"><h4${attr_class("task-title svelte-1faqkv3", void 0, { "completed": task.completed })}>${escape_html(task.title)}</h4> `;
    if (task.description) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<p class="task-description svelte-1faqkv3">${escape_html(task.description)}</p>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> <div class="task-meta svelte-1faqkv3"><span class="priority-badge svelte-1faqkv3"${attr_style(`background-color: ${stringify(getPriorityColor(task.priority))}`)}>${escape_html(task.priority)}</span> `;
    if (task.category) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<span class="category-badge svelte-1faqkv3">📂 ${escape_html(task.category)}</span>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (task.dueDate) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<span${attr_class("due-date-badge svelte-1faqkv3", void 0, { "overdue": isOverdue, "due-soon": isDueSoon })}>📅 ${escape_html(formattedDueDate)}</span>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> <span class="banana-reward svelte-1faqkv3">🍌 ${escape_html(task.bananaReward)}</span></div></div>`;
  }
  $$payload.out += `<!--]--></div> `;
  {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="task-actions svelte-1faqkv3"><button class="edit-btn svelte-1faqkv3" title="Edit task">✏️</button> <button class="delete-btn svelte-1faqkv3" title="Delete task">🗑️</button></div>`;
  }
  $$payload.out += `<!--]--></div></div>`;
  bind_props($$props, { task });
  pop();
}
function TaskList($$payload, $$props) {
  push();
  var $$store_subs;
  let tasks, pendingTasks, completedTasks2, categories, filteredTasks;
  let showCompleted = false;
  let sortBy = "created";
  let filterCategory = "";
  tasks = store_get($$store_subs ??= {}, "$taskStore", taskStore).tasks;
  pendingTasks = tasks.filter((task) => !task.completed);
  completedTasks2 = tasks.filter((task) => task.completed);
  categories = [
    ...new Set(tasks.map((task) => task.category).filter(Boolean))
  ];
  filteredTasks = (() => {
    let filtered = pendingTasks;
    return filtered.sort((a, b) => {
      switch (sortBy) {
        case "priority":
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        case "dueDate":
          if (!a.dueDate && !b.dueDate) return 0;
          if (!a.dueDate) return 1;
          if (!b.dueDate) return -1;
          return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
        case "title":
          return a.title.localeCompare(b.title);
        case "created":
        default:
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    });
  })();
  $$payload.out += `<div class="task-list-container svelte-1dkqmps"><div class="task-list-header svelte-1dkqmps"><h3 class="svelte-1dkqmps">Tasks <span class="task-count svelte-1dkqmps">(${escape_html(pendingTasks.length)} pending${escape_html(completedTasks2.length > 0 ? `, ${completedTasks2.length} completed` : "")})</span></h3> <div class="task-controls svelte-1dkqmps"><div class="control-group svelte-1dkqmps"><label for="sortBy" class="svelte-1dkqmps">Sort by:</label> <select id="sortBy" class="svelte-1dkqmps">`;
  $$payload.select_value = sortBy;
  $$payload.out += `<option value="created"${maybe_selected($$payload, "created")}>Date Created</option><option value="priority"${maybe_selected($$payload, "priority")}>Priority</option><option value="dueDate"${maybe_selected($$payload, "dueDate")}>Due Date</option><option value="title"${maybe_selected($$payload, "title")}>Title</option>`;
  $$payload.select_value = void 0;
  $$payload.out += `</select></div> `;
  if (categories.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(categories);
    $$payload.out += `<div class="control-group svelte-1dkqmps"><label for="filterCategory" class="svelte-1dkqmps">Category:</label> <select id="filterCategory" class="svelte-1dkqmps">`;
    $$payload.select_value = filterCategory;
    $$payload.out += `<option value=""${maybe_selected($$payload, "")}>All Categories</option><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let category = each_array[$$index];
      $$payload.out += `<option${attr("value", category)}${maybe_selected($$payload, category)}>${escape_html(category)}</option>`;
    }
    $$payload.out += `<!--]-->`;
    $$payload.select_value = void 0;
    $$payload.out += `</select></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="control-group svelte-1dkqmps"><label class="checkbox-label svelte-1dkqmps"><input type="checkbox"${attr("checked", showCompleted, true)} class="svelte-1dkqmps"/> Show completed</label></div> `;
  if (completedTasks2.length > 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<button class="clear-btn svelte-1dkqmps">Clear Completed</button>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div> <div class="task-list svelte-1dkqmps">`;
  if (filteredTasks.length === 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="empty-state svelte-1dkqmps">`;
    if (tasks.length === 0) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<p class="svelte-1dkqmps">🎯 No tasks yet! Add your first task above to get started.</p>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<p class="svelte-1dkqmps">🎉 All tasks completed! Great job!</p>`;
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array_1 = ensure_array_like(filteredTasks);
    $$payload.out += `<!--[-->`;
    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
      let task = each_array_1[$$index_1];
      TaskItem($$payload, { task });
    }
    $$payload.out += `<!--]-->`;
  }
  $$payload.out += `<!--]--></div></div>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  $$payload.out += `<main class="container svelte-1591osu"><header class="header svelte-1591osu"><h1 class="svelte-1591osu">🍌 Banana Checklist</h1> <div class="banana-counter svelte-1591osu"><span class="banana-icon svelte-1591osu">🍌</span> <span class="banana-count">${escape_html(store_get($$store_subs ??= {}, "$userStore", userStore).bananaCount)}</span></div></header> <div class="app-layout svelte-1591osu"><section class="task-section svelte-1591osu">`;
  TaskForm($$payload);
  $$payload.out += `<!----> `;
  TaskList($$payload);
  $$payload.out += `<!----></section> <section class="game-section svelte-1591osu"><h2 class="svelte-1591osu">🎮 Monkey Adventure</h2> `;
  GameCanvas($$payload);
  $$payload.out += `<!----></section></div> <footer class="footer svelte-1591osu"><p>Complete tasks to earn bananas and unlock new features!</p></footer></main>`;
  if ($$store_subs) unsubscribe_stores($$store_subs);
  pop();
}
export {
  _page as default
};
