import{E as B,U as wn,a as Ee,b as be,v as A,x as I,D as dt,k as Kt,z as Tn,s as L,T as E,I as ue,M as H,G as Pn,w as Y,R as $,H as vn,P as gt,B as tt,e as V,J as Cn,K as Mn,i as Zt,L as Ft,N as q,j as X,O as kn,n as Ve,Q as Fn}from"./DYMbuX_3.js";import{g as Bn,C as xt}from"./DYELU7KU.js";import{D as Rn,U as mt,B as vr,p as Se,R as we,c as Ct,b as K,S as jt,o as Qt,y as Te,m as de,V as Lt,i as Pe,k as Gn,G as Un,e as Cr,j as An,r as Mr,s as kr,v as zn,w as Fr,g as Wn,h as Ln,t as Dn,u as In,x as Hn,n as Br}from"./BYziMHbo.js";function Oe(i,t,e=2){const r=t&&t.length,n=r?t[0]*e:i.length;let s=Rr(i,0,n,e,!0);const o=[];if(!s||s.next===s.prev)return o;let a,l,c;if(r&&(s=jn(i,t,s,e)),i.length>80*e){a=1/0,l=1/0;let h=-1/0,u=-1/0;for(let f=e;f<n;f+=e){const d=i[f],p=i[f+1];d<a&&(a=d),p<l&&(l=p),d>h&&(h=d),p>u&&(u=p)}c=Math.max(h-a,u-l),c=c!==0?32767/c:0}return Bt(s,o,e,a,l,c,0),o}function Rr(i,t,e,r,n){let s;if(n===ri(i,t,e,r)>0)for(let o=t;o<e;o+=r)s=$e(o/r|0,i[o],i[o+1],s);else for(let o=e-r;o>=t;o-=r)s=$e(o/r|0,i[o],i[o+1],s);return s&&yt(s,s.next)&&(Gt(s),s=s.next),s}function ft(i,t){if(!i)return i;t||(t=i);let e=i,r;do if(r=!1,!e.steiner&&(yt(e,e.next)||W(e.prev,e,e.next)===0)){if(Gt(e),e=t=e.prev,e===e.next)break;r=!0}else e=e.next;while(r||e!==t);return t}function Bt(i,t,e,r,n,s,o){if(!i)return;!o&&s&&Kn(i,r,n,s);let a=i;for(;i.prev!==i.next;){const l=i.prev,c=i.next;if(s?Vn(i,r,n,s):En(i)){t.push(l.i,i.i,c.i),Gt(i),i=c.next,a=c.next;continue}if(i=c,i===a){o?o===1?(i=On(ft(i),t),Bt(i,t,e,r,n,s,2)):o===2&&$n(i,t,e,r,n,s):Bt(ft(i),t,e,r,n,s,1);break}}}function En(i){const t=i.prev,e=i,r=i.next;if(W(t,e,r)>=0)return!1;const n=t.x,s=e.x,o=r.x,a=t.y,l=e.y,c=r.y,h=Math.min(n,s,o),u=Math.min(a,l,c),f=Math.max(n,s,o),d=Math.max(a,l,c);let p=r.next;for(;p!==t;){if(p.x>=h&&p.x<=f&&p.y>=u&&p.y<=d&&vt(n,a,s,l,o,c,p.x,p.y)&&W(p.prev,p,p.next)>=0)return!1;p=p.next}return!0}function Vn(i,t,e,r){const n=i.prev,s=i,o=i.next;if(W(n,s,o)>=0)return!1;const a=n.x,l=s.x,c=o.x,h=n.y,u=s.y,f=o.y,d=Math.min(a,l,c),p=Math.min(h,u,f),m=Math.max(a,l,c),g=Math.max(h,u,f),x=fe(d,p,t,e,r),y=fe(m,g,t,e,r);let b=i.prevZ,_=i.nextZ;for(;b&&b.z>=x&&_&&_.z<=y;){if(b.x>=d&&b.x<=m&&b.y>=p&&b.y<=g&&b!==n&&b!==o&&vt(a,h,l,u,c,f,b.x,b.y)&&W(b.prev,b,b.next)>=0||(b=b.prevZ,_.x>=d&&_.x<=m&&_.y>=p&&_.y<=g&&_!==n&&_!==o&&vt(a,h,l,u,c,f,_.x,_.y)&&W(_.prev,_,_.next)>=0))return!1;_=_.nextZ}for(;b&&b.z>=x;){if(b.x>=d&&b.x<=m&&b.y>=p&&b.y<=g&&b!==n&&b!==o&&vt(a,h,l,u,c,f,b.x,b.y)&&W(b.prev,b,b.next)>=0)return!1;b=b.prevZ}for(;_&&_.z<=y;){if(_.x>=d&&_.x<=m&&_.y>=p&&_.y<=g&&_!==n&&_!==o&&vt(a,h,l,u,c,f,_.x,_.y)&&W(_.prev,_,_.next)>=0)return!1;_=_.nextZ}return!0}function On(i,t){let e=i;do{const r=e.prev,n=e.next.next;!yt(r,n)&&Ur(r,e,e.next,n)&&Rt(r,n)&&Rt(n,r)&&(t.push(r.i,e.i,n.i),Gt(e),Gt(e.next),e=i=n),e=e.next}while(e!==i);return ft(e)}function $n(i,t,e,r,n,s){let o=i;do{let a=o.next.next;for(;a!==o.prev;){if(o.i!==a.i&&Jn(o,a)){let l=Ar(o,a);o=ft(o,o.next),l=ft(l,l.next),Bt(o,t,e,r,n,s,0),Bt(l,t,e,r,n,s,0);return}a=a.next}o=o.next}while(o!==i)}function jn(i,t,e,r){const n=[];for(let s=0,o=t.length;s<o;s++){const a=t[s]*r,l=s<o-1?t[s+1]*r:i.length,c=Rr(i,a,l,r,!1);c===c.next&&(c.steiner=!0),n.push(Qn(c))}n.sort(Yn);for(let s=0;s<n.length;s++)e=Nn(n[s],e);return e}function Yn(i,t){let e=i.x-t.x;if(e===0&&(e=i.y-t.y,e===0)){const r=(i.next.y-i.y)/(i.next.x-i.x),n=(t.next.y-t.y)/(t.next.x-t.x);e=r-n}return e}function Nn(i,t){const e=qn(i,t);if(!e)return t;const r=Ar(e,i);return ft(r,r.next),ft(e,e.next)}function qn(i,t){let e=t;const r=i.x,n=i.y;let s=-1/0,o;if(yt(i,e))return e;do{if(yt(i,e.next))return e.next;if(n<=e.y&&n>=e.next.y&&e.next.y!==e.y){const u=e.x+(n-e.y)*(e.next.x-e.x)/(e.next.y-e.y);if(u<=r&&u>s&&(s=u,o=e.x<e.next.x?e:e.next,u===r))return o}e=e.next}while(e!==t);if(!o)return null;const a=o,l=o.x,c=o.y;let h=1/0;e=o;do{if(r>=e.x&&e.x>=l&&r!==e.x&&Gr(n<c?r:s,n,l,c,n<c?s:r,n,e.x,e.y)){const u=Math.abs(n-e.y)/(r-e.x);Rt(e,i)&&(u<h||u===h&&(e.x>o.x||e.x===o.x&&Xn(o,e)))&&(o=e,h=u)}e=e.next}while(e!==a);return o}function Xn(i,t){return W(i.prev,i,t.prev)<0&&W(t.next,i,i.next)<0}function Kn(i,t,e,r){let n=i;do n.z===0&&(n.z=fe(n.x,n.y,t,e,r)),n.prevZ=n.prev,n.nextZ=n.next,n=n.next;while(n!==i);n.prevZ.nextZ=null,n.prevZ=null,Zn(n)}function Zn(i){let t,e=1;do{let r=i,n;i=null;let s=null;for(t=0;r;){t++;let o=r,a=0;for(let c=0;c<e&&(a++,o=o.nextZ,!!o);c++);let l=e;for(;a>0||l>0&&o;)a!==0&&(l===0||!o||r.z<=o.z)?(n=r,r=r.nextZ,a--):(n=o,o=o.nextZ,l--),s?s.nextZ=n:i=n,n.prevZ=s,s=n;r=o}s.nextZ=null,e*=2}while(t>1);return i}function fe(i,t,e,r,n){return i=(i-e)*n|0,t=(t-r)*n|0,i=(i|i<<8)&16711935,i=(i|i<<4)&252645135,i=(i|i<<2)&858993459,i=(i|i<<1)&1431655765,t=(t|t<<8)&16711935,t=(t|t<<4)&252645135,t=(t|t<<2)&858993459,t=(t|t<<1)&1431655765,i|t<<1}function Qn(i){let t=i,e=i;do(t.x<e.x||t.x===e.x&&t.y<e.y)&&(e=t),t=t.next;while(t!==i);return e}function Gr(i,t,e,r,n,s,o,a){return(n-o)*(t-a)>=(i-o)*(s-a)&&(i-o)*(r-a)>=(e-o)*(t-a)&&(e-o)*(s-a)>=(n-o)*(r-a)}function vt(i,t,e,r,n,s,o,a){return!(i===o&&t===a)&&Gr(i,t,e,r,n,s,o,a)}function Jn(i,t){return i.next.i!==t.i&&i.prev.i!==t.i&&!ti(i,t)&&(Rt(i,t)&&Rt(t,i)&&ei(i,t)&&(W(i.prev,i,t.prev)||W(i,t.prev,t))||yt(i,t)&&W(i.prev,i,i.next)>0&&W(t.prev,t,t.next)>0)}function W(i,t,e){return(t.y-i.y)*(e.x-t.x)-(t.x-i.x)*(e.y-t.y)}function yt(i,t){return i.x===t.x&&i.y===t.y}function Ur(i,t,e,r){const n=It(W(i,t,e)),s=It(W(i,t,r)),o=It(W(e,r,i)),a=It(W(e,r,t));return!!(n!==s&&o!==a||n===0&&Dt(i,e,t)||s===0&&Dt(i,r,t)||o===0&&Dt(e,i,r)||a===0&&Dt(e,t,r))}function Dt(i,t,e){return t.x<=Math.max(i.x,e.x)&&t.x>=Math.min(i.x,e.x)&&t.y<=Math.max(i.y,e.y)&&t.y>=Math.min(i.y,e.y)}function It(i){return i>0?1:i<0?-1:0}function ti(i,t){let e=i;do{if(e.i!==i.i&&e.next.i!==i.i&&e.i!==t.i&&e.next.i!==t.i&&Ur(e,e.next,i,t))return!0;e=e.next}while(e!==i);return!1}function Rt(i,t){return W(i.prev,i,i.next)<0?W(i,t,i.next)>=0&&W(i,i.prev,t)>=0:W(i,t,i.prev)<0||W(i,i.next,t)<0}function ei(i,t){let e=i,r=!1;const n=(i.x+t.x)/2,s=(i.y+t.y)/2;do e.y>s!=e.next.y>s&&e.next.y!==e.y&&n<(e.next.x-e.x)*(s-e.y)/(e.next.y-e.y)+e.x&&(r=!r),e=e.next;while(e!==i);return r}function Ar(i,t){const e=pe(i.i,i.x,i.y),r=pe(t.i,t.x,t.y),n=i.next,s=t.prev;return i.next=t,t.prev=i,e.next=n,n.prev=e,r.next=e,e.prev=r,s.next=r,r.prev=s,r}function $e(i,t,e,r){const n=pe(i,t,e);return r?(n.next=r.next,n.prev=r,r.next.prev=n,r.next=n):(n.prev=n,n.next=n),n}function Gt(i){i.next.prev=i.prev,i.prev.next=i.next,i.prevZ&&(i.prevZ.nextZ=i.nextZ),i.nextZ&&(i.nextZ.prevZ=i.prevZ)}function pe(i,t,e){return{i,x:t,y:e,prev:null,next:null,z:0,prevZ:null,nextZ:null,steiner:!1}}function ri(i,t,e,r){let n=0;for(let s=t,o=e-r;s<e;s+=r)n+=(i[o]-i[s])*(i[s+1]+i[o+1]),o=s;return n}const ni=Oe.default||Oe;class zr{static init(t){Object.defineProperty(this,"resizeTo",{set(e){globalThis.removeEventListener("resize",this.queueResize),this._resizeTo=e,e&&(globalThis.addEventListener("resize",this.queueResize),this.resize())},get(){return this._resizeTo}}),this.queueResize=()=>{this._resizeTo&&(this._cancelResize(),this._resizeId=requestAnimationFrame(()=>this.resize()))},this._cancelResize=()=>{this._resizeId&&(cancelAnimationFrame(this._resizeId),this._resizeId=null)},this.resize=()=>{if(!this._resizeTo)return;this._cancelResize();let e,r;if(this._resizeTo===globalThis.window)e=globalThis.innerWidth,r=globalThis.innerHeight;else{const{clientWidth:n,clientHeight:s}=this._resizeTo;e=n,r=s}this.renderer.resize(e,r),this.render()},this._resizeId=null,this._resizeTo=null,this.resizeTo=t.resizeTo||null}static destroy(){globalThis.removeEventListener("resize",this.queueResize),this._cancelResize(),this._cancelResize=null,this.queueResize=null,this.resizeTo=null,this.resize=null}}zr.extension=B.Application;class Wr{static init(t){t=Object.assign({autoStart:!0,sharedTicker:!1},t),Object.defineProperty(this,"ticker",{set(e){this._ticker&&this._ticker.remove(this.render,this),this._ticker=e,e&&e.add(this.render,this,wn.LOW)},get(){return this._ticker}}),this.stop=()=>{this._ticker.stop()},this.start=()=>{this._ticker.start()},this._ticker=null,this.ticker=t.sharedTicker?Ee.shared:new Ee,t.autoStart&&this.start()}static destroy(){if(this._ticker){const t=this._ticker;this.ticker=null,t.destroy()}}}Wr.extension=B.Application;class ii extends be{constructor(){super(...arguments),this.chars=Object.create(null),this.lineHeight=0,this.fontFamily="",this.fontMetrics={fontSize:0,ascent:0,descent:0},this.baseLineOffset=0,this.distanceField={type:"none",range:0},this.pages=[],this.applyFillAsTint=!0,this.baseMeasurementFontSize=100,this.baseRenderedFontSize=100}get font(){return A(I,"BitmapFont.font is deprecated, please use BitmapFont.fontFamily instead."),this.fontFamily}get pageTextures(){return A(I,"BitmapFont.pageTextures is deprecated, please use BitmapFont.pages instead."),this.pages}get size(){return A(I,"BitmapFont.size is deprecated, please use BitmapFont.fontMetrics.fontSize instead."),this.fontMetrics.fontSize}get distanceFieldRange(){return A(I,"BitmapFont.distanceFieldRange is deprecated, please use BitmapFont.distanceField.range instead."),this.distanceField.range}get distanceFieldType(){return A(I,"BitmapFont.distanceFieldType is deprecated, please use BitmapFont.distanceField.type instead."),this.distanceField.type}destroy(t=!1){var e;this.emit("destroy",this),this.removeAllListeners();for(const r in this.chars)(e=this.chars[r].texture)==null||e.destroy();this.chars=null,t&&(this.pages.forEach(r=>r.texture.destroy(!0)),this.pages=null)}}const si=["serif","sans-serif","monospace","cursive","fantasy","system-ui"];function Yt(i){const t=typeof i.fontSize=="number"?`${i.fontSize}px`:i.fontSize;let e=i.fontFamily;Array.isArray(i.fontFamily)||(e=i.fontFamily.split(","));for(let r=e.length-1;r>=0;r--){let n=e[r].trim();!/([\"\'])[^\'\"]+\1/.test(n)&&!si.includes(n)&&(n=`"${n}"`),e[r]=n}return`${i.fontStyle} ${i.fontVariant} ${i.fontWeight} ${t} ${e.join(",")}`}const re={willReadFrequently:!0},Q=class C{static get experimentalLetterSpacingSupported(){let t=C._experimentalLetterSpacingSupported;if(t===void 0){const e=dt.get().getCanvasRenderingContext2D().prototype;t=C._experimentalLetterSpacingSupported="letterSpacing"in e||"textLetterSpacing"in e}return t}constructor(t,e,r,n,s,o,a,l,c){this.text=t,this.style=e,this.width=r,this.height=n,this.lines=s,this.lineWidths=o,this.lineHeight=a,this.maxLineWidth=l,this.fontProperties=c}static measureText(t=" ",e,r=C._canvas,n=e.wordWrap){var x;const s=Yt(e),o=C.measureFont(s);o.fontSize===0&&(o.fontSize=e.fontSize,o.ascent=e.fontSize);const a=C.__context;a.font=s;const c=(n?C._wordWrap(t,e,r):t).split(/(?:\r\n|\r|\n)/),h=new Array(c.length);let u=0;for(let y=0;y<c.length;y++){const b=C._measureText(c[y],e.letterSpacing,a);h[y]=b,u=Math.max(u,b)}const f=((x=e._stroke)==null?void 0:x.width)||0;let d=u+f;e.dropShadow&&(d+=e.dropShadow.distance);const p=e.lineHeight||o.fontSize;let m=Math.max(p,o.fontSize+f)+(c.length-1)*(p+e.leading);return e.dropShadow&&(m+=e.dropShadow.distance),new C(t,e,d,m,c,h,p+e.leading,u,o)}static _measureText(t,e,r){let n=!1;C.experimentalLetterSpacingSupported&&(C.experimentalLetterSpacing?(r.letterSpacing=`${e}px`,r.textLetterSpacing=`${e}px`,n=!0):(r.letterSpacing="0px",r.textLetterSpacing="0px"));const s=r.measureText(t);let o=s.width;const a=-s.actualBoundingBoxLeft;let c=s.actualBoundingBoxRight-a;if(o>0)if(n)o-=e,c-=e;else{const h=(C.graphemeSegmenter(t).length-1)*e;o+=h,c+=h}return Math.max(o,c)}static _wordWrap(t,e,r=C._canvas){const n=r.getContext("2d",re);let s=0,o="",a="";const l=Object.create(null),{letterSpacing:c,whiteSpace:h}=e,u=C._collapseSpaces(h),f=C._collapseNewlines(h);let d=!u;const p=e.wordWrapWidth+c,m=C._tokenize(t);for(let g=0;g<m.length;g++){let x=m[g];if(C._isNewline(x)){if(!f){a+=C._addLine(o),d=!u,o="",s=0;continue}x=" "}if(u){const b=C.isBreakingSpace(x),_=C.isBreakingSpace(o[o.length-1]);if(b&&_)continue}const y=C._getFromCache(x,c,l,n);if(y>p)if(o!==""&&(a+=C._addLine(o),o="",s=0),C.canBreakWords(x,e.breakWords)){const b=C.wordWrapSplit(x);for(let _=0;_<b.length;_++){let v=b[_],w=v,S=1;for(;b[_+S];){const k=b[_+S];if(!C.canBreakChars(w,k,x,_,e.breakWords))v+=k;else break;w=k,S++}_+=S-1;const R=C._getFromCache(v,c,l,n);R+s>p&&(a+=C._addLine(o),d=!1,o="",s=0),o+=v,s+=R}}else{o.length>0&&(a+=C._addLine(o),o="",s=0);const b=g===m.length-1;a+=C._addLine(x,!b),d=!1,o="",s=0}else y+s>p&&(d=!1,a+=C._addLine(o),o="",s=0),(o.length>0||!C.isBreakingSpace(x)||d)&&(o+=x,s+=y)}return a+=C._addLine(o,!1),a}static _addLine(t,e=!0){return t=C._trimRight(t),t=e?`${t}
`:t,t}static _getFromCache(t,e,r,n){let s=r[t];return typeof s!="number"&&(s=C._measureText(t,e,n)+e,r[t]=s),s}static _collapseSpaces(t){return t==="normal"||t==="pre-line"}static _collapseNewlines(t){return t==="normal"}static _trimRight(t){if(typeof t!="string")return"";for(let e=t.length-1;e>=0;e--){const r=t[e];if(!C.isBreakingSpace(r))break;t=t.slice(0,-1)}return t}static _isNewline(t){return typeof t!="string"?!1:C._newlines.includes(t.charCodeAt(0))}static isBreakingSpace(t,e){return typeof t!="string"?!1:C._breakingSpaces.includes(t.charCodeAt(0))}static _tokenize(t){const e=[];let r="";if(typeof t!="string")return e;for(let n=0;n<t.length;n++){const s=t[n],o=t[n+1];if(C.isBreakingSpace(s,o)||C._isNewline(s)){r!==""&&(e.push(r),r=""),s==="\r"&&o===`
`?(e.push(`\r
`),n++):e.push(s);continue}r+=s}return r!==""&&e.push(r),e}static canBreakWords(t,e){return e}static canBreakChars(t,e,r,n,s){return!0}static wordWrapSplit(t){return C.graphemeSegmenter(t)}static measureFont(t){if(C._fonts[t])return C._fonts[t];const e=C._context;e.font=t;const r=e.measureText(C.METRICS_STRING+C.BASELINE_SYMBOL),n={ascent:r.actualBoundingBoxAscent,descent:r.actualBoundingBoxDescent,fontSize:r.actualBoundingBoxAscent+r.actualBoundingBoxDescent};return C._fonts[t]=n,n}static clearMetrics(t=""){t?delete C._fonts[t]:C._fonts={}}static get _canvas(){if(!C.__canvas){let t;try{const e=new OffscreenCanvas(0,0),r=e.getContext("2d",re);if(r!=null&&r.measureText)return C.__canvas=e,e;t=dt.get().createCanvas()}catch{t=dt.get().createCanvas()}t.width=t.height=10,C.__canvas=t}return C.__canvas}static get _context(){return C.__context||(C.__context=C._canvas.getContext("2d",re)),C.__context}};Q.METRICS_STRING="|ÉqÅ";Q.BASELINE_SYMBOL="M";Q.BASELINE_MULTIPLIER=1.4;Q.HEIGHT_MULTIPLIER=2;Q.graphemeSegmenter=(()=>{if(typeof(Intl==null?void 0:Intl.Segmenter)=="function"){const i=new Intl.Segmenter;return t=>{const e=i.segment(t),r=[];let n=0;for(const s of e)r[n++]=s.segment;return r}}return i=>[...i]})();Q.experimentalLetterSpacing=!1;Q._fonts={};Q._newlines=[10,13];Q._breakingSpaces=[9,32,8192,8193,8194,8195,8196,8197,8198,8200,8201,8202,8287,12288];let Z=Q;const je=[{offset:0,color:"white"},{offset:1,color:"black"}],ve=class ge{constructor(...t){this.uid=Kt("fillGradient"),this.type="linear",this.colorStops=[];let e=oi(t);e={...e.type==="radial"?ge.defaultRadialOptions:ge.defaultLinearOptions,...Tn(e)},this._textureSize=e.textureSize,this._wrapMode=e.wrapMode,e.type==="radial"?(this.center=e.center,this.outerCenter=e.outerCenter??this.center,this.innerRadius=e.innerRadius,this.outerRadius=e.outerRadius,this.scale=e.scale,this.rotation=e.rotation):(this.start=e.start,this.end=e.end),this.textureSpace=e.textureSpace,this.type=e.type,e.colorStops.forEach(n=>{this.addColorStop(n.offset,n.color)})}addColorStop(t,e){return this.colorStops.push({offset:t,color:L.shared.setValue(e).toHexa()}),this}buildLinearGradient(){if(this.texture)return;let{x:t,y:e}=this.start,{x:r,y:n}=this.end,s=r-t,o=n-e;const a=s<0||o<0;if(this._wrapMode==="clamp-to-edge"){if(s<0){const g=t;t=r,r=g,s*=-1}if(o<0){const g=e;e=n,n=g,o*=-1}}const l=this.colorStops.length?this.colorStops:je,c=this._textureSize,{canvas:h,context:u}=Ne(c,1),f=a?u.createLinearGradient(this._textureSize,0,0,0):u.createLinearGradient(0,0,this._textureSize,0);Ye(f,l),u.fillStyle=f,u.fillRect(0,0,c,1),this.texture=new E({source:new ue({resource:h,addressMode:this._wrapMode})});const d=Math.sqrt(s*s+o*o),p=Math.atan2(o,s),m=new H;m.scale(d/c,1),m.rotate(p),m.translate(t,e),this.textureSpace==="local"&&m.scale(c,c),this.transform=m}buildGradient(){this.type==="linear"?this.buildLinearGradient():this.buildRadialGradient()}buildRadialGradient(){if(this.texture)return;const t=this.colorStops.length?this.colorStops:je,e=this._textureSize,{canvas:r,context:n}=Ne(e,e),{x:s,y:o}=this.center,{x:a,y:l}=this.outerCenter,c=this.innerRadius,h=this.outerRadius,u=a-h,f=l-h,d=e/(h*2),p=(s-u)*d,m=(o-f)*d,g=n.createRadialGradient(p,m,c*d,(a-u)*d,(l-f)*d,h*d);Ye(g,t),n.fillStyle=t[t.length-1].color,n.fillRect(0,0,e,e),n.fillStyle=g,n.translate(p,m),n.rotate(this.rotation),n.scale(1,this.scale),n.translate(-p,-m),n.fillRect(0,0,e,e),this.texture=new E({source:new ue({resource:r,addressMode:this._wrapMode})});const x=new H;x.scale(1/d,1/d),x.translate(u,f),this.textureSpace==="local"&&x.scale(e,e),this.transform=x}get styleKey(){return this.uid}destroy(){var t;(t=this.texture)==null||t.destroy(!0),this.texture=null}};ve.defaultLinearOptions={start:{x:0,y:0},end:{x:0,y:1},colorStops:[],textureSpace:"local",type:"linear",textureSize:256,wrapMode:"clamp-to-edge"};ve.defaultRadialOptions={center:{x:.5,y:.5},innerRadius:0,outerRadius:.5,colorStops:[],scale:1,textureSpace:"local",type:"radial",textureSize:256,wrapMode:"clamp-to-edge"};let et=ve;function Ye(i,t){for(let e=0;e<t.length;e++){const r=t[e];i.addColorStop(r.offset,r.color)}}function Ne(i,t){const e=dt.get().createCanvas(i,t),r=e.getContext("2d");return{canvas:e,context:r}}function oi(i){let t=i[0]??{};return(typeof t=="number"||i[1])&&(A("8.5.2","use options object instead"),t={type:"linear",start:{x:i[0],y:i[1]},end:{x:i[2],y:i[3]},textureSpace:i[4],textureSize:i[5]??et.defaultLinearOptions.textureSize}),t}const qe={repeat:{addressModeU:"repeat",addressModeV:"repeat"},"repeat-x":{addressModeU:"repeat",addressModeV:"clamp-to-edge"},"repeat-y":{addressModeU:"clamp-to-edge",addressModeV:"repeat"},"no-repeat":{addressModeU:"clamp-to-edge",addressModeV:"clamp-to-edge"}};class Jt{constructor(t,e){this.uid=Kt("fillPattern"),this.transform=new H,this._styleKey=null,this.texture=t,this.transform.scale(1/t.frame.width,1/t.frame.height),e&&(t.source.style.addressModeU=qe[e].addressModeU,t.source.style.addressModeV=qe[e].addressModeV)}setTransform(t){const e=this.texture;this.transform.copyFrom(t),this.transform.invert(),this.transform.scale(1/e.frame.width,1/e.frame.height),this._styleKey=null}get styleKey(){return this._styleKey?this._styleKey:(this._styleKey=`fill-pattern-${this.uid}-${this.texture.uid}-${this.transform.toArray().join("-")}`,this._styleKey)}}var ne,Xe;function ai(){if(Xe)return ne;Xe=1,ne=e;var i={a:7,c:6,h:1,l:2,m:2,q:4,s:4,t:2,v:1,z:0},t=/([astvzqmhlc])([^astvzqmhlc]*)/ig;function e(s){var o=[];return s.replace(t,function(a,l,c){var h=l.toLowerCase();for(c=n(c),h=="m"&&c.length>2&&(o.push([l].concat(c.splice(0,2))),h="l",l=l=="m"?"l":"L");;){if(c.length==i[h])return c.unshift(l),o.push(c);if(c.length<i[h])throw new Error("malformed path data");o.push([l].concat(c.splice(0,i[h])))}}),o}var r=/-?[0-9]*\.?[0-9]+(?:e[-+]?\d+)?/ig;function n(s){var o=s.match(r);return o?o.map(Number):[]}return ne}var li=ai();const hi=Pn(li);function ci(i,t){const e=hi(i),r=[];let n=null,s=0,o=0;for(let a=0;a<e.length;a++){const l=e[a],c=l[0],h=l;switch(c){case"M":s=h[1],o=h[2],t.moveTo(s,o);break;case"m":s+=h[1],o+=h[2],t.moveTo(s,o);break;case"H":s=h[1],t.lineTo(s,o);break;case"h":s+=h[1],t.lineTo(s,o);break;case"V":o=h[1],t.lineTo(s,o);break;case"v":o+=h[1],t.lineTo(s,o);break;case"L":s=h[1],o=h[2],t.lineTo(s,o);break;case"l":s+=h[1],o+=h[2],t.lineTo(s,o);break;case"C":s=h[5],o=h[6],t.bezierCurveTo(h[1],h[2],h[3],h[4],s,o);break;case"c":t.bezierCurveTo(s+h[1],o+h[2],s+h[3],o+h[4],s+h[5],o+h[6]),s+=h[5],o+=h[6];break;case"S":s=h[3],o=h[4],t.bezierCurveToShort(h[1],h[2],s,o);break;case"s":t.bezierCurveToShort(s+h[1],o+h[2],s+h[3],o+h[4]),s+=h[3],o+=h[4];break;case"Q":s=h[3],o=h[4],t.quadraticCurveTo(h[1],h[2],s,o);break;case"q":t.quadraticCurveTo(s+h[1],o+h[2],s+h[3],o+h[4]),s+=h[3],o+=h[4];break;case"T":s=h[1],o=h[2],t.quadraticCurveToShort(s,o);break;case"t":s+=h[1],o+=h[2],t.quadraticCurveToShort(s,o);break;case"A":s=h[6],o=h[7],t.arcToSvg(h[1],h[2],h[3],h[4],h[5],s,o);break;case"a":s+=h[6],o+=h[7],t.arcToSvg(h[1],h[2],h[3],h[4],h[5],s,o);break;case"Z":case"z":t.closePath(),r.length>0&&(n=r.pop(),n?(s=n.startX,o=n.startY):(s=0,o=0)),n=null;break;default:Y(`Unknown SVG path command: ${c}`)}c!=="Z"&&c!=="z"&&n===null&&(n={startX:s,startY:o},r.push(n))}return t}class Ce{constructor(t=0,e=0,r=0){this.type="circle",this.x=t,this.y=e,this.radius=r}clone(){return new Ce(this.x,this.y,this.radius)}contains(t,e){if(this.radius<=0)return!1;const r=this.radius*this.radius;let n=this.x-t,s=this.y-e;return n*=n,s*=s,n+s<=r}strokeContains(t,e,r,n=.5){if(this.radius===0)return!1;const s=this.x-t,o=this.y-e,a=this.radius,l=(1-n)*r,c=Math.sqrt(s*s+o*o);return c<=a+l&&c>a-(r-l)}getBounds(t){return t||(t=new $),t.x=this.x-this.radius,t.y=this.y-this.radius,t.width=this.radius*2,t.height=this.radius*2,t}copyFrom(t){return this.x=t.x,this.y=t.y,this.radius=t.radius,this}copyTo(t){return t.copyFrom(this),t}toString(){return`[pixi.js/math:Circle x=${this.x} y=${this.y} radius=${this.radius}]`}}class Me{constructor(t=0,e=0,r=0,n=0){this.type="ellipse",this.x=t,this.y=e,this.halfWidth=r,this.halfHeight=n}clone(){return new Me(this.x,this.y,this.halfWidth,this.halfHeight)}contains(t,e){if(this.halfWidth<=0||this.halfHeight<=0)return!1;let r=(t-this.x)/this.halfWidth,n=(e-this.y)/this.halfHeight;return r*=r,n*=n,r+n<=1}strokeContains(t,e,r,n=.5){const{halfWidth:s,halfHeight:o}=this;if(s<=0||o<=0)return!1;const a=r*(1-n),l=r-a,c=s-l,h=o-l,u=s+a,f=o+a,d=t-this.x,p=e-this.y,m=d*d/(c*c)+p*p/(h*h),g=d*d/(u*u)+p*p/(f*f);return m>1&&g<=1}getBounds(t){return t||(t=new $),t.x=this.x-this.halfWidth,t.y=this.y-this.halfHeight,t.width=this.halfWidth*2,t.height=this.halfHeight*2,t}copyFrom(t){return this.x=t.x,this.y=t.y,this.halfWidth=t.halfWidth,this.halfHeight=t.halfHeight,this}copyTo(t){return t.copyFrom(this),t}toString(){return`[pixi.js/math:Ellipse x=${this.x} y=${this.y} halfWidth=${this.halfWidth} halfHeight=${this.halfHeight}]`}}function ui(i,t,e,r,n,s){const o=i-e,a=t-r,l=n-e,c=s-r,h=o*l+a*c,u=l*l+c*c;let f=-1;u!==0&&(f=h/u);let d,p;f<0?(d=e,p=r):f>1?(d=n,p=s):(d=e+f*l,p=r+f*c);const m=i-d,g=t-p;return m*m+g*g}let di,fi;class Mt{constructor(...t){this.type="polygon";let e=Array.isArray(t[0])?t[0]:t;if(typeof e[0]!="number"){const r=[];for(let n=0,s=e.length;n<s;n++)r.push(e[n].x,e[n].y);e=r}this.points=e,this.closePath=!0}isClockwise(){let t=0;const e=this.points,r=e.length;for(let n=0;n<r;n+=2){const s=e[n],o=e[n+1],a=e[(n+2)%r],l=e[(n+3)%r];t+=(a-s)*(l+o)}return t<0}containsPolygon(t){const e=this.getBounds(di),r=t.getBounds(fi);if(!e.containsRect(r))return!1;const n=t.points;for(let s=0;s<n.length;s+=2){const o=n[s],a=n[s+1];if(!this.contains(o,a))return!1}return!0}clone(){const t=this.points.slice(),e=new Mt(t);return e.closePath=this.closePath,e}contains(t,e){let r=!1;const n=this.points.length/2;for(let s=0,o=n-1;s<n;o=s++){const a=this.points[s*2],l=this.points[s*2+1],c=this.points[o*2],h=this.points[o*2+1];l>e!=h>e&&t<(c-a)*((e-l)/(h-l))+a&&(r=!r)}return r}strokeContains(t,e,r,n=.5){const s=r*r,o=s*(1-n),a=s-o,{points:l}=this,c=l.length-(this.closePath?0:2);for(let h=0;h<c;h+=2){const u=l[h],f=l[h+1],d=l[(h+2)%l.length],p=l[(h+3)%l.length],m=ui(t,e,u,f,d,p),g=Math.sign((d-u)*(e-f)-(p-f)*(t-u));if(m<=(g<0?a:o))return!0}return!1}getBounds(t){t||(t=new $);const e=this.points;let r=1/0,n=-1/0,s=1/0,o=-1/0;for(let a=0,l=e.length;a<l;a+=2){const c=e[a],h=e[a+1];r=c<r?c:r,n=c>n?c:n,s=h<s?h:s,o=h>o?h:o}return t.x=r,t.width=n-r,t.y=s,t.height=o-s,t}copyFrom(t){return this.points=t.points.slice(),this.closePath=t.closePath,this}copyTo(t){return t.copyFrom(this),t}toString(){return`[pixi.js/math:PolygoncloseStroke=${this.closePath}points=${this.points.reduce((t,e)=>`${t}, ${e}`,"")}]`}get lastX(){return this.points[this.points.length-2]}get lastY(){return this.points[this.points.length-1]}get x(){return A("8.11.0","Polygon.lastX is deprecated, please use Polygon.lastX instead."),this.points[this.points.length-2]}get y(){return A("8.11.0","Polygon.y is deprecated, please use Polygon.lastY instead."),this.points[this.points.length-1]}get startX(){return this.points[0]}get startY(){return this.points[1]}}const Ht=(i,t,e,r,n,s,o)=>{const a=i-e,l=t-r,c=Math.sqrt(a*a+l*l);return c>=n-s&&c<=n+o};class ke{constructor(t=0,e=0,r=0,n=0,s=20){this.type="roundedRectangle",this.x=t,this.y=e,this.width=r,this.height=n,this.radius=s}getBounds(t){return t||(t=new $),t.x=this.x,t.y=this.y,t.width=this.width,t.height=this.height,t}clone(){return new ke(this.x,this.y,this.width,this.height,this.radius)}copyFrom(t){return this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height,this}copyTo(t){return t.copyFrom(this),t}contains(t,e){if(this.width<=0||this.height<=0)return!1;if(t>=this.x&&t<=this.x+this.width&&e>=this.y&&e<=this.y+this.height){const r=Math.max(0,Math.min(this.radius,Math.min(this.width,this.height)/2));if(e>=this.y+r&&e<=this.y+this.height-r||t>=this.x+r&&t<=this.x+this.width-r)return!0;let n=t-(this.x+r),s=e-(this.y+r);const o=r*r;if(n*n+s*s<=o||(n=t-(this.x+this.width-r),n*n+s*s<=o)||(s=e-(this.y+this.height-r),n*n+s*s<=o)||(n=t-(this.x+r),n*n+s*s<=o))return!0}return!1}strokeContains(t,e,r,n=.5){const{x:s,y:o,width:a,height:l,radius:c}=this,h=r*(1-n),u=r-h,f=s+c,d=o+c,p=a-c*2,m=l-c*2,g=s+a,x=o+l;return(t>=s-h&&t<=s+u||t>=g-u&&t<=g+h)&&e>=d&&e<=d+m||(e>=o-h&&e<=o+u||e>=x-u&&e<=x+h)&&t>=f&&t<=f+p?!0:t<f&&e<d&&Ht(t,e,f,d,c,u,h)||t>g-c&&e<d&&Ht(t,e,g-c,d,c,u,h)||t>g-c&&e>x-c&&Ht(t,e,g-c,x-c,c,u,h)||t<f&&e>x-c&&Ht(t,e,f,x-c,c,u,h)}toString(){return`[pixi.js/math:RoundedRectangle x=${this.x} y=${this.y}width=${this.width} height=${this.height} radius=${this.radius}]`}}function pi(i,t,e,r,n,s,o,a=null){let l=0;e*=t,n*=s;const c=a.a,h=a.b,u=a.c,f=a.d,d=a.tx,p=a.ty;for(;l<o;){const m=i[e],g=i[e+1];r[n]=c*m+u*g+d,r[n+1]=h*m+f*g+p,n+=s,e+=t,l++}}function gi(i,t,e,r){let n=0;for(t*=e;n<r;)i[t]=0,i[t+1]=0,t+=e,n++}function Lr(i,t,e,r,n){const s=t.a,o=t.b,a=t.c,l=t.d,c=t.tx,h=t.ty;e||(e=0),r||(r=2),n||(n=i.length/r-e);let u=e*r;for(let f=0;f<n;f++){const d=i[u],p=i[u+1];i[u]=s*d+a*p+c,i[u+1]=o*d+l*p+h,u+=r}}const xi=new H;class Fe{constructor(){this.packAsQuad=!1,this.batcherName="default",this.topology="triangle-list",this.applyTransform=!0,this.roundPixels=0,this._batcher=null,this._batch=null}get uvs(){return this.geometryData.uvs}get positions(){return this.geometryData.vertices}get indices(){return this.geometryData.indices}get blendMode(){return this.renderable&&this.applyTransform?this.renderable.groupBlendMode:"normal"}get color(){const t=this.baseColor,e=t>>16|t&65280|(t&255)<<16,r=this.renderable;return r?vn(e,r.groupColor)+(this.alpha*r.groupAlpha*255<<24):e+(this.alpha*255<<24)}get transform(){var t;return((t=this.renderable)==null?void 0:t.groupTransform)||xi}copyTo(t){t.indexOffset=this.indexOffset,t.indexSize=this.indexSize,t.attributeOffset=this.attributeOffset,t.attributeSize=this.attributeSize,t.baseColor=this.baseColor,t.alpha=this.alpha,t.texture=this.texture,t.geometryData=this.geometryData,t.topology=this.topology}reset(){this.applyTransform=!0,this.renderable=null,this.topology="triangle-list"}}const Ut={extension:{type:B.ShapeBuilder,name:"circle"},build(i,t){let e,r,n,s,o,a;if(i.type==="circle"){const _=i;if(o=a=_.radius,o<=0)return!1;e=_.x,r=_.y,n=s=0}else if(i.type==="ellipse"){const _=i;if(o=_.halfWidth,a=_.halfHeight,o<=0||a<=0)return!1;e=_.x,r=_.y,n=s=0}else{const _=i,v=_.width/2,w=_.height/2;e=_.x+v,r=_.y+w,o=a=Math.max(0,Math.min(_.radius,Math.min(v,w))),n=v-o,s=w-a}if(n<0||s<0)return!1;const l=Math.ceil(2.3*Math.sqrt(o+a)),c=l*8+(n?4:0)+(s?4:0);if(c===0)return!1;if(l===0)return t[0]=t[6]=e+n,t[1]=t[3]=r+s,t[2]=t[4]=e-n,t[5]=t[7]=r-s,!0;let h=0,u=l*4+(n?2:0)+2,f=u,d=c,p=n+o,m=s,g=e+p,x=e-p,y=r+m;if(t[h++]=g,t[h++]=y,t[--u]=y,t[--u]=x,s){const _=r-m;t[f++]=x,t[f++]=_,t[--d]=_,t[--d]=g}for(let _=1;_<l;_++){const v=Math.PI/2*(_/l),w=n+Math.cos(v)*o,S=s+Math.sin(v)*a,R=e+w,k=e-w,P=r+S,T=r-S;t[h++]=R,t[h++]=P,t[--u]=P,t[--u]=k,t[f++]=k,t[f++]=T,t[--d]=T,t[--d]=R}p=n,m=s+a,g=e+p,x=e-p,y=r+m;const b=r-m;return t[h++]=g,t[h++]=y,t[--d]=b,t[--d]=g,n&&(t[h++]=x,t[h++]=y,t[--d]=b,t[--d]=x),!0},triangulate(i,t,e,r,n,s){if(i.length===0)return;let o=0,a=0;for(let h=0;h<i.length;h+=2)o+=i[h],a+=i[h+1];o/=i.length/2,a/=i.length/2;let l=r;t[l*e]=o,t[l*e+1]=a;const c=l++;for(let h=0;h<i.length;h+=2)t[l*e]=i[h],t[l*e+1]=i[h+1],h>0&&(n[s++]=l,n[s++]=c,n[s++]=l-1),l++;n[s++]=c+1,n[s++]=c,n[s++]=l-1}},mi={...Ut,extension:{...Ut.extension,name:"ellipse"}},yi={...Ut,extension:{...Ut.extension,name:"roundedRectangle"}},Dr=1e-4,Ke=1e-4;function _i(i){const t=i.length;if(t<6)return 1;let e=0;for(let r=0,n=i[t-2],s=i[t-1];r<t;r+=2){const o=i[r],a=i[r+1];e+=(o-n)*(a+s),n=o,s=a}return e<0?-1:1}function Ze(i,t,e,r,n,s,o,a){const l=i-e*n,c=t-r*n,h=i+e*s,u=t+r*s;let f,d;o?(f=r,d=-e):(f=-r,d=e);const p=l+f,m=c+d,g=h+f,x=u+d;return a.push(p,m),a.push(g,x),2}function ht(i,t,e,r,n,s,o,a){const l=e-i,c=r-t;let h=Math.atan2(l,c),u=Math.atan2(n-i,s-t);a&&h<u?h+=Math.PI*2:!a&&h>u&&(u+=Math.PI*2);let f=h;const d=u-h,p=Math.abs(d),m=Math.sqrt(l*l+c*c),g=(15*p*Math.sqrt(m)/Math.PI>>0)+1,x=d/g;if(f+=x,a){o.push(i,t),o.push(e,r);for(let y=1,b=f;y<g;y++,b+=x)o.push(i,t),o.push(i+Math.sin(b)*m,t+Math.cos(b)*m);o.push(i,t),o.push(n,s)}else{o.push(e,r),o.push(i,t);for(let y=1,b=f;y<g;y++,b+=x)o.push(i+Math.sin(b)*m,t+Math.cos(b)*m),o.push(i,t);o.push(n,s),o.push(i,t)}return g*2}function bi(i,t,e,r,n,s){const o=Dr;if(i.length===0)return;const a=t;let l=a.alignment;if(t.alignment!==.5){let z=_i(i);l=(l-.5)*z+.5}const c=new gt(i[0],i[1]),h=new gt(i[i.length-2],i[i.length-1]),u=r,f=Math.abs(c.x-h.x)<o&&Math.abs(c.y-h.y)<o;if(u){i=i.slice(),f&&(i.pop(),i.pop(),h.set(i[i.length-2],i[i.length-1]));const z=(c.x+h.x)*.5,rt=(h.y+c.y)*.5;i.unshift(z,rt),i.push(z,rt)}const d=n,p=i.length/2;let m=i.length;const g=d.length/2,x=a.width/2,y=x*x,b=a.miterLimit*a.miterLimit;let _=i[0],v=i[1],w=i[2],S=i[3],R=0,k=0,P=-(v-S),T=_-w,U=0,G=0,O=Math.sqrt(P*P+T*T);P/=O,T/=O,P*=x,T*=x;const it=l,M=(1-it)*2,F=it*2;u||(a.cap==="round"?m+=ht(_-P*(M-F)*.5,v-T*(M-F)*.5,_-P*M,v-T*M,_+P*F,v+T*F,d,!0)+2:a.cap==="square"&&(m+=Ze(_,v,P,T,M,F,!0,d))),d.push(_-P*M,v-T*M),d.push(_+P*F,v+T*F);for(let z=1;z<p-1;++z){_=i[(z-1)*2],v=i[(z-1)*2+1],w=i[z*2],S=i[z*2+1],R=i[(z+1)*2],k=i[(z+1)*2+1],P=-(v-S),T=_-w,O=Math.sqrt(P*P+T*T),P/=O,T/=O,P*=x,T*=x,U=-(S-k),G=w-R,O=Math.sqrt(U*U+G*G),U/=O,G/=O,U*=x,G*=x;const rt=w-_,bt=v-S,St=w-R,wt=k-S,Le=rt*St+bt*wt,At=bt*St-wt*rt,Tt=At<0;if(Math.abs(At)<.001*Math.abs(Le)){d.push(w-P*M,S-T*M),d.push(w+P*F,S+T*F),Le>=0&&(a.join==="round"?m+=ht(w,S,w-P*M,S-T*M,w-U*M,S-G*M,d,!1)+4:m+=2,d.push(w-U*F,S-G*F),d.push(w+U*M,S+G*M));continue}const De=(-P+_)*(-T+S)-(-P+w)*(-T+v),Ie=(-U+R)*(-G+S)-(-U+w)*(-G+k),zt=(rt*Ie-St*De)/At,Wt=(wt*De-bt*Ie)/At,ee=(zt-w)*(zt-w)+(Wt-S)*(Wt-S),st=w+(zt-w)*M,ot=S+(Wt-S)*M,at=w-(zt-w)*F,lt=S-(Wt-S)*F,bn=Math.min(rt*rt+bt*bt,St*St+wt*wt),He=Tt?M:F,Sn=bn+He*He*y;ee<=Sn?a.join==="bevel"||ee/y>b?(Tt?(d.push(st,ot),d.push(w+P*F,S+T*F),d.push(st,ot),d.push(w+U*F,S+G*F)):(d.push(w-P*M,S-T*M),d.push(at,lt),d.push(w-U*M,S-G*M),d.push(at,lt)),m+=2):a.join==="round"?Tt?(d.push(st,ot),d.push(w+P*F,S+T*F),m+=ht(w,S,w+P*F,S+T*F,w+U*F,S+G*F,d,!0)+4,d.push(st,ot),d.push(w+U*F,S+G*F)):(d.push(w-P*M,S-T*M),d.push(at,lt),m+=ht(w,S,w-P*M,S-T*M,w-U*M,S-G*M,d,!1)+4,d.push(w-U*M,S-G*M),d.push(at,lt)):(d.push(st,ot),d.push(at,lt)):(d.push(w-P*M,S-T*M),d.push(w+P*F,S+T*F),a.join==="round"?Tt?m+=ht(w,S,w+P*F,S+T*F,w+U*F,S+G*F,d,!0)+2:m+=ht(w,S,w-P*M,S-T*M,w-U*M,S-G*M,d,!1)+2:a.join==="miter"&&ee/y<=b&&(Tt?(d.push(at,lt),d.push(at,lt)):(d.push(st,ot),d.push(st,ot)),m+=2),d.push(w-U*M,S-G*M),d.push(w+U*F,S+G*F),m+=2)}_=i[(p-2)*2],v=i[(p-2)*2+1],w=i[(p-1)*2],S=i[(p-1)*2+1],P=-(v-S),T=_-w,O=Math.sqrt(P*P+T*T),P/=O,T/=O,P*=x,T*=x,d.push(w-P*M,S-T*M),d.push(w+P*F,S+T*F),u||(a.cap==="round"?m+=ht(w-P*(M-F)*.5,S-T*(M-F)*.5,w-P*M,S-T*M,w+P*F,S+T*F,d,!1)+2:a.cap==="square"&&(m+=Ze(w,S,P,T,M,F,!1,d)));const _n=Ke*Ke;for(let z=g;z<m+g-2;++z)_=d[z*2],v=d[z*2+1],w=d[(z+1)*2],S=d[(z+1)*2+1],R=d[(z+2)*2],k=d[(z+2)*2+1],!(Math.abs(_*(S-k)+w*(k-v)+R*(v-S))<_n)&&s.push(z,z+1,z+2)}function Si(i,t,e,r){const n=Dr;if(i.length===0)return;const s=i[0],o=i[1],a=i[i.length-2],l=i[i.length-1],c=t||Math.abs(s-a)<n&&Math.abs(o-l)<n,h=e,u=i.length/2,f=h.length/2;for(let d=0;d<u;d++)h.push(i[d*2]),h.push(i[d*2+1]);for(let d=0;d<u-1;d++)r.push(f+d,f+d+1);c&&r.push(f+u-1,f)}function Ir(i,t,e,r,n,s,o){const a=ni(i,t,2);if(!a)return;for(let c=0;c<a.length;c+=3)s[o++]=a[c]+n,s[o++]=a[c+1]+n,s[o++]=a[c+2]+n;let l=n*r;for(let c=0;c<i.length;c+=2)e[l]=i[c],e[l+1]=i[c+1],l+=r}const wi=[],Ti={extension:{type:B.ShapeBuilder,name:"polygon"},build(i,t){for(let e=0;e<i.points.length;e++)t[e]=i.points[e];return!0},triangulate(i,t,e,r,n,s){Ir(i,wi,t,e,r,n,s)}},Pi={extension:{type:B.ShapeBuilder,name:"rectangle"},build(i,t){const e=i,r=e.x,n=e.y,s=e.width,o=e.height;return s>0&&o>0?(t[0]=r,t[1]=n,t[2]=r+s,t[3]=n,t[4]=r+s,t[5]=n+o,t[6]=r,t[7]=n+o,!0):!1},triangulate(i,t,e,r,n,s){let o=0;r*=e,t[r+o]=i[0],t[r+o+1]=i[1],o+=e,t[r+o]=i[2],t[r+o+1]=i[3],o+=e,t[r+o]=i[6],t[r+o+1]=i[7],o+=e,t[r+o]=i[4],t[r+o+1]=i[5],o+=e;const a=r/e;n[s++]=a,n[s++]=a+1,n[s++]=a+2,n[s++]=a+1,n[s++]=a+3,n[s++]=a+2}},vi={extension:{type:B.ShapeBuilder,name:"triangle"},build(i,t){return t[0]=i.x,t[1]=i.y,t[2]=i.x2,t[3]=i.y2,t[4]=i.x3,t[5]=i.y3,!0},triangulate(i,t,e,r,n,s){let o=0;r*=e,t[r+o]=i[0],t[r+o+1]=i[1],o+=e,t[r+o]=i[2],t[r+o+1]=i[3],o+=e,t[r+o]=i[4],t[r+o+1]=i[5];const a=r/e;n[s++]=a,n[s++]=a+1,n[s++]=a+2}},Ci=new H,Mi=new $;function ki(i,t,e,r){const n=t.matrix?i.copyFrom(t.matrix).invert():i.identity();if(t.textureSpace==="local"){const o=e.getBounds(Mi);t.width&&o.pad(t.width);const{x:a,y:l}=o,c=1/o.width,h=1/o.height,u=-a*c,f=-l*h,d=n.a,p=n.b,m=n.c,g=n.d;n.a*=c,n.b*=c,n.c*=h,n.d*=h,n.tx=u*d+f*m+n.tx,n.ty=u*p+f*g+n.ty}else n.translate(t.texture.frame.x,t.texture.frame.y),n.scale(1/t.texture.source.width,1/t.texture.source.height);const s=t.texture.source.style;return!(t.fill instanceof et)&&s.addressMode==="clamp-to-edge"&&(s.addressMode="repeat",s.update()),r&&n.append(Ci.copyFrom(r).invert()),n}const te={};V.handleByMap(B.ShapeBuilder,te);V.add(Pi,Ti,vi,Ut,mi,yi);const Fi=new $,Bi=new H;function Ri(i,t){const{geometryData:e,batches:r}=t;r.length=0,e.indices.length=0,e.vertices.length=0,e.uvs.length=0;for(let n=0;n<i.instructions.length;n++){const s=i.instructions[n];if(s.action==="texture")Gi(s.data,r,e);else if(s.action==="fill"||s.action==="stroke"){const o=s.action==="stroke",a=s.data.path.shapePath,l=s.data.style,c=s.data.hole;o&&c&&Qe(c.shapePath,l,!0,r,e),c&&(a.shapePrimitives[a.shapePrimitives.length-1].holes=c.shapePath.shapePrimitives),Qe(a,l,o,r,e)}}}function Gi(i,t,e){const r=[],n=te.rectangle,s=Fi;s.x=i.dx,s.y=i.dy,s.width=i.dw,s.height=i.dh;const o=i.transform;if(!n.build(s,r))return;const{vertices:a,uvs:l,indices:c}=e,h=c.length,u=a.length/2;o&&Lr(r,o),n.triangulate(r,a,2,u,c,h);const f=i.image,d=f.uvs;l.push(d.x0,d.y0,d.x1,d.y1,d.x3,d.y3,d.x2,d.y2);const p=tt.get(Fe);p.indexOffset=h,p.indexSize=c.length-h,p.attributeOffset=u,p.attributeSize=a.length/2-u,p.baseColor=i.style,p.alpha=i.alpha,p.texture=f,p.geometryData=e,t.push(p)}function Qe(i,t,e,r,n){const{vertices:s,uvs:o,indices:a}=n;i.shapePrimitives.forEach(({shape:l,transform:c,holes:h})=>{const u=[],f=te[l.type];if(!f.build(l,u))return;const d=a.length,p=s.length/2;let m="triangle-list";if(c&&Lr(u,c),e){const b=l.closePath??!0,_=t;_.pixelLine?(Si(u,b,s,a),m="line-list"):bi(u,_,!1,b,s,a)}else if(h){const b=[],_=u.slice();Ui(h).forEach(w=>{b.push(_.length/2),_.push(...w)}),Ir(_,b,s,2,p,a,d)}else f.triangulate(u,s,2,p,a,d);const g=o.length/2,x=t.texture;if(x!==E.WHITE){const b=ki(Bi,t,l,c);pi(s,2,p,o,g,2,s.length/2-p,b)}else gi(o,g,2,s.length/2-p);const y=tt.get(Fe);y.indexOffset=d,y.indexSize=a.length-d,y.attributeOffset=p,y.attributeSize=s.length/2-p,y.baseColor=t.color,y.alpha=t.alpha,y.texture=x,y.geometryData=n,y.topology=m,r.push(y)})}function Ui(i){const t=[];for(let e=0;e<i.length;e++){const r=i[e].shape,n=[];te[r.type].build(r,n)&&t.push(n)}return t}class Ai{constructor(){this.batches=[],this.geometryData={vertices:[],uvs:[],indices:[]}}}class zi{constructor(){this.instructions=new Cn}init(t){this.batcher=new Rn({maxTextures:t}),this.instructions.reset()}get geometry(){return A(Mn,"GraphicsContextRenderData#geometry is deprecated, please use batcher.geometry instead."),this.batcher.geometry}}const Be=class xe{constructor(t){this._gpuContextHash={},this._graphicsDataContextHash=Object.create(null),this._renderer=t,t.renderableGC.addManagedHash(this,"_gpuContextHash"),t.renderableGC.addManagedHash(this,"_graphicsDataContextHash")}init(t){xe.defaultOptions.bezierSmoothness=(t==null?void 0:t.bezierSmoothness)??xe.defaultOptions.bezierSmoothness}getContextRenderData(t){return this._graphicsDataContextHash[t.uid]||this._initContextRenderData(t)}updateGpuContext(t){let e=this._gpuContextHash[t.uid]||this._initContext(t);if(t.dirty){e?this._cleanGraphicsContextData(t):e=this._initContext(t),Ri(t,e);const r=t.batchMode;t.customShader||r==="no-batch"?e.isBatchable=!1:r==="auto"?e.isBatchable=e.geometryData.vertices.length<400:e.isBatchable=!0,t.dirty=!1}return e}getGpuContext(t){return this._gpuContextHash[t.uid]||this._initContext(t)}_initContextRenderData(t){const e=tt.get(zi,{maxTextures:this._renderer.limits.maxBatchableTextures}),{batches:r,geometryData:n}=this._gpuContextHash[t.uid],s=n.vertices.length,o=n.indices.length;for(let h=0;h<r.length;h++)r[h].applyTransform=!1;const a=e.batcher;a.ensureAttributeBuffer(s),a.ensureIndexBuffer(o),a.begin();for(let h=0;h<r.length;h++){const u=r[h];a.add(u)}a.finish(e.instructions);const l=a.geometry;l.indexBuffer.setDataWithSize(a.indexBuffer,a.indexSize,!0),l.buffers[0].setDataWithSize(a.attributeBuffer.float32View,a.attributeSize,!0);const c=a.batches;for(let h=0;h<c.length;h++){const u=c[h];u.bindGroup=Bn(u.textures.textures,u.textures.count,this._renderer.limits.maxBatchableTextures)}return this._graphicsDataContextHash[t.uid]=e,e}_initContext(t){const e=new Ai;return e.context=t,this._gpuContextHash[t.uid]=e,t.on("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[t.uid]}onGraphicsContextDestroy(t){this._cleanGraphicsContextData(t),t.off("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[t.uid]=null}_cleanGraphicsContextData(t){const e=this._gpuContextHash[t.uid];e.isBatchable||this._graphicsDataContextHash[t.uid]&&(tt.return(this.getContextRenderData(t)),this._graphicsDataContextHash[t.uid]=null),e.batches&&e.batches.forEach(r=>{tt.return(r)})}destroy(){for(const t in this._gpuContextHash)this._gpuContextHash[t]&&this.onGraphicsContextDestroy(this._gpuContextHash[t].context)}};Be.extension={type:[B.WebGLSystem,B.WebGPUSystem,B.CanvasSystem],name:"graphicsContext"};Be.defaultOptions={bezierSmoothness:.5};let Re=Be;const Wi=8,Et=11920929e-14,Li=1;function Hr(i,t,e,r,n,s,o,a,l,c){const u=Math.min(.99,Math.max(0,c??Re.defaultOptions.bezierSmoothness));let f=(Li-u)/1;return f*=f,Di(t,e,r,n,s,o,a,l,i,f),i}function Di(i,t,e,r,n,s,o,a,l,c){me(i,t,e,r,n,s,o,a,l,c,0),l.push(o,a)}function me(i,t,e,r,n,s,o,a,l,c,h){if(h>Wi)return;const u=(i+e)/2,f=(t+r)/2,d=(e+n)/2,p=(r+s)/2,m=(n+o)/2,g=(s+a)/2,x=(u+d)/2,y=(f+p)/2,b=(d+m)/2,_=(p+g)/2,v=(x+b)/2,w=(y+_)/2;if(h>0){let S=o-i,R=a-t;const k=Math.abs((e-o)*R-(r-a)*S),P=Math.abs((n-o)*R-(s-a)*S);if(k>Et&&P>Et){if((k+P)*(k+P)<=c*(S*S+R*R)){l.push(v,w);return}}else if(k>Et){if(k*k<=c*(S*S+R*R)){l.push(v,w);return}}else if(P>Et){if(P*P<=c*(S*S+R*R)){l.push(v,w);return}}else if(S=v-(i+o)/2,R=w-(t+a)/2,S*S+R*R<=c){l.push(v,w);return}}me(i,t,u,f,x,y,v,w,l,c,h+1),me(v,w,b,_,m,g,o,a,l,c,h+1)}const Ii=8,Hi=11920929e-14,Ei=1;function Vi(i,t,e,r,n,s,o,a){const c=Math.min(.99,Math.max(0,a??Re.defaultOptions.bezierSmoothness));let h=(Ei-c)/1;return h*=h,Oi(t,e,r,n,s,o,i,h),i}function Oi(i,t,e,r,n,s,o,a){ye(o,i,t,e,r,n,s,a,0),o.push(n,s)}function ye(i,t,e,r,n,s,o,a,l){if(l>Ii)return;const c=(t+r)/2,h=(e+n)/2,u=(r+s)/2,f=(n+o)/2,d=(c+u)/2,p=(h+f)/2;let m=s-t,g=o-e;const x=Math.abs((r-s)*g-(n-o)*m);if(x>Hi){if(x*x<=a*(m*m+g*g)){i.push(d,p);return}}else if(m=d-(t+s)/2,g=p-(e+o)/2,m*m+g*g<=a){i.push(d,p);return}ye(i,t,e,c,h,d,p,a,l+1),ye(i,d,p,u,f,s,o,a,l+1)}function Er(i,t,e,r,n,s,o,a){let l=Math.abs(n-s);(!o&&n>s||o&&s>n)&&(l=2*Math.PI-l),a||(a=Math.max(6,Math.floor(6*Math.pow(r,1/3)*(l/Math.PI)))),a=Math.max(a,3);let c=l/a,h=n;c*=o?-1:1;for(let u=0;u<a+1;u++){const f=Math.cos(h),d=Math.sin(h),p=t+f*r,m=e+d*r;i.push(p,m),h+=c}}function $i(i,t,e,r,n,s){const o=i[i.length-2],l=i[i.length-1]-e,c=o-t,h=n-e,u=r-t,f=Math.abs(l*u-c*h);if(f<1e-8||s===0){(i[i.length-2]!==t||i[i.length-1]!==e)&&i.push(t,e);return}const d=l*l+c*c,p=h*h+u*u,m=l*h+c*u,g=s*Math.sqrt(d)/f,x=s*Math.sqrt(p)/f,y=g*m/d,b=x*m/p,_=g*u+x*c,v=g*h+x*l,w=c*(x+y),S=l*(x+y),R=u*(g+b),k=h*(g+b),P=Math.atan2(S-v,w-_),T=Math.atan2(k-v,R-_);Er(i,_+t,v+e,s,P,T,c*h>u*l)}const kt=Math.PI*2,ie={centerX:0,centerY:0,ang1:0,ang2:0},se=({x:i,y:t},e,r,n,s,o,a,l)=>{i*=e,t*=r;const c=n*i-s*t,h=s*i+n*t;return l.x=c+o,l.y=h+a,l};function ji(i,t){const e=t===-1.5707963267948966?-.551915024494:1.3333333333333333*Math.tan(t/4),r=t===1.5707963267948966?.551915024494:e,n=Math.cos(i),s=Math.sin(i),o=Math.cos(i+t),a=Math.sin(i+t);return[{x:n-s*r,y:s+n*r},{x:o+a*r,y:a-o*r},{x:o,y:a}]}const Je=(i,t,e,r)=>{const n=i*r-t*e<0?-1:1;let s=i*e+t*r;return s>1&&(s=1),s<-1&&(s=-1),n*Math.acos(s)},Yi=(i,t,e,r,n,s,o,a,l,c,h,u,f)=>{const d=Math.pow(n,2),p=Math.pow(s,2),m=Math.pow(h,2),g=Math.pow(u,2);let x=d*p-d*g-p*m;x<0&&(x=0),x/=d*g+p*m,x=Math.sqrt(x)*(o===a?-1:1);const y=x*n/s*u,b=x*-s/n*h,_=c*y-l*b+(i+e)/2,v=l*y+c*b+(t+r)/2,w=(h-y)/n,S=(u-b)/s,R=(-h-y)/n,k=(-u-b)/s,P=Je(1,0,w,S);let T=Je(w,S,R,k);a===0&&T>0&&(T-=kt),a===1&&T<0&&(T+=kt),f.centerX=_,f.centerY=v,f.ang1=P,f.ang2=T};function Ni(i,t,e,r,n,s,o,a=0,l=0,c=0){if(s===0||o===0)return;const h=Math.sin(a*kt/360),u=Math.cos(a*kt/360),f=u*(t-r)/2+h*(e-n)/2,d=-h*(t-r)/2+u*(e-n)/2;if(f===0&&d===0)return;s=Math.abs(s),o=Math.abs(o);const p=Math.pow(f,2)/Math.pow(s,2)+Math.pow(d,2)/Math.pow(o,2);p>1&&(s*=Math.sqrt(p),o*=Math.sqrt(p)),Yi(t,e,r,n,s,o,l,c,h,u,f,d,ie);let{ang1:m,ang2:g}=ie;const{centerX:x,centerY:y}=ie;let b=Math.abs(g)/(kt/4);Math.abs(1-b)<1e-7&&(b=1);const _=Math.max(Math.ceil(b),1);g/=_;let v=i[i.length-2],w=i[i.length-1];const S={x:0,y:0};for(let R=0;R<_;R++){const k=ji(m,g),{x:P,y:T}=se(k[0],s,o,u,h,x,y,S),{x:U,y:G}=se(k[1],s,o,u,h,x,y,S),{x:O,y:it}=se(k[2],s,o,u,h,x,y,S);Hr(i,v,w,P,T,U,G,O,it),v=O,w=it,m+=g}}function qi(i,t,e){const r=(o,a)=>{const l=a.x-o.x,c=a.y-o.y,h=Math.sqrt(l*l+c*c),u=l/h,f=c/h;return{len:h,nx:u,ny:f}},n=(o,a)=>{o===0?i.moveTo(a.x,a.y):i.lineTo(a.x,a.y)};let s=t[t.length-1];for(let o=0;o<t.length;o++){const a=t[o%t.length],l=a.radius??e;if(l<=0){n(o,a),s=a;continue}const c=t[(o+1)%t.length],h=r(a,s),u=r(a,c);if(h.len<1e-4||u.len<1e-4){n(o,a),s=a;continue}let f=Math.asin(h.nx*u.ny-h.ny*u.nx),d=1,p=!1;h.nx*u.nx-h.ny*-u.ny<0?f<0?f=Math.PI+f:(f=Math.PI-f,d=-1,p=!0):f>0&&(d=-1,p=!0);const m=f/2;let g,x=Math.abs(Math.cos(m)*l/Math.sin(m));x>Math.min(h.len/2,u.len/2)?(x=Math.min(h.len/2,u.len/2),g=Math.abs(x*Math.sin(m)/Math.cos(m))):g=l;const y=a.x+u.nx*x+-u.ny*g*d,b=a.y+u.ny*x+u.nx*g*d,_=Math.atan2(h.ny,h.nx)+Math.PI/2*d,v=Math.atan2(u.ny,u.nx)-Math.PI/2*d;o===0&&i.moveTo(y+Math.cos(_)*g,b+Math.sin(_)*g),i.arc(y,b,g,_,v,p),s=a}}function Xi(i,t,e,r){const n=(a,l)=>Math.sqrt((a.x-l.x)**2+(a.y-l.y)**2),s=(a,l,c)=>({x:a.x+(l.x-a.x)*c,y:a.y+(l.y-a.y)*c}),o=t.length;for(let a=0;a<o;a++){const l=t[(a+1)%o],c=l.radius??e;if(c<=0){a===0?i.moveTo(l.x,l.y):i.lineTo(l.x,l.y);continue}const h=t[a],u=t[(a+2)%o],f=n(h,l);let d;if(f<1e-4)d=l;else{const g=Math.min(f/2,c);d=s(l,h,g/f)}const p=n(u,l);let m;if(p<1e-4)m=l;else{const g=Math.min(p/2,c);m=s(l,u,g/p)}a===0?i.moveTo(d.x,d.y):i.lineTo(d.x,d.y),i.quadraticCurveTo(l.x,l.y,m.x,m.y,r)}}const Ki=new $;class Zi{constructor(t){this.shapePrimitives=[],this._currentPoly=null,this._bounds=new Zt,this._graphicsPath2D=t,this.signed=t.checkForHoles}moveTo(t,e){return this.startPoly(t,e),this}lineTo(t,e){this._ensurePoly();const r=this._currentPoly.points,n=r[r.length-2],s=r[r.length-1];return(n!==t||s!==e)&&r.push(t,e),this}arc(t,e,r,n,s,o){this._ensurePoly(!1);const a=this._currentPoly.points;return Er(a,t,e,r,n,s,o),this}arcTo(t,e,r,n,s){this._ensurePoly();const o=this._currentPoly.points;return $i(o,t,e,r,n,s),this}arcToSvg(t,e,r,n,s,o,a){const l=this._currentPoly.points;return Ni(l,this._currentPoly.lastX,this._currentPoly.lastY,o,a,t,e,r,n,s),this}bezierCurveTo(t,e,r,n,s,o,a){this._ensurePoly();const l=this._currentPoly;return Hr(this._currentPoly.points,l.lastX,l.lastY,t,e,r,n,s,o,a),this}quadraticCurveTo(t,e,r,n,s){this._ensurePoly();const o=this._currentPoly;return Vi(this._currentPoly.points,o.lastX,o.lastY,t,e,r,n,s),this}closePath(){return this.endPoly(!0),this}addPath(t,e){this.endPoly(),e&&!e.isIdentity()&&(t=t.clone(!0),t.transform(e));const r=this.shapePrimitives,n=r.length;for(let s=0;s<t.instructions.length;s++){const o=t.instructions[s];this[o.action](...o.data)}if(t.checkForHoles&&r.length-n>1){let s=null;for(let o=n;o<r.length;o++){const a=r[o];if(a.shape.type==="polygon"){const l=a.shape,c=s==null?void 0:s.shape;c&&c.containsPolygon(l)?(s.holes||(s.holes=[]),s.holes.push(a),r.copyWithin(o,o+1),r.length--,o--):s=a}}}return this}finish(t=!1){this.endPoly(t)}rect(t,e,r,n,s){return this.drawShape(new $(t,e,r,n),s),this}circle(t,e,r,n){return this.drawShape(new Ce(t,e,r),n),this}poly(t,e,r){const n=new Mt(t);return n.closePath=e,this.drawShape(n,r),this}regularPoly(t,e,r,n,s=0,o){n=Math.max(n|0,3);const a=-1*Math.PI/2+s,l=Math.PI*2/n,c=[];for(let h=0;h<n;h++){const u=a-h*l;c.push(t+r*Math.cos(u),e+r*Math.sin(u))}return this.poly(c,!0,o),this}roundPoly(t,e,r,n,s,o=0,a){if(n=Math.max(n|0,3),s<=0)return this.regularPoly(t,e,r,n,o);const l=r*Math.sin(Math.PI/n)-.001;s=Math.min(s,l);const c=-1*Math.PI/2+o,h=Math.PI*2/n,u=(n-2)*Math.PI/n/2;for(let f=0;f<n;f++){const d=f*h+c,p=t+r*Math.cos(d),m=e+r*Math.sin(d),g=d+Math.PI+u,x=d-Math.PI-u,y=p+s*Math.cos(g),b=m+s*Math.sin(g),_=p+s*Math.cos(x),v=m+s*Math.sin(x);f===0?this.moveTo(y,b):this.lineTo(y,b),this.quadraticCurveTo(p,m,_,v,a)}return this.closePath()}roundShape(t,e,r=!1,n){return t.length<3?this:(r?Xi(this,t,e,n):qi(this,t,e),this.closePath())}filletRect(t,e,r,n,s){if(s===0)return this.rect(t,e,r,n);const o=Math.min(r,n)/2,a=Math.min(o,Math.max(-o,s)),l=t+r,c=e+n,h=a<0?-a:0,u=Math.abs(a);return this.moveTo(t,e+u).arcTo(t+h,e+h,t+u,e,u).lineTo(l-u,e).arcTo(l-h,e+h,l,e+u,u).lineTo(l,c-u).arcTo(l-h,c-h,t+r-u,c,u).lineTo(t+u,c).arcTo(t+h,c-h,t,c-u,u).closePath()}chamferRect(t,e,r,n,s,o){if(s<=0)return this.rect(t,e,r,n);const a=Math.min(s,Math.min(r,n)/2),l=t+r,c=e+n,h=[t+a,e,l-a,e,l,e+a,l,c-a,l-a,c,t+a,c,t,c-a,t,e+a];for(let u=h.length-1;u>=2;u-=2)h[u]===h[u-2]&&h[u-1]===h[u-3]&&h.splice(u-1,2);return this.poly(h,!0,o)}ellipse(t,e,r,n,s){return this.drawShape(new Me(t,e,r,n),s),this}roundRect(t,e,r,n,s,o){return this.drawShape(new ke(t,e,r,n,s),o),this}drawShape(t,e){return this.endPoly(),this.shapePrimitives.push({shape:t,transform:e}),this}startPoly(t,e){let r=this._currentPoly;return r&&this.endPoly(),r=new Mt,r.points.push(t,e),this._currentPoly=r,this}endPoly(t=!1){const e=this._currentPoly;return e&&e.points.length>2&&(e.closePath=t,this.shapePrimitives.push({shape:e})),this._currentPoly=null,this}_ensurePoly(t=!0){if(!this._currentPoly&&(this._currentPoly=new Mt,t)){const e=this.shapePrimitives[this.shapePrimitives.length-1];if(e){let r=e.shape.x,n=e.shape.y;if(e.transform&&!e.transform.isIdentity()){const s=e.transform,o=r;r=s.a*r+s.c*n+s.tx,n=s.b*o+s.d*n+s.ty}this._currentPoly.points.push(r,n)}else this._currentPoly.points.push(0,0)}}buildPath(){const t=this._graphicsPath2D;this.shapePrimitives.length=0,this._currentPoly=null;for(let e=0;e<t.instructions.length;e++){const r=t.instructions[e];this[r.action](...r.data)}this.finish()}get bounds(){const t=this._bounds;t.clear();const e=this.shapePrimitives;for(let r=0;r<e.length;r++){const n=e[r],s=n.shape.getBounds(Ki);n.transform?t.addRect(s,n.transform):t.addRect(s)}return t}}class _t{constructor(t,e=!1){this.instructions=[],this.uid=Kt("graphicsPath"),this._dirty=!0,this.checkForHoles=e,typeof t=="string"?ci(t,this):this.instructions=(t==null?void 0:t.slice())??[]}get shapePath(){return this._shapePath||(this._shapePath=new Zi(this)),this._dirty&&(this._dirty=!1,this._shapePath.buildPath()),this._shapePath}addPath(t,e){return t=t.clone(),this.instructions.push({action:"addPath",data:[t,e]}),this._dirty=!0,this}arc(...t){return this.instructions.push({action:"arc",data:t}),this._dirty=!0,this}arcTo(...t){return this.instructions.push({action:"arcTo",data:t}),this._dirty=!0,this}arcToSvg(...t){return this.instructions.push({action:"arcToSvg",data:t}),this._dirty=!0,this}bezierCurveTo(...t){return this.instructions.push({action:"bezierCurveTo",data:t}),this._dirty=!0,this}bezierCurveToShort(t,e,r,n,s){const o=this.instructions[this.instructions.length-1],a=this.getLastPoint(gt.shared);let l=0,c=0;if(!o||o.action!=="bezierCurveTo")l=a.x,c=a.y;else{l=o.data[2],c=o.data[3];const h=a.x,u=a.y;l=h+(h-l),c=u+(u-c)}return this.instructions.push({action:"bezierCurveTo",data:[l,c,t,e,r,n,s]}),this._dirty=!0,this}closePath(){return this.instructions.push({action:"closePath",data:[]}),this._dirty=!0,this}ellipse(...t){return this.instructions.push({action:"ellipse",data:t}),this._dirty=!0,this}lineTo(...t){return this.instructions.push({action:"lineTo",data:t}),this._dirty=!0,this}moveTo(...t){return this.instructions.push({action:"moveTo",data:t}),this}quadraticCurveTo(...t){return this.instructions.push({action:"quadraticCurveTo",data:t}),this._dirty=!0,this}quadraticCurveToShort(t,e,r){const n=this.instructions[this.instructions.length-1],s=this.getLastPoint(gt.shared);let o=0,a=0;if(!n||n.action!=="quadraticCurveTo")o=s.x,a=s.y;else{o=n.data[0],a=n.data[1];const l=s.x,c=s.y;o=l+(l-o),a=c+(c-a)}return this.instructions.push({action:"quadraticCurveTo",data:[o,a,t,e,r]}),this._dirty=!0,this}rect(t,e,r,n,s){return this.instructions.push({action:"rect",data:[t,e,r,n,s]}),this._dirty=!0,this}circle(t,e,r,n){return this.instructions.push({action:"circle",data:[t,e,r,n]}),this._dirty=!0,this}roundRect(...t){return this.instructions.push({action:"roundRect",data:t}),this._dirty=!0,this}poly(...t){return this.instructions.push({action:"poly",data:t}),this._dirty=!0,this}regularPoly(...t){return this.instructions.push({action:"regularPoly",data:t}),this._dirty=!0,this}roundPoly(...t){return this.instructions.push({action:"roundPoly",data:t}),this._dirty=!0,this}roundShape(...t){return this.instructions.push({action:"roundShape",data:t}),this._dirty=!0,this}filletRect(...t){return this.instructions.push({action:"filletRect",data:t}),this._dirty=!0,this}chamferRect(...t){return this.instructions.push({action:"chamferRect",data:t}),this._dirty=!0,this}star(t,e,r,n,s,o,a){s||(s=n/2);const l=-1*Math.PI/2+o,c=r*2,h=Math.PI*2/c,u=[];for(let f=0;f<c;f++){const d=f%2?s:n,p=f*h+l;u.push(t+d*Math.cos(p),e+d*Math.sin(p))}return this.poly(u,!0,a),this}clone(t=!1){const e=new _t;if(e.checkForHoles=this.checkForHoles,!t)e.instructions=this.instructions.slice();else for(let r=0;r<this.instructions.length;r++){const n=this.instructions[r];e.instructions.push({action:n.action,data:n.data.slice()})}return e}clear(){return this.instructions.length=0,this._dirty=!0,this}transform(t){if(t.isIdentity())return this;const e=t.a,r=t.b,n=t.c,s=t.d,o=t.tx,a=t.ty;let l=0,c=0,h=0,u=0,f=0,d=0,p=0,m=0;for(let g=0;g<this.instructions.length;g++){const x=this.instructions[g],y=x.data;switch(x.action){case"moveTo":case"lineTo":l=y[0],c=y[1],y[0]=e*l+n*c+o,y[1]=r*l+s*c+a;break;case"bezierCurveTo":h=y[0],u=y[1],f=y[2],d=y[3],l=y[4],c=y[5],y[0]=e*h+n*u+o,y[1]=r*h+s*u+a,y[2]=e*f+n*d+o,y[3]=r*f+s*d+a,y[4]=e*l+n*c+o,y[5]=r*l+s*c+a;break;case"quadraticCurveTo":h=y[0],u=y[1],l=y[2],c=y[3],y[0]=e*h+n*u+o,y[1]=r*h+s*u+a,y[2]=e*l+n*c+o,y[3]=r*l+s*c+a;break;case"arcToSvg":l=y[5],c=y[6],p=y[0],m=y[1],y[0]=e*p+n*m,y[1]=r*p+s*m,y[5]=e*l+n*c+o,y[6]=r*l+s*c+a;break;case"circle":y[4]=Pt(y[3],t);break;case"rect":y[4]=Pt(y[4],t);break;case"ellipse":y[8]=Pt(y[8],t);break;case"roundRect":y[5]=Pt(y[5],t);break;case"addPath":y[0].transform(t);break;case"poly":y[2]=Pt(y[2],t);break;default:Y("unknown transform action",x.action);break}}return this._dirty=!0,this}get bounds(){return this.shapePath.bounds}getLastPoint(t){let e=this.instructions.length-1,r=this.instructions[e];if(!r)return t.x=0,t.y=0,t;for(;r.action==="closePath";){if(e--,e<0)return t.x=0,t.y=0,t;r=this.instructions[e]}switch(r.action){case"moveTo":case"lineTo":t.x=r.data[0],t.y=r.data[1];break;case"quadraticCurveTo":t.x=r.data[2],t.y=r.data[3];break;case"bezierCurveTo":t.x=r.data[4],t.y=r.data[5];break;case"arc":case"arcToSvg":t.x=r.data[5],t.y=r.data[6];break;case"addPath":r.data[0].getLastPoint(t);break}return t}}function Pt(i,t){return i?i.prepend(t):t.clone()}function D(i,t,e){const r=i.getAttribute(t);return r?Number(r):e}function Qi(i,t){const e=i.querySelectorAll("defs");for(let r=0;r<e.length;r++){const n=e[r];for(let s=0;s<n.children.length;s++){const o=n.children[s];switch(o.nodeName.toLowerCase()){case"lineargradient":t.defs[o.id]=Ji(o);break;case"radialgradient":t.defs[o.id]=ts();break}}}}function Ji(i){const t=D(i,"x1",0),e=D(i,"y1",0),r=D(i,"x2",1),n=D(i,"y2",0),s=i.getAttribute("gradientUnits")||"objectBoundingBox",o=new et(t,e,r,n,s==="objectBoundingBox"?"local":"global");for(let a=0;a<i.children.length;a++){const l=i.children[a],c=D(l,"offset",0),h=L.shared.setValue(l.getAttribute("stop-color")).toNumber();o.addColorStop(c,h)}return o}function ts(i){return Y("[SVG Parser] Radial gradients are not yet supported"),new et(0,0,1,0)}function tr(i){const t=i.match(/url\s*\(\s*['"]?\s*#([^'"\s)]+)\s*['"]?\s*\)/i);return t?t[1]:""}const er={fill:{type:"paint",default:0},"fill-opacity":{type:"number",default:1},stroke:{type:"paint",default:0},"stroke-width":{type:"number",default:1},"stroke-opacity":{type:"number",default:1},"stroke-linecap":{type:"string",default:"butt"},"stroke-linejoin":{type:"string",default:"miter"},"stroke-miterlimit":{type:"number",default:10},"stroke-dasharray":{type:"string",default:"none"},"stroke-dashoffset":{type:"number",default:0},opacity:{type:"number",default:1}};function Vr(i,t){const e=i.getAttribute("style"),r={},n={},s={strokeStyle:r,fillStyle:n,useFill:!1,useStroke:!1};for(const o in er){const a=i.getAttribute(o);a&&rr(t,s,o,a.trim())}if(e){const o=e.split(";");for(let a=0;a<o.length;a++){const l=o[a].trim(),[c,h]=l.split(":");er[c]&&rr(t,s,c,h.trim())}}return{strokeStyle:s.useStroke?r:null,fillStyle:s.useFill?n:null,useFill:s.useFill,useStroke:s.useStroke}}function rr(i,t,e,r){switch(e){case"stroke":if(r!=="none"){if(r.startsWith("url(")){const n=tr(r);t.strokeStyle.fill=i.defs[n]}else t.strokeStyle.color=L.shared.setValue(r).toNumber();t.useStroke=!0}break;case"stroke-width":t.strokeStyle.width=Number(r);break;case"fill":if(r!=="none"){if(r.startsWith("url(")){const n=tr(r);t.fillStyle.fill=i.defs[n]}else t.fillStyle.color=L.shared.setValue(r).toNumber();t.useFill=!0}break;case"fill-opacity":t.fillStyle.alpha=Number(r);break;case"stroke-opacity":t.strokeStyle.alpha=Number(r);break;case"opacity":t.fillStyle.alpha=Number(r),t.strokeStyle.alpha=Number(r);break}}function es(i,t){if(typeof i=="string"){const o=document.createElement("div");o.innerHTML=i.trim(),i=o.querySelector("svg")}const e={context:t,defs:{},path:new _t};Qi(i,e);const r=i.children,{fillStyle:n,strokeStyle:s}=Vr(i,e);for(let o=0;o<r.length;o++){const a=r[o];a.nodeName.toLowerCase()!=="defs"&&Or(a,e,n,s)}return t}function Or(i,t,e,r){const n=i.children,{fillStyle:s,strokeStyle:o}=Vr(i,t);s&&e?e={...e,...s}:s&&(e=s),o&&r?r={...r,...o}:o&&(r=o);const a=!e&&!r;a&&(e={color:0});let l,c,h,u,f,d,p,m,g,x,y,b,_,v,w,S,R;switch(i.nodeName.toLowerCase()){case"path":v=i.getAttribute("d"),i.getAttribute("fill-rule")==="evenodd"&&Y("SVG Evenodd fill rule not supported, your svg may render incorrectly"),w=new _t(v,!0),t.context.path(w),e&&t.context.fill(e),r&&t.context.stroke(r);break;case"circle":p=D(i,"cx",0),m=D(i,"cy",0),g=D(i,"r",0),t.context.ellipse(p,m,g,g),e&&t.context.fill(e),r&&t.context.stroke(r);break;case"rect":l=D(i,"x",0),c=D(i,"y",0),S=D(i,"width",0),R=D(i,"height",0),x=D(i,"rx",0),y=D(i,"ry",0),x||y?t.context.roundRect(l,c,S,R,x||y):t.context.rect(l,c,S,R),e&&t.context.fill(e),r&&t.context.stroke(r);break;case"ellipse":p=D(i,"cx",0),m=D(i,"cy",0),x=D(i,"rx",0),y=D(i,"ry",0),t.context.beginPath(),t.context.ellipse(p,m,x,y),e&&t.context.fill(e),r&&t.context.stroke(r);break;case"line":h=D(i,"x1",0),u=D(i,"y1",0),f=D(i,"x2",0),d=D(i,"y2",0),t.context.beginPath(),t.context.moveTo(h,u),t.context.lineTo(f,d),r&&t.context.stroke(r);break;case"polygon":_=i.getAttribute("points"),b=_.match(/\d+/g).map(k=>parseInt(k,10)),t.context.poly(b,!0),e&&t.context.fill(e),r&&t.context.stroke(r);break;case"polyline":_=i.getAttribute("points"),b=_.match(/\d+/g).map(k=>parseInt(k,10)),t.context.poly(b,!1),r&&t.context.stroke(r);break;case"g":case"svg":break;default:{Y(`[SVG parser] <${i.nodeName}> elements unsupported`);break}}a&&(e=null);for(let k=0;k<n.length;k++)Or(n[k],t,e,r)}function rs(i){return L.isColorLike(i)}function nr(i){return i instanceof Jt}function ir(i){return i instanceof et}function ns(i){return i instanceof E}function is(i,t,e){const r=L.shared.setValue(t??0);return i.color=r.toNumber(),i.alpha=r.alpha===1?e.alpha:r.alpha,i.texture=E.WHITE,{...e,...i}}function ss(i,t,e){return i.texture=t,{...e,...i}}function sr(i,t,e){return i.fill=t,i.color=16777215,i.texture=t.texture,i.matrix=t.transform,{...e,...i}}function or(i,t,e){return t.buildGradient(),i.fill=t,i.color=16777215,i.texture=t.texture,i.matrix=t.transform,i.textureSpace=t.textureSpace,{...e,...i}}function os(i,t){const e={...t,...i},r=L.shared.setValue(e.color);return e.alpha*=r.alpha,e.color=r.toNumber(),e}function ut(i,t){if(i==null)return null;const e={},r=i;return rs(i)?is(e,i,t):ns(i)?ss(e,i,t):nr(i)?sr(e,i,t):ir(i)?or(e,i,t):r.fill&&nr(r.fill)?sr(r,r.fill,t):r.fill&&ir(r.fill)?or(r,r.fill,t):os(r,t)}function Nt(i,t){const{width:e,alignment:r,miterLimit:n,cap:s,join:o,pixelLine:a,...l}=t,c=ut(i,l);return c?{width:e,alignment:r,miterLimit:n,cap:s,join:o,pixelLine:a,...c}:null}const as=new gt,ar=new H,Ge=class N extends be{constructor(){super(...arguments),this.uid=Kt("graphicsContext"),this.dirty=!0,this.batchMode="auto",this.instructions=[],this._activePath=new _t,this._transform=new H,this._fillStyle={...N.defaultFillStyle},this._strokeStyle={...N.defaultStrokeStyle},this._stateStack=[],this._tick=0,this._bounds=new Zt,this._boundsDirty=!0}clone(){const t=new N;return t.batchMode=this.batchMode,t.instructions=this.instructions.slice(),t._activePath=this._activePath.clone(),t._transform=this._transform.clone(),t._fillStyle={...this._fillStyle},t._strokeStyle={...this._strokeStyle},t._stateStack=this._stateStack.slice(),t._bounds=this._bounds.clone(),t._boundsDirty=!0,t}get fillStyle(){return this._fillStyle}set fillStyle(t){this._fillStyle=ut(t,N.defaultFillStyle)}get strokeStyle(){return this._strokeStyle}set strokeStyle(t){this._strokeStyle=Nt(t,N.defaultStrokeStyle)}setFillStyle(t){return this._fillStyle=ut(t,N.defaultFillStyle),this}setStrokeStyle(t){return this._strokeStyle=ut(t,N.defaultStrokeStyle),this}texture(t,e,r,n,s,o){return this.instructions.push({action:"texture",data:{image:t,dx:r||0,dy:n||0,dw:s||t.frame.width,dh:o||t.frame.height,transform:this._transform.clone(),alpha:this._fillStyle.alpha,style:e?L.shared.setValue(e).toNumber():16777215}}),this.onUpdate(),this}beginPath(){return this._activePath=new _t,this}fill(t,e){let r;const n=this.instructions[this.instructions.length-1];return this._tick===0&&n&&n.action==="stroke"?r=n.data.path:r=this._activePath.clone(),r?(t!=null&&(e!==void 0&&typeof t=="number"&&(A(I,"GraphicsContext.fill(color, alpha) is deprecated, use GraphicsContext.fill({ color, alpha }) instead"),t={color:t,alpha:e}),this._fillStyle=ut(t,N.defaultFillStyle)),this.instructions.push({action:"fill",data:{style:this.fillStyle,path:r}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}_initNextPathLocation(){const{x:t,y:e}=this._activePath.getLastPoint(gt.shared);this._activePath.clear(),this._activePath.moveTo(t,e)}stroke(t){let e;const r=this.instructions[this.instructions.length-1];return this._tick===0&&r&&r.action==="fill"?e=r.data.path:e=this._activePath.clone(),e?(t!=null&&(this._strokeStyle=Nt(t,N.defaultStrokeStyle)),this.instructions.push({action:"stroke",data:{style:this.strokeStyle,path:e}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}cut(){for(let t=0;t<2;t++){const e=this.instructions[this.instructions.length-1-t],r=this._activePath.clone();if(e&&(e.action==="stroke"||e.action==="fill"))if(e.data.hole)e.data.hole.addPath(r);else{e.data.hole=r;break}}return this._initNextPathLocation(),this}arc(t,e,r,n,s,o){this._tick++;const a=this._transform;return this._activePath.arc(a.a*t+a.c*e+a.tx,a.b*t+a.d*e+a.ty,r,n,s,o),this}arcTo(t,e,r,n,s){this._tick++;const o=this._transform;return this._activePath.arcTo(o.a*t+o.c*e+o.tx,o.b*t+o.d*e+o.ty,o.a*r+o.c*n+o.tx,o.b*r+o.d*n+o.ty,s),this}arcToSvg(t,e,r,n,s,o,a){this._tick++;const l=this._transform;return this._activePath.arcToSvg(t,e,r,n,s,l.a*o+l.c*a+l.tx,l.b*o+l.d*a+l.ty),this}bezierCurveTo(t,e,r,n,s,o,a){this._tick++;const l=this._transform;return this._activePath.bezierCurveTo(l.a*t+l.c*e+l.tx,l.b*t+l.d*e+l.ty,l.a*r+l.c*n+l.tx,l.b*r+l.d*n+l.ty,l.a*s+l.c*o+l.tx,l.b*s+l.d*o+l.ty,a),this}closePath(){var t;return this._tick++,(t=this._activePath)==null||t.closePath(),this}ellipse(t,e,r,n){return this._tick++,this._activePath.ellipse(t,e,r,n,this._transform.clone()),this}circle(t,e,r){return this._tick++,this._activePath.circle(t,e,r,this._transform.clone()),this}path(t){return this._tick++,this._activePath.addPath(t,this._transform.clone()),this}lineTo(t,e){this._tick++;const r=this._transform;return this._activePath.lineTo(r.a*t+r.c*e+r.tx,r.b*t+r.d*e+r.ty),this}moveTo(t,e){this._tick++;const r=this._transform,n=this._activePath.instructions,s=r.a*t+r.c*e+r.tx,o=r.b*t+r.d*e+r.ty;return n.length===1&&n[0].action==="moveTo"?(n[0].data[0]=s,n[0].data[1]=o,this):(this._activePath.moveTo(s,o),this)}quadraticCurveTo(t,e,r,n,s){this._tick++;const o=this._transform;return this._activePath.quadraticCurveTo(o.a*t+o.c*e+o.tx,o.b*t+o.d*e+o.ty,o.a*r+o.c*n+o.tx,o.b*r+o.d*n+o.ty,s),this}rect(t,e,r,n){return this._tick++,this._activePath.rect(t,e,r,n,this._transform.clone()),this}roundRect(t,e,r,n,s){return this._tick++,this._activePath.roundRect(t,e,r,n,s,this._transform.clone()),this}poly(t,e){return this._tick++,this._activePath.poly(t,e,this._transform.clone()),this}regularPoly(t,e,r,n,s=0,o){return this._tick++,this._activePath.regularPoly(t,e,r,n,s,o),this}roundPoly(t,e,r,n,s,o){return this._tick++,this._activePath.roundPoly(t,e,r,n,s,o),this}roundShape(t,e,r,n){return this._tick++,this._activePath.roundShape(t,e,r,n),this}filletRect(t,e,r,n,s){return this._tick++,this._activePath.filletRect(t,e,r,n,s),this}chamferRect(t,e,r,n,s,o){return this._tick++,this._activePath.chamferRect(t,e,r,n,s,o),this}star(t,e,r,n,s=0,o=0){return this._tick++,this._activePath.star(t,e,r,n,s,o,this._transform.clone()),this}svg(t){return this._tick++,es(t,this),this}restore(){const t=this._stateStack.pop();return t&&(this._transform=t.transform,this._fillStyle=t.fillStyle,this._strokeStyle=t.strokeStyle),this}save(){return this._stateStack.push({transform:this._transform.clone(),fillStyle:{...this._fillStyle},strokeStyle:{...this._strokeStyle}}),this}getTransform(){return this._transform}resetTransform(){return this._transform.identity(),this}rotate(t){return this._transform.rotate(t),this}scale(t,e=t){return this._transform.scale(t,e),this}setTransform(t,e,r,n,s,o){return t instanceof H?(this._transform.set(t.a,t.b,t.c,t.d,t.tx,t.ty),this):(this._transform.set(t,e,r,n,s,o),this)}transform(t,e,r,n,s,o){return t instanceof H?(this._transform.append(t),this):(ar.set(t,e,r,n,s,o),this._transform.append(ar),this)}translate(t,e=t){return this._transform.translate(t,e),this}clear(){return this._activePath.clear(),this.instructions.length=0,this.resetTransform(),this.onUpdate(),this}onUpdate(){this.dirty||(this.emit("update",this,16),this.dirty=!0,this._boundsDirty=!0)}get bounds(){if(!this._boundsDirty)return this._bounds;const t=this._bounds;t.clear();for(let e=0;e<this.instructions.length;e++){const r=this.instructions[e],n=r.action;if(n==="fill"){const s=r.data;t.addBounds(s.path.bounds)}else if(n==="texture"){const s=r.data;t.addFrame(s.dx,s.dy,s.dx+s.dw,s.dy+s.dh,s.transform)}if(n==="stroke"){const s=r.data,o=s.style.alignment,a=s.style.width*(1-o),l=s.path.bounds;t.addFrame(l.minX-a,l.minY-a,l.maxX+a,l.maxY+a)}}return t}containsPoint(t){var n;if(!this.bounds.containsPoint(t.x,t.y))return!1;const e=this.instructions;let r=!1;for(let s=0;s<e.length;s++){const o=e[s],a=o.data,l=a.path;if(!o.action||!l)continue;const c=a.style,h=l.shapePath.shapePrimitives;for(let u=0;u<h.length;u++){const f=h[u].shape;if(!c||!f)continue;const d=h[u].transform,p=d?d.applyInverse(t,as):t;if(o.action==="fill")r=f.contains(p.x,p.y);else{const g=c;r=f.strokeContains(p.x,p.y,g.width,g.alignment)}const m=a.hole;if(m){const g=(n=m.shapePath)==null?void 0:n.shapePrimitives;if(g)for(let x=0;x<g.length;x++)g[x].shape.contains(p.x,p.y)&&(r=!1)}if(r)return!0}}return r}destroy(t=!1){if(this._stateStack.length=0,this._transform=null,this.emit("destroy",this),this.removeAllListeners(),typeof t=="boolean"?t:t==null?void 0:t.texture){const r=typeof t=="boolean"?t:t==null?void 0:t.textureSource;this._fillStyle.texture&&this._fillStyle.texture.destroy(r),this._strokeStyle.texture&&this._strokeStyle.texture.destroy(r)}this._fillStyle=null,this._strokeStyle=null,this.instructions=null,this._activePath=null,this._bounds=null,this._stateStack=null,this.customShader=null,this._transform=null}};Ge.defaultFillStyle={color:16777215,alpha:1,texture:E.WHITE,matrix:null,fill:null,textureSpace:"local"};Ge.defaultStrokeStyle={width:1,color:16777215,alpha:1,alignment:.5,miterLimit:10,cap:"butt",join:"miter",texture:E.WHITE,matrix:null,fill:null,textureSpace:"local",pixelLine:!1};let j=Ge;const lr=["align","breakWords","cssOverrides","fontVariant","fontWeight","leading","letterSpacing","lineHeight","padding","textBaseline","trim","whiteSpace","wordWrap","wordWrapWidth","fontFamily","fontStyle","fontSize"];function ls(i){const t=[];let e=0;for(let r=0;r<lr.length;r++){const n=`_${lr[r]}`;t[e++]=i[n]}return e=$r(i._fill,t,e),e=cs(i._stroke,t,e),e=us(i.dropShadow,t,e),e=hs(i.filters,t,e),t.join("-")}function hs(i,t,e){if(!i)return e;for(const r of i)t[e++]=r.uid;return e}function $r(i,t,e){var r;return i&&(t[e++]=i.color,t[e++]=i.alpha,t[e++]=(r=i.fill)==null?void 0:r.styleKey),e}function cs(i,t,e){return i&&(e=$r(i,t,e),t[e++]=i.width,t[e++]=i.alignment,t[e++]=i.cap,t[e++]=i.join,t[e++]=i.miterLimit),e}function us(i,t,e){return i&&(t[e++]=i.alpha,t[e++]=i.angle,t[e++]=i.blur,t[e++]=i.distance,t[e++]=L.shared.setValue(i.color).toNumber()),e}const Ue=class pt extends be{constructor(t={}){super(),ds(t);const e={...pt.defaultTextStyle,...t};for(const r in e){const n=r;this[n]=e[r]}this.update()}get align(){return this._align}set align(t){this._align=t,this.update()}get breakWords(){return this._breakWords}set breakWords(t){this._breakWords=t,this.update()}get dropShadow(){return this._dropShadow}set dropShadow(t){t!==null&&typeof t=="object"?this._dropShadow=this._createProxy({...pt.defaultDropShadow,...t}):this._dropShadow=t?this._createProxy({...pt.defaultDropShadow}):null,this.update()}get fontFamily(){return this._fontFamily}set fontFamily(t){this._fontFamily=t,this.update()}get fontSize(){return this._fontSize}set fontSize(t){typeof t=="string"?this._fontSize=parseInt(t,10):this._fontSize=t,this.update()}get fontStyle(){return this._fontStyle}set fontStyle(t){this._fontStyle=t.toLowerCase(),this.update()}get fontVariant(){return this._fontVariant}set fontVariant(t){this._fontVariant=t,this.update()}get fontWeight(){return this._fontWeight}set fontWeight(t){this._fontWeight=t,this.update()}get leading(){return this._leading}set leading(t){this._leading=t,this.update()}get letterSpacing(){return this._letterSpacing}set letterSpacing(t){this._letterSpacing=t,this.update()}get lineHeight(){return this._lineHeight}set lineHeight(t){this._lineHeight=t,this.update()}get padding(){return this._padding}set padding(t){this._padding=t,this.update()}get filters(){return this._filters}set filters(t){this._filters=t,this.update()}get trim(){return this._trim}set trim(t){this._trim=t,this.update()}get textBaseline(){return this._textBaseline}set textBaseline(t){this._textBaseline=t,this.update()}get whiteSpace(){return this._whiteSpace}set whiteSpace(t){this._whiteSpace=t,this.update()}get wordWrap(){return this._wordWrap}set wordWrap(t){this._wordWrap=t,this.update()}get wordWrapWidth(){return this._wordWrapWidth}set wordWrapWidth(t){this._wordWrapWidth=t,this.update()}get fill(){return this._originalFill}set fill(t){t!==this._originalFill&&(this._originalFill=t,this._isFillStyle(t)&&(this._originalFill=this._createProxy({...j.defaultFillStyle,...t},()=>{this._fill=ut({...this._originalFill},j.defaultFillStyle)})),this._fill=ut(t===0?"black":t,j.defaultFillStyle),this.update())}get stroke(){return this._originalStroke}set stroke(t){t!==this._originalStroke&&(this._originalStroke=t,this._isFillStyle(t)&&(this._originalStroke=this._createProxy({...j.defaultStrokeStyle,...t},()=>{this._stroke=Nt({...this._originalStroke},j.defaultStrokeStyle)})),this._stroke=Nt(t,j.defaultStrokeStyle),this.update())}_generateKey(){return this._styleKey=ls(this),this._styleKey}update(){this._styleKey=null,this.emit("update",this)}reset(){const t=pt.defaultTextStyle;for(const e in t)this[e]=t[e]}get styleKey(){return this._styleKey||this._generateKey()}clone(){return new pt({align:this.align,breakWords:this.breakWords,dropShadow:this._dropShadow?{...this._dropShadow}:null,fill:this._fill,fontFamily:this.fontFamily,fontSize:this.fontSize,fontStyle:this.fontStyle,fontVariant:this.fontVariant,fontWeight:this.fontWeight,leading:this.leading,letterSpacing:this.letterSpacing,lineHeight:this.lineHeight,padding:this.padding,stroke:this._stroke,textBaseline:this.textBaseline,whiteSpace:this.whiteSpace,wordWrap:this.wordWrap,wordWrapWidth:this.wordWrapWidth,filters:this._filters?[...this._filters]:void 0})}_getFinalPadding(){let t=0;if(this._filters)for(let e=0;e<this._filters.length;e++)t+=this._filters[e].padding;return Math.max(this._padding,t)}destroy(t=!1){var r,n,s,o;if(this.removeAllListeners(),typeof t=="boolean"?t:t==null?void 0:t.texture){const a=typeof t=="boolean"?t:t==null?void 0:t.textureSource;(r=this._fill)!=null&&r.texture&&this._fill.texture.destroy(a),(n=this._originalFill)!=null&&n.texture&&this._originalFill.texture.destroy(a),(s=this._stroke)!=null&&s.texture&&this._stroke.texture.destroy(a),(o=this._originalStroke)!=null&&o.texture&&this._originalStroke.texture.destroy(a)}this._fill=null,this._stroke=null,this.dropShadow=null,this._originalStroke=null,this._originalFill=null}_createProxy(t,e){return new Proxy(t,{set:(r,n,s)=>(r[n]=s,e==null||e(n,s),this.update(),!0)})}_isFillStyle(t){return(t??null)!==null&&!(L.isColorLike(t)||t instanceof et||t instanceof Jt)}};Ue.defaultDropShadow={alpha:1,angle:Math.PI/6,blur:0,color:"black",distance:5};Ue.defaultTextStyle={align:"left",breakWords:!1,dropShadow:null,fill:"black",fontFamily:"Arial",fontSize:26,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",leading:0,letterSpacing:0,lineHeight:0,padding:0,stroke:null,textBaseline:"alphabetic",trim:!1,whiteSpace:"pre",wordWrap:!1,wordWrapWidth:100};let nt=Ue;function ds(i){const t=i;if(typeof t.dropShadow=="boolean"&&t.dropShadow){const e=nt.defaultDropShadow;i.dropShadow={alpha:t.dropShadowAlpha??e.alpha,angle:t.dropShadowAngle??e.angle,blur:t.dropShadowBlur??e.blur,color:t.dropShadowColor??e.color,distance:t.dropShadowDistance??e.distance}}if(t.strokeThickness!==void 0){A(I,"strokeThickness is now a part of stroke");const e=t.stroke;let r={};if(L.isColorLike(e))r.color=e;else if(e instanceof et||e instanceof Jt)r.fill=e;else if(Object.hasOwnProperty.call(e,"color")||Object.hasOwnProperty.call(e,"fill"))r=e;else throw new Error("Invalid stroke value.");i.stroke={...r,width:t.strokeThickness}}if(Array.isArray(t.fillGradientStops)){A(I,"gradient fill is now a fill pattern: `new FillGradient(...)`");let e;i.fontSize==null?i.fontSize=nt.defaultTextStyle.fontSize:typeof i.fontSize=="string"?e=parseInt(i.fontSize,10):e=i.fontSize;const r=new et({start:{x:0,y:0},end:{x:0,y:(e||0)*1.7}}),n=t.fillGradientStops.map(s=>L.shared.setValue(s).toNumber());n.forEach((s,o)=>{const a=o/(n.length-1);r.addColorStop(a,s)}),i.fill={fill:r}}}const hr=1e5;function qt(i,t,e,r=0){if(i.texture===E.WHITE&&!i.fill)return L.shared.setValue(i.color).setAlpha(i.alpha??1).toHexa();if(i.fill){if(i.fill instanceof Jt){const n=i.fill,s=t.createPattern(n.texture.source.resource,"repeat"),o=n.transform.copyTo(H.shared);return o.scale(n.texture.frame.width,n.texture.frame.height),s.setTransform(o),s}else if(i.fill instanceof et){const n=i.fill,s=n.type==="linear",o=n.textureSpace==="local";let a=1,l=1;o&&e&&(a=e.width+r,l=e.height+r);let c,h=!1;if(s){const{start:u,end:f}=n;c=t.createLinearGradient(u.x*a,u.y*l,f.x*a,f.y*l),h=Math.abs(f.x-u.x)<Math.abs((f.y-u.y)*.1)}else{const{center:u,innerRadius:f,outerCenter:d,outerRadius:p}=n;c=t.createRadialGradient(u.x*a,u.y*l,f*a,d.x*a,d.y*l,p*a)}if(h&&o&&e){const u=e.lineHeight/l;for(let f=0;f<e.lines.length;f++){const d=(f*e.lineHeight+r/2)/l;n.colorStops.forEach(p=>{const m=d+p.offset*u;c.addColorStop(Math.floor(m*hr)/hr,L.shared.setValue(p.color).toHex())})}}else n.colorStops.forEach(u=>{c.addColorStop(u.offset,L.shared.setValue(u.color).toHex())});return c}}else{const n=t.createPattern(i.texture.source.resource,"repeat"),s=i.matrix.copyTo(H.shared);return s.scale(i.texture.frame.width,i.texture.frame.height),n.setTransform(s),n}return Y("FillStyle not recognised",i),"red"}const jr=class Yr extends ii{constructor(t){super(),this.resolution=1,this.pages=[],this._padding=0,this._measureCache=Object.create(null),this._currentChars=[],this._currentX=0,this._currentY=0,this._currentMaxCharHeight=0,this._currentPageIndex=-1,this._skipKerning=!1;const e={...Yr.defaultOptions,...t};this._textureSize=e.textureSize,this._mipmap=e.mipmap;const r=e.style.clone();e.overrideFill&&(r._fill.color=16777215,r._fill.alpha=1,r._fill.texture=E.WHITE,r._fill.fill=null),this.applyFillAsTint=e.overrideFill;const n=r.fontSize;r.fontSize=this.baseMeasurementFontSize;const s=Yt(r);e.overrideSize?r._stroke&&(r._stroke.width*=this.baseRenderedFontSize/n):r.fontSize=this.baseRenderedFontSize=n,this._style=r,this._skipKerning=e.skipKerning??!1,this.resolution=e.resolution??1,this._padding=e.padding??4,e.textureStyle&&(this._textureStyle=e.textureStyle instanceof Ft?e.textureStyle:new Ft(e.textureStyle)),this.fontMetrics=Z.measureFont(s),this.lineHeight=r.lineHeight||this.fontMetrics.fontSize||r.fontSize}ensureCharacters(t){var g,x;const e=Z.graphemeSegmenter(t).filter(y=>!this._currentChars.includes(y)).filter((y,b,_)=>_.indexOf(y)===b);if(!e.length)return;this._currentChars=[...this._currentChars,...e];let r;this._currentPageIndex===-1?r=this._nextPage():r=this.pages[this._currentPageIndex];let{canvas:n,context:s}=r.canvasAndContext,o=r.texture.source;const a=this._style;let l=this._currentX,c=this._currentY,h=this._currentMaxCharHeight;const u=this.baseRenderedFontSize/this.baseMeasurementFontSize,f=this._padding*u;let d=!1;const p=n.width/this.resolution,m=n.height/this.resolution;for(let y=0;y<e.length;y++){const b=e[y],_=Z.measureText(b,a,n,!1);_.lineHeight=_.height;const v=_.width*u,w=Math.ceil((a.fontStyle==="italic"?2:1)*v),S=_.height*u,R=w+f*2,k=S+f*2;if(d=!1,b!==`
`&&b!=="\r"&&b!=="	"&&b!==" "&&(d=!0,h=Math.ceil(Math.max(k,h))),l+R>p&&(c+=h,h=k,l=0,c+h>m)){o.update();const T=this._nextPage();n=T.canvasAndContext.canvas,s=T.canvasAndContext.context,o=T.texture.source,l=0,c=0,h=0}const P=v/u-(((g=a.dropShadow)==null?void 0:g.distance)??0)-(((x=a._stroke)==null?void 0:x.width)??0);if(this.chars[b]={id:b.codePointAt(0),xOffset:-this._padding,yOffset:-this._padding,xAdvance:P,kerning:{}},d){this._drawGlyph(s,_,l+f,c+f,u,a);const T=o.width*u,U=o.height*u,G=new $(l/T*o.width,c/U*o.height,R/T*o.width,k/U*o.height);this.chars[b].texture=new E({source:o,frame:G}),l+=Math.ceil(R)}}o.update(),this._currentX=l,this._currentY=c,this._currentMaxCharHeight=h,this._skipKerning&&this._applyKerning(e,s)}get pageTextures(){return A(I,"BitmapFont.pageTextures is deprecated, please use BitmapFont.pages instead."),this.pages}_applyKerning(t,e){const r=this._measureCache;for(let n=0;n<t.length;n++){const s=t[n];for(let o=0;o<this._currentChars.length;o++){const a=this._currentChars[o];let l=r[s];l||(l=r[s]=e.measureText(s).width);let c=r[a];c||(c=r[a]=e.measureText(a).width);let h=e.measureText(s+a).width,u=h-(l+c);u&&(this.chars[s].kerning[a]=u),h=e.measureText(s+a).width,u=h-(l+c),u&&(this.chars[a].kerning[s]=u)}}}_nextPage(){this._currentPageIndex++;const t=this.resolution,e=xt.getOptimalCanvasAndContext(this._textureSize,this._textureSize,t);this._setupContext(e.context,this._style,t);const r=t*(this.baseRenderedFontSize/this.baseMeasurementFontSize),n=new E({source:new ue({resource:e.canvas,resolution:r,alphaMode:"premultiply-alpha-on-upload",autoGenerateMipmaps:this._mipmap})});this._textureStyle&&(n.source.style=this._textureStyle);const s={canvasAndContext:e,texture:n};return this.pages[this._currentPageIndex]=s,s}_setupContext(t,e,r){e.fontSize=this.baseRenderedFontSize,t.scale(r,r),t.font=Yt(e),e.fontSize=this.baseMeasurementFontSize,t.textBaseline=e.textBaseline;const n=e._stroke,s=(n==null?void 0:n.width)??0;if(n&&(t.lineWidth=s,t.lineJoin=n.join,t.miterLimit=n.miterLimit,t.strokeStyle=qt(n,t)),e._fill&&(t.fillStyle=qt(e._fill,t)),e.dropShadow){const o=e.dropShadow,a=L.shared.setValue(o.color).toArray(),l=o.blur*r,c=o.distance*r;t.shadowColor=`rgba(${a[0]*255},${a[1]*255},${a[2]*255},${o.alpha})`,t.shadowBlur=l,t.shadowOffsetX=Math.cos(o.angle)*c,t.shadowOffsetY=Math.sin(o.angle)*c}else t.shadowColor="black",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0}_drawGlyph(t,e,r,n,s,o){const a=e.text,l=e.fontProperties,c=o._stroke,h=((c==null?void 0:c.width)??0)*s,u=r+h/2,f=n-h/2,d=l.descent*s,p=e.lineHeight*s;let m=!1;o.stroke&&h&&(m=!0,t.strokeText(a,u,f+p-d));const{shadowBlur:g,shadowOffsetX:x,shadowOffsetY:y}=t;o._fill&&(m&&(t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0),t.fillText(a,u,f+p-d)),m&&(t.shadowBlur=g,t.shadowOffsetX=x,t.shadowOffsetY=y)}destroy(){super.destroy();for(let t=0;t<this.pages.length;t++){const{canvasAndContext:e,texture:r}=this.pages[t];xt.returnCanvasAndContext(e),r.destroy(!0)}this.pages=null}};jr.defaultOptions={textureSize:512,style:new nt,mipmap:!0};let cr=jr;function Nr(i,t,e,r){const n={width:0,height:0,offsetY:0,scale:t.fontSize/e.baseMeasurementFontSize,lines:[{width:0,charPositions:[],spaceWidth:0,spacesIndex:[],chars:[]}]};n.offsetY=e.baseLineOffset;let s=n.lines[0],o=null,a=!0;const l={width:0,start:0,index:0,positions:[],chars:[]},c=g=>{const x=s.width;for(let y=0;y<l.index;y++){const b=g.positions[y];s.chars.push(g.chars[y]),s.charPositions.push(b+x)}s.width+=g.width,a=!1,l.width=0,l.index=0,l.chars.length=0},h=()=>{let g=s.chars.length-1;if(r){let x=s.chars[g];for(;x===" ";)s.width-=e.chars[x].xAdvance,x=s.chars[--g]}n.width=Math.max(n.width,s.width),s={width:0,charPositions:[],chars:[],spaceWidth:0,spacesIndex:[]},a=!0,n.lines.push(s),n.height+=e.lineHeight},u=e.baseMeasurementFontSize/t.fontSize,f=t.letterSpacing*u,d=t.wordWrapWidth*u,p=t.wordWrap&&t.breakWords,m=g=>g-f>d;for(let g=0;g<i.length+1;g++){let x;const y=g===i.length;y||(x=i[g]);const b=e.chars[x]||e.chars[" "];if(/(?:\s)/.test(x)||x==="\r"||x===`
`||y){if(!a&&t.wordWrap&&m(s.width+l.width)?(h(),c(l),y||s.charPositions.push(0)):(l.start=s.width,c(l),y||s.charPositions.push(0)),x==="\r"||x===`
`)s.width!==0&&h();else if(!y){const S=b.xAdvance+(b.kerning[o]||0)+f;s.width+=S,s.spaceWidth=S,s.spacesIndex.push(s.charPositions.length),s.chars.push(x)}}else{const w=b.kerning[o]||0,S=b.xAdvance+w+f;p&&m(s.width+l.width+S)&&(c(l),h()),l.positions[l.index++]=l.width+w,l.chars.push(x),l.width+=S}o=x}return h(),t.align==="center"?fs(n):t.align==="right"?ps(n):t.align==="justify"&&gs(n),n}function fs(i){for(let t=0;t<i.lines.length;t++){const e=i.lines[t],r=i.width/2-e.width/2;for(let n=0;n<e.charPositions.length;n++)e.charPositions[n]+=r}}function ps(i){for(let t=0;t<i.lines.length;t++){const e=i.lines[t],r=i.width-e.width;for(let n=0;n<e.charPositions.length;n++)e.charPositions[n]+=r}}function gs(i){const t=i.width;for(let e=0;e<i.lines.length;e++){const r=i.lines[e];let n=0,s=r.spacesIndex[n++],o=0;const a=r.spacesIndex.length,c=(t-r.width)/a;for(let h=0;h<r.charPositions.length;h++)h===s&&(s=r.spacesIndex[n++],o+=c),r.charPositions[h]+=o}}function xs(i){if(i==="")return[];typeof i=="string"&&(i=[i]);const t=[];for(let e=0,r=i.length;e<r;e++){const n=i[e];if(Array.isArray(n)){if(n.length!==2)throw new Error(`[BitmapFont]: Invalid character range length, expecting 2 got ${n.length}.`);if(n[0].length===0||n[1].length===0)throw new Error("[BitmapFont]: Invalid character delimiter.");const s=n[0].charCodeAt(0),o=n[1].charCodeAt(0);if(o<s)throw new Error("[BitmapFont]: Invalid character range.");for(let a=s,l=o;a<=l;a++)t.push(String.fromCharCode(a))}else t.push(...Array.from(n))}if(t.length===0)throw new Error("[BitmapFont]: Empty set when resolving characters.");return t}let Vt=0;class ms{constructor(){this.ALPHA=[["a","z"],["A","Z"]," "],this.NUMERIC=[["0","9"]],this.ALPHANUMERIC=[["a","z"],["A","Z"],["0","9"]," "],this.ASCII=[[" ","~"]],this.defaultOptions={chars:this.ALPHANUMERIC,resolution:1,padding:4,skipKerning:!1,textureStyle:null}}getFont(t,e){var o;let r=`${e.fontFamily}-bitmap`,n=!0;if(e._fill.fill&&!e._stroke)r+=e._fill.fill.styleKey,n=!1;else if(e._stroke||e.dropShadow){let a=e.styleKey;a=a.substring(0,a.lastIndexOf("-")),r=`${a}-bitmap`,n=!1}if(!q.has(r)){const a=new cr({style:e,overrideFill:n,overrideSize:!0,...this.defaultOptions});Vt++,Vt>50&&Y("BitmapText",`You have dynamically created ${Vt} bitmap fonts, this can be inefficient. Try pre installing your font styles using \`BitmapFont.install({name:"style1", style})\``),a.once("destroy",()=>{Vt--,q.remove(r)}),q.set(r,a)}const s=q.get(r);return(o=s.ensureCharacters)==null||o.call(s,t),s}getLayout(t,e,r=!0){const n=this.getFont(t,e),s=Z.graphemeSegmenter(t);return Nr(s,e,n,r)}measureText(t,e,r=!0){return this.getLayout(t,e,r)}install(...t){var c,h,u,f;let e=t[0];typeof e=="string"&&(e={name:e,style:t[1],chars:(c=t[2])==null?void 0:c.chars,resolution:(h=t[2])==null?void 0:h.resolution,padding:(u=t[2])==null?void 0:u.padding,skipKerning:(f=t[2])==null?void 0:f.skipKerning},A(I,"BitmapFontManager.install(name, style, options) is deprecated, use BitmapFontManager.install({name, style, ...options})"));const r=e==null?void 0:e.name;if(!r)throw new Error("[BitmapFontManager] Property `name` is required.");e={...this.defaultOptions,...e};const n=e.style,s=n instanceof nt?n:new nt(n),o=s._fill.fill!==null&&s._fill.fill!==void 0,a=new cr({style:s,overrideFill:o,skipKerning:e.skipKerning,padding:e.padding,resolution:e.resolution,overrideSize:!1,textureStyle:e.textureStyle}),l=xs(e.chars);return a.ensureCharacters(l.join("")),q.set(`${r}-bitmap`,a),a.once("destroy",()=>q.remove(`${r}-bitmap`)),a}uninstall(t){const e=`${t}-bitmap`,r=q.get(e);r&&r.destroy()}}const ys=new ms;class qr{constructor(t){this._renderer=t}push(t,e,r){this._renderer.renderPipes.batch.break(r),r.add({renderPipeId:"filter",canBundle:!1,action:"pushFilter",container:e,filterEffect:t})}pop(t,e,r){this._renderer.renderPipes.batch.break(r),r.add({renderPipeId:"filter",action:"popFilter",canBundle:!1})}execute(t){t.action==="pushFilter"?this._renderer.filter.push(t):t.action==="popFilter"&&this._renderer.filter.pop()}destroy(){this._renderer=null}}qr.extension={type:[B.WebGLPipes,B.WebGPUPipes,B.CanvasPipes],name:"filter"};function _s(i,t){t.clear();const e=t.matrix;for(let r=0;r<i.length;r++){const n=i[r];n.globalDisplayStatus<7||(t.matrix=n.worldTransform,t.addBounds(n.bounds))}return t.matrix=e,t}const bs=new Se({attributes:{aPosition:{buffer:new Float32Array([0,0,1,0,1,1,0,1]),format:"float32x2",stride:2*4,offset:0}},indexBuffer:new Uint32Array([0,1,2,0,2,3])});class Ss{constructor(){this.skip=!1,this.inputTexture=null,this.backTexture=null,this.filters=null,this.bounds=new Zt,this.container=null,this.blendRequired=!1,this.outputRenderSurface=null,this.globalFrame={x:0,y:0,width:0,height:0}}}class Xr{constructor(t){this._filterStackIndex=0,this._filterStack=[],this._filterGlobalUniforms=new mt({uInputSize:{value:new Float32Array(4),type:"vec4<f32>"},uInputPixel:{value:new Float32Array(4),type:"vec4<f32>"},uInputClamp:{value:new Float32Array(4),type:"vec4<f32>"},uOutputFrame:{value:new Float32Array(4),type:"vec4<f32>"},uGlobalFrame:{value:new Float32Array(4),type:"vec4<f32>"},uOutputTexture:{value:new Float32Array(4),type:"vec4<f32>"}}),this._globalFilterBindGroup=new vr({}),this.renderer=t}get activeBackTexture(){var t;return(t=this._activeFilterData)==null?void 0:t.backTexture}push(t){const e=this.renderer,r=t.filterEffect.filters,n=this._pushFilterData();n.skip=!1,n.filters=r,n.container=t.container,n.outputRenderSurface=e.renderTarget.renderSurface;const s=e.renderTarget.renderTarget.colorTexture.source,o=s.resolution,a=s.antialias;if(r.length===0){n.skip=!0;return}const l=n.bounds;if(this._calculateFilterArea(t,l),this._calculateFilterBounds(n,e.renderTarget.rootViewPort,a,o,1),n.skip)return;const c=this._getPreviousFilterData(),h=this._findFilterResolution(o);let u=0,f=0;c&&(u=c.bounds.minX,f=c.bounds.minY),this._calculateGlobalFrame(n,u,f,h,s.width,s.height),this._setupFilterTextures(n,l,e,c)}generateFilteredTexture({texture:t,filters:e}){const r=this._pushFilterData();this._activeFilterData=r,r.skip=!1,r.filters=e;const n=t.source,s=n.resolution,o=n.antialias;if(e.length===0)return r.skip=!0,t;const a=r.bounds;if(a.addRect(t.frame),this._calculateFilterBounds(r,a.rectangle,o,s,0),r.skip)return t;const l=s;this._calculateGlobalFrame(r,0,0,l,n.width,n.height),r.outputRenderSurface=X.getOptimalTexture(a.width,a.height,r.resolution,r.antialias),r.backTexture=E.EMPTY,r.inputTexture=t,this.renderer.renderTarget.finishRenderPass(),this._applyFiltersToTexture(r,!0);const f=r.outputRenderSurface;return f.source.alphaMode="premultiplied-alpha",f}pop(){const t=this.renderer,e=this._popFilterData();e.skip||(t.globalUniforms.pop(),t.renderTarget.finishRenderPass(),this._activeFilterData=e,this._applyFiltersToTexture(e,!1),e.blendRequired&&X.returnTexture(e.backTexture),X.returnTexture(e.inputTexture))}getBackTexture(t,e,r){const n=t.colorTexture.source._resolution,s=X.getOptimalTexture(e.width,e.height,n,!1);let o=e.minX,a=e.minY;r&&(o-=r.minX,a-=r.minY),o=Math.floor(o*n),a=Math.floor(a*n);const l=Math.ceil(e.width*n),c=Math.ceil(e.height*n);return this.renderer.renderTarget.copyToTexture(t,s,{x:o,y:a},{width:l,height:c},{x:0,y:0}),s}applyFilter(t,e,r,n){const s=this.renderer,o=this._activeFilterData,l=o.outputRenderSurface===r,c=s.renderTarget.rootRenderTarget.colorTexture.source._resolution,h=this._findFilterResolution(c);let u=0,f=0;if(l){const d=this._findPreviousFilterOffset();u=d.x,f=d.y}this._updateFilterUniforms(e,r,o,u,f,h,l,n),this._setupBindGroupsAndRender(t,e,s)}calculateSpriteMatrix(t,e){const r=this._activeFilterData,n=t.set(r.inputTexture._source.width,0,0,r.inputTexture._source.height,r.bounds.minX,r.bounds.minY),s=e.worldTransform.copyTo(H.shared),o=e.renderGroup||e.parentRenderGroup;return o&&o.cacheToLocalTransform&&s.prepend(o.cacheToLocalTransform),s.invert(),n.prepend(s),n.scale(1/e.texture.frame.width,1/e.texture.frame.height),n.translate(e.anchor.x,e.anchor.y),n}destroy(){}_setupBindGroupsAndRender(t,e,r){if(r.renderPipes.uniformBatch){const n=r.renderPipes.uniformBatch.getUboResource(this._filterGlobalUniforms);this._globalFilterBindGroup.setResource(n,0)}else this._globalFilterBindGroup.setResource(this._filterGlobalUniforms,0);this._globalFilterBindGroup.setResource(e.source,1),this._globalFilterBindGroup.setResource(e.source.style,2),t.groups[0]=this._globalFilterBindGroup,r.encoder.draw({geometry:bs,shader:t,state:t._state,topology:"triangle-list"}),r.type===we.WEBGL&&r.renderTarget.finishRenderPass()}_setupFilterTextures(t,e,r,n){if(t.backTexture=E.EMPTY,t.blendRequired){r.renderTarget.finishRenderPass();const s=r.renderTarget.getRenderTarget(t.outputRenderSurface);t.backTexture=this.getBackTexture(s,e,n==null?void 0:n.bounds)}t.inputTexture=X.getOptimalTexture(e.width,e.height,t.resolution,t.antialias),r.renderTarget.bind(t.inputTexture,!0),r.globalUniforms.push({offset:e})}_calculateGlobalFrame(t,e,r,n,s,o){const a=t.globalFrame;a.x=e*n,a.y=r*n,a.width=s*n,a.height=o*n}_updateFilterUniforms(t,e,r,n,s,o,a,l){const c=this._filterGlobalUniforms.uniforms,h=c.uOutputFrame,u=c.uInputSize,f=c.uInputPixel,d=c.uInputClamp,p=c.uGlobalFrame,m=c.uOutputTexture;a?(h[0]=r.bounds.minX-n,h[1]=r.bounds.minY-s):(h[0]=0,h[1]=0),h[2]=t.frame.width,h[3]=t.frame.height,u[0]=t.source.width,u[1]=t.source.height,u[2]=1/u[0],u[3]=1/u[1],f[0]=t.source.pixelWidth,f[1]=t.source.pixelHeight,f[2]=1/f[0],f[3]=1/f[1],d[0]=.5*f[2],d[1]=.5*f[3],d[2]=t.frame.width*u[2]-.5*f[2],d[3]=t.frame.height*u[3]-.5*f[3];const g=this.renderer.renderTarget.rootRenderTarget.colorTexture;p[0]=n*o,p[1]=s*o,p[2]=g.source.width*o,p[3]=g.source.height*o,e instanceof E&&(e.source.resource=null);const x=this.renderer.renderTarget.getRenderTarget(e);this.renderer.renderTarget.bind(e,!!l),e instanceof E?(m[0]=e.frame.width,m[1]=e.frame.height):(m[0]=x.width,m[1]=x.height),m[2]=x.isRoot?-1:1,this._filterGlobalUniforms.update()}_findFilterResolution(t){let e=this._filterStackIndex-1;for(;e>0&&this._filterStack[e].skip;)--e;return e>0&&this._filterStack[e].inputTexture?this._filterStack[e].inputTexture.source._resolution:t}_findPreviousFilterOffset(){let t=0,e=0,r=this._filterStackIndex;for(;r>0;){r--;const n=this._filterStack[r];if(!n.skip){t=n.bounds.minX,e=n.bounds.minY;break}}return{x:t,y:e}}_calculateFilterArea(t,e){if(t.renderables?_s(t.renderables,e):t.filterEffect.filterArea?(e.clear(),e.addRect(t.filterEffect.filterArea),e.applyMatrix(t.container.worldTransform)):t.container.getFastGlobalBounds(!0,e),t.container){const n=(t.container.renderGroup||t.container.parentRenderGroup).cacheToLocalTransform;n&&e.applyMatrix(n)}}_applyFiltersToTexture(t,e){const r=t.inputTexture,n=t.bounds,s=t.filters;if(this._globalFilterBindGroup.setResource(r.source.style,2),this._globalFilterBindGroup.setResource(t.backTexture.source,3),s.length===1)s[0].apply(this,r,t.outputRenderSurface,e);else{let o=t.inputTexture;const a=X.getOptimalTexture(n.width,n.height,o.source._resolution,!1);let l=a,c=0;for(c=0;c<s.length-1;++c){s[c].apply(this,o,l,!0);const u=o;o=l,l=u}s[c].apply(this,o,t.outputRenderSurface,e),X.returnTexture(a)}}_calculateFilterBounds(t,e,r,n,s){var m;const o=this.renderer,a=t.bounds,l=t.filters;let c=1/0,h=0,u=!0,f=!1,d=!1,p=!0;for(let g=0;g<l.length;g++){const x=l[g];if(c=Math.min(c,x.resolution==="inherit"?n:x.resolution),h+=x.padding,x.antialias==="off"?u=!1:x.antialias==="inherit"&&u&&(u=r),x.clipToViewport||(p=!1),!!!(x.compatibleRenderers&o.type)){d=!1;break}if(x.blendRequired&&!(((m=o.backBuffer)==null?void 0:m.useBackBuffer)??!0)){Y("Blend filter requires backBuffer on WebGL renderer to be enabled. Set `useBackBuffer: true` in the renderer options."),d=!1;break}d=x.enabled||d,f||(f=x.blendRequired)}if(!d){t.skip=!0;return}if(p&&a.fitBounds(0,e.width/n,0,e.height/n),a.scale(c).ceil().scale(1/c).pad((h|0)*s),!a.isPositive){t.skip=!0;return}t.antialias=u,t.resolution=c,t.blendRequired=f}_popFilterData(){return this._filterStackIndex--,this._filterStack[this._filterStackIndex]}_getPreviousFilterData(){let t,e=this._filterStackIndex-1;for(;e>1&&(e--,t=this._filterStack[e],!!t.skip););return t}_pushFilterData(){let t=this._filterStack[this._filterStackIndex];return t||(t=this._filterStack[this._filterStackIndex]=new Ss),this._filterStackIndex++,t}}Xr.extension={type:[B.WebGLSystem,B.WebGPUSystem],name:"filter"};class Xt extends kn{constructor(t){t instanceof j&&(t={context:t});const{context:e,roundPixels:r,...n}=t||{};super({label:"Graphics",...n}),this.renderPipeId="graphics",e?this._context=e:this._context=this._ownedContext=new j,this._context.on("update",this.onViewUpdate,this),this.didViewUpdate=!0,this.allowChildren=!1,this.roundPixels=r??!1}set context(t){t!==this._context&&(this._context.off("update",this.onViewUpdate,this),this._context=t,this._context.on("update",this.onViewUpdate,this),this.onViewUpdate())}get context(){return this._context}get bounds(){return this._context.bounds}updateBounds(){}containsPoint(t){return this._context.containsPoint(t)}destroy(t){this._ownedContext&&!t?this._ownedContext.destroy(t):(t===!0||(t==null?void 0:t.context)===!0)&&this._context.destroy(t),this._ownedContext=null,this._context=null,super.destroy(t)}_callContextMethod(t,e){return this.context[t](...e),this}setFillStyle(...t){return this._callContextMethod("setFillStyle",t)}setStrokeStyle(...t){return this._callContextMethod("setStrokeStyle",t)}fill(...t){return this._callContextMethod("fill",t)}stroke(...t){return this._callContextMethod("stroke",t)}texture(...t){return this._callContextMethod("texture",t)}beginPath(){return this._callContextMethod("beginPath",[])}cut(){return this._callContextMethod("cut",[])}arc(...t){return this._callContextMethod("arc",t)}arcTo(...t){return this._callContextMethod("arcTo",t)}arcToSvg(...t){return this._callContextMethod("arcToSvg",t)}bezierCurveTo(...t){return this._callContextMethod("bezierCurveTo",t)}closePath(){return this._callContextMethod("closePath",[])}ellipse(...t){return this._callContextMethod("ellipse",t)}circle(...t){return this._callContextMethod("circle",t)}path(...t){return this._callContextMethod("path",t)}lineTo(...t){return this._callContextMethod("lineTo",t)}moveTo(...t){return this._callContextMethod("moveTo",t)}quadraticCurveTo(...t){return this._callContextMethod("quadraticCurveTo",t)}rect(...t){return this._callContextMethod("rect",t)}roundRect(...t){return this._callContextMethod("roundRect",t)}poly(...t){return this._callContextMethod("poly",t)}regularPoly(...t){return this._callContextMethod("regularPoly",t)}roundPoly(...t){return this._callContextMethod("roundPoly",t)}roundShape(...t){return this._callContextMethod("roundShape",t)}filletRect(...t){return this._callContextMethod("filletRect",t)}chamferRect(...t){return this._callContextMethod("chamferRect",t)}star(...t){return this._callContextMethod("star",t)}svg(...t){return this._callContextMethod("svg",t)}restore(...t){return this._callContextMethod("restore",t)}save(){return this._callContextMethod("save",[])}getTransform(){return this.context.getTransform()}resetTransform(){return this._callContextMethod("resetTransform",[])}rotateTransform(...t){return this._callContextMethod("rotate",t)}scaleTransform(...t){return this._callContextMethod("scale",t)}setTransform(...t){return this._callContextMethod("setTransform",t)}transform(...t){return this._callContextMethod("transform",t)}translateTransform(...t){return this._callContextMethod("translate",t)}clear(){return this._callContextMethod("clear",[])}get fillStyle(){return this._context.fillStyle}set fillStyle(t){this._context.fillStyle=t}get strokeStyle(){return this._context.strokeStyle}set strokeStyle(t){this._context.strokeStyle=t}clone(t=!1){return t?new Xt(this._context.clone()):(this._ownedContext=null,new Xt(this._context))}lineStyle(t,e,r){A(I,"Graphics#lineStyle is no longer needed. Use Graphics#setStrokeStyle to set the stroke style.");const n={};return t&&(n.width=t),e&&(n.color=e),r&&(n.alpha=r),this.context.strokeStyle=n,this}beginFill(t,e){A(I,"Graphics#beginFill is no longer needed. Use Graphics#fill to fill the shape with the desired style.");const r={};return t!==void 0&&(r.color=t),e!==void 0&&(r.alpha=e),this.context.fillStyle=r,this}endFill(){A(I,"Graphics#endFill is no longer needed. Use Graphics#fill to fill the shape with the desired style."),this.context.fill();const t=this.context.strokeStyle;return(t.width!==j.defaultStrokeStyle.width||t.color!==j.defaultStrokeStyle.color||t.alpha!==j.defaultStrokeStyle.alpha)&&this.context.stroke(),this}drawCircle(...t){return A(I,"Graphics#drawCircle has been renamed to Graphics#circle"),this._callContextMethod("circle",t)}drawEllipse(...t){return A(I,"Graphics#drawEllipse has been renamed to Graphics#ellipse"),this._callContextMethod("ellipse",t)}drawPolygon(...t){return A(I,"Graphics#drawPolygon has been renamed to Graphics#poly"),this._callContextMethod("poly",t)}drawRect(...t){return A(I,"Graphics#drawRect has been renamed to Graphics#rect"),this._callContextMethod("rect",t)}drawRoundedRect(...t){return A(I,"Graphics#drawRoundedRect has been renamed to Graphics#roundRect"),this._callContextMethod("roundRect",t)}drawStar(...t){return A(I,"Graphics#drawStar has been renamed to Graphics#star"),this._callContextMethod("star",t)}}const Kr=class Zr extends Se{constructor(...t){let e=t[0]??{};e instanceof Float32Array&&(A(I,"use new MeshGeometry({ positions, uvs, indices }) instead"),e={positions:e,uvs:t[1],indices:t[2]}),e={...Zr.defaultOptions,...e};const r=e.positions||new Float32Array([0,0,1,0,1,1,0,1]);let n=e.uvs;n||(e.positions?n=new Float32Array(r.length):n=new Float32Array([0,0,1,0,1,1,0,1]));const s=e.indices||new Uint32Array([0,1,2,0,2,3]),o=e.shrinkBuffersToFit,a=new Ct({data:r,label:"attribute-mesh-positions",shrinkToFit:o,usage:K.VERTEX|K.COPY_DST}),l=new Ct({data:n,label:"attribute-mesh-uvs",shrinkToFit:o,usage:K.VERTEX|K.COPY_DST}),c=new Ct({data:s,label:"index-mesh-buffer",shrinkToFit:o,usage:K.INDEX|K.COPY_DST});super({attributes:{aPosition:{buffer:a,format:"float32x2",stride:2*4,offset:0},aUV:{buffer:l,format:"float32x2",stride:2*4,offset:0}},indexBuffer:c,topology:e.topology}),this.batchMode="auto"}get positions(){return this.attributes.aPosition.buffer.data}set positions(t){this.attributes.aPosition.buffer.data=t}get uvs(){return this.attributes.aUV.buffer.data}set uvs(t){this.attributes.aUV.buffer.data=t}get indices(){return this.indexBuffer.data}set indices(t){this.indexBuffer.data=t}};Kr.defaultOptions={topology:"triangle-list",shrinkBuffersToFit:!1};let Ae=Kr,ct=null,J=null;function ws(i,t){ct||(ct=dt.get().createCanvas(256,128),J=ct.getContext("2d",{willReadFrequently:!0}),J.globalCompositeOperation="copy",J.globalAlpha=1),(ct.width<i||ct.height<t)&&(ct.width=Ve(i),ct.height=Ve(t))}function ur(i,t,e){for(let r=0,n=4*e*t;r<t;++r,n+=4)if(i[n+3]!==0)return!1;return!0}function dr(i,t,e,r,n){const s=4*t;for(let o=r,a=r*s+4*e;o<=n;++o,a+=s)if(i[a+3]!==0)return!1;return!0}function Ts(...i){let t=i[0];t.canvas||(t={canvas:i[0],resolution:i[1]});const{canvas:e}=t,r=Math.min(t.resolution??1,1),n=t.width??e.width,s=t.height??e.height;let o=t.output;if(ws(n,s),!J)throw new TypeError("Failed to get canvas 2D context");J.drawImage(e,0,0,n,s,0,0,n*r,s*r);const l=J.getImageData(0,0,n,s).data;let c=0,h=0,u=n-1,f=s-1;for(;h<s&&ur(l,n,h);)++h;if(h===s)return $.EMPTY;for(;ur(l,n,f);)--f;for(;dr(l,n,c,h,f);)++c;for(;dr(l,n,u,h,f);)--u;return++u,++f,J.globalCompositeOperation="source-over",J.strokeRect(c,h,u-c,f-h),J.globalCompositeOperation="copy",o??(o=new $),o.set(c/r,h/r,(u-c)/r,(f-h)/r),o}const fr=new $;class Ps{getCanvasAndContext(t){const{text:e,style:r,resolution:n=1}=t,s=r._getFinalPadding(),o=Z.measureText(e||" ",r),a=Math.ceil(Math.ceil(Math.max(1,o.width)+s*2)*n),l=Math.ceil(Math.ceil(Math.max(1,o.height)+s*2)*n),c=xt.getOptimalCanvasAndContext(a,l);this._renderTextToCanvas(e,r,s,n,c);const h=r.trim?Ts({canvas:c.canvas,width:a,height:l,resolution:1,output:fr}):fr.set(0,0,a,l);return{canvasAndContext:c,frame:h}}returnCanvasAndContext(t){xt.returnCanvasAndContext(t)}_renderTextToCanvas(t,e,r,n,s){var b,_,v,w;const{canvas:o,context:a}=s,l=Yt(e),c=Z.measureText(t||" ",e),h=c.lines,u=c.lineHeight,f=c.lineWidths,d=c.maxLineWidth,p=c.fontProperties,m=o.height;if(a.resetTransform(),a.scale(n,n),a.textBaseline=e.textBaseline,(b=e._stroke)!=null&&b.width){const S=e._stroke;a.lineWidth=S.width,a.miterLimit=S.miterLimit,a.lineJoin=S.join,a.lineCap=S.cap}a.font=l;let g,x;const y=e.dropShadow?2:1;for(let S=0;S<y;++S){const R=e.dropShadow&&S===0,k=R?Math.ceil(Math.max(1,m)+r*2):0,P=k*n;if(R){a.fillStyle="black",a.strokeStyle="black";const G=e.dropShadow,O=G.color,it=G.alpha;a.shadowColor=L.shared.setValue(O).setAlpha(it).toRgbaString();const M=G.blur*n,F=G.distance*n;a.shadowBlur=M,a.shadowOffsetX=Math.cos(G.angle)*F,a.shadowOffsetY=Math.sin(G.angle)*F+P}else{if(a.fillStyle=e._fill?qt(e._fill,a,c):null,(_=e._stroke)!=null&&_.width){const G=e._stroke.width*e._stroke.alignment;a.strokeStyle=qt(e._stroke,a,c,G)}a.shadowColor="black"}let T=(u-p.fontSize)/2;u-p.fontSize<0&&(T=0);const U=((v=e._stroke)==null?void 0:v.width)??0;for(let G=0;G<h.length;G++)g=U/2,x=U/2+G*u+p.ascent+T,e.align==="right"?g+=d-f[G]:e.align==="center"&&(g+=(d-f[G])/2),(w=e._stroke)!=null&&w.width&&this._drawLetterSpacing(h[G],e,s,g+r,x+r-k,!0),e._fill!==void 0&&this._drawLetterSpacing(h[G],e,s,g+r,x+r-k)}}_drawLetterSpacing(t,e,r,n,s,o=!1){const{context:a}=r,l=e.letterSpacing;let c=!1;if(Z.experimentalLetterSpacingSupported&&(Z.experimentalLetterSpacing?(a.letterSpacing=`${l}px`,a.textLetterSpacing=`${l}px`,c=!0):(a.letterSpacing="0px",a.textLetterSpacing="0px")),l===0||c){o?a.strokeText(t,n,s):a.fillText(t,n,s);return}let h=n;const u=Z.graphemeSegmenter(t);let f=a.measureText(t).width,d=0;for(let p=0;p<u.length;++p){const m=u[p];o?a.strokeText(m,h,s):a.fillText(m,h,s);let g="";for(let x=p+1;x<u.length;++x)g+=u[x];d=a.measureText(g).width,h+=f-d+l,f=d}}}const oe=new Ps;function vs(i){const t=i._stroke,e=i._fill,n=[`div { ${[`color: ${L.shared.setValue(e.color).toHex()}`,`font-size: ${i.fontSize}px`,`font-family: ${i.fontFamily}`,`font-weight: ${i.fontWeight}`,`font-style: ${i.fontStyle}`,`font-variant: ${i.fontVariant}`,`letter-spacing: ${i.letterSpacing}px`,`text-align: ${i.align}`,`padding: ${i.padding}px`,`white-space: ${i.whiteSpace==="pre"&&i.wordWrap?"pre-wrap":i.whiteSpace}`,...i.lineHeight?[`line-height: ${i.lineHeight}px`]:[],...i.wordWrap?[`word-wrap: ${i.breakWords?"break-all":"break-word"}`,`max-width: ${i.wordWrapWidth}px`]:[],...t?[Jr(t)]:[],...i.dropShadow?[Qr(i.dropShadow)]:[],...i.cssOverrides].join(";")} }`];return Cs(i.tagStyles,n),n.join(" ")}function Qr(i){const t=L.shared.setValue(i.color).setAlpha(i.alpha).toHexa(),e=Math.round(Math.cos(i.angle)*i.distance),r=Math.round(Math.sin(i.angle)*i.distance),n=`${e}px ${r}px`;return i.blur>0?`text-shadow: ${n} ${i.blur}px ${t}`:`text-shadow: ${n} ${t}`}function Jr(i){return[`-webkit-text-stroke-width: ${i.width}px`,`-webkit-text-stroke-color: ${L.shared.setValue(i.color).toHex()}`,`text-stroke-width: ${i.width}px`,`text-stroke-color: ${L.shared.setValue(i.color).toHex()}`,"paint-order: stroke"].join(";")}const pr={fontSize:"font-size: {{VALUE}}px",fontFamily:"font-family: {{VALUE}}",fontWeight:"font-weight: {{VALUE}}",fontStyle:"font-style: {{VALUE}}",fontVariant:"font-variant: {{VALUE}}",letterSpacing:"letter-spacing: {{VALUE}}px",align:"text-align: {{VALUE}}",padding:"padding: {{VALUE}}px",whiteSpace:"white-space: {{VALUE}}",lineHeight:"line-height: {{VALUE}}px",wordWrapWidth:"max-width: {{VALUE}}px"},gr={fill:i=>`color: ${L.shared.setValue(i).toHex()}`,breakWords:i=>`word-wrap: ${i?"break-all":"break-word"}`,stroke:Jr,dropShadow:Qr};function Cs(i,t){for(const e in i){const r=i[e],n=[];for(const s in r)gr[s]?n.push(gr[s](r[s])):pr[s]&&n.push(pr[s].replace("{{VALUE}}",r[s]));t.push(`${e} { ${n.join(";")} }`)}}class ze extends nt{constructor(t={}){super(t),this._cssOverrides=[],this.cssOverrides=t.cssOverrides??[],this.tagStyles=t.tagStyles??{}}set cssOverrides(t){this._cssOverrides=t instanceof Array?t:[t],this.update()}get cssOverrides(){return this._cssOverrides}update(){this._cssStyle=null,super.update()}clone(){return new ze({align:this.align,breakWords:this.breakWords,dropShadow:this.dropShadow?{...this.dropShadow}:null,fill:this._fill,fontFamily:this.fontFamily,fontSize:this.fontSize,fontStyle:this.fontStyle,fontVariant:this.fontVariant,fontWeight:this.fontWeight,letterSpacing:this.letterSpacing,lineHeight:this.lineHeight,padding:this.padding,stroke:this._stroke,whiteSpace:this.whiteSpace,wordWrap:this.wordWrap,wordWrapWidth:this.wordWrapWidth,cssOverrides:this.cssOverrides,tagStyles:{...this.tagStyles}})}get cssStyle(){return this._cssStyle||(this._cssStyle=vs(this)),this._cssStyle}addOverride(...t){const e=t.filter(r=>!this.cssOverrides.includes(r));e.length>0&&(this.cssOverrides.push(...e),this.update())}removeOverride(...t){const e=t.filter(r=>this.cssOverrides.includes(r));e.length>0&&(this.cssOverrides=this.cssOverrides.filter(r=>!e.includes(r)),this.update())}set fill(t){typeof t!="string"&&typeof t!="number"&&Y("[HTMLTextStyle] only color fill is not supported by HTMLText"),super.fill=t}set stroke(t){t&&typeof t!="string"&&typeof t!="number"&&Y("[HTMLTextStyle] only color stroke is not supported by HTMLText"),super.stroke=t}}const xr="http://www.w3.org/2000/svg",mr="http://www.w3.org/1999/xhtml";class tn{constructor(){this.svgRoot=document.createElementNS(xr,"svg"),this.foreignObject=document.createElementNS(xr,"foreignObject"),this.domElement=document.createElementNS(mr,"div"),this.styleElement=document.createElementNS(mr,"style"),this.image=new Image;const{foreignObject:t,svgRoot:e,styleElement:r,domElement:n}=this;t.setAttribute("width","10000"),t.setAttribute("height","10000"),t.style.overflow="hidden",e.appendChild(t),t.appendChild(r),t.appendChild(n)}}let yr;function Ms(i,t,e,r){r||(r=yr||(yr=new tn));const{domElement:n,styleElement:s,svgRoot:o}=r;n.innerHTML=`<style>${t.cssStyle};</style><div style='padding:0'>${i}</div>`,n.setAttribute("style","transform-origin: top left; display: inline-block"),e&&(s.textContent=e),document.body.appendChild(o);const a=n.getBoundingClientRect();o.remove();const l=t.padding*2;return{width:a.width-l,height:a.height-l}}class ks{constructor(){this.batches=[],this.batched=!1}destroy(){this.batches.forEach(t=>{tt.return(t)}),this.batches.length=0}}class en{constructor(t,e){this.state=jt.for2d(),this.renderer=t,this._adaptor=e,this.renderer.runners.contextChange.add(this)}contextChange(){this._adaptor.contextChange(this.renderer)}validateRenderable(t){const e=t.context,r=!!t._gpuData,n=this.renderer.graphicsContext.updateGpuContext(e);return!!(n.isBatchable||r!==n.isBatchable)}addRenderable(t,e){const r=this.renderer.graphicsContext.updateGpuContext(t.context);t.didViewUpdate&&this._rebuild(t),r.isBatchable?this._addToBatcher(t,e):(this.renderer.renderPipes.batch.break(e),e.add(t))}updateRenderable(t){const r=this._getGpuDataForRenderable(t).batches;for(let n=0;n<r.length;n++){const s=r[n];s._batcher.updateElement(s)}}execute(t){if(!t.isRenderable)return;const e=this.renderer,r=t.context;if(!e.graphicsContext.getGpuContext(r).batches.length)return;const s=r.customShader||this._adaptor.shader;this.state.blendMode=t.groupBlendMode;const o=s.resources.localUniforms.uniforms;o.uTransformMatrix=t.groupTransform,o.uRound=e._roundPixels|t._roundPixels,Qt(t.groupColorAlpha,o.uColor,0),this._adaptor.execute(this,t)}_rebuild(t){const e=this._getGpuDataForRenderable(t),r=this.renderer.graphicsContext.updateGpuContext(t.context);e.destroy(),r.isBatchable&&this._updateBatchesForRenderable(t,e)}_addToBatcher(t,e){const r=this.renderer.renderPipes.batch,n=this._getGpuDataForRenderable(t).batches;for(let s=0;s<n.length;s++){const o=n[s];r.addToBatch(o,e)}}_getGpuDataForRenderable(t){return t._gpuData[this.renderer.uid]||this._initGpuDataForRenderable(t)}_initGpuDataForRenderable(t){const e=new ks;return t._gpuData[this.renderer.uid]=e,e}_updateBatchesForRenderable(t,e){const r=t.context,n=this.renderer.graphicsContext.getGpuContext(r),s=this.renderer._roundPixels|t._roundPixels;e.batches=n.batches.map(o=>{const a=tt.get(Fe);return o.copyTo(a),a.renderable=t,a.roundPixels=s,a})}destroy(){this.renderer=null,this._adaptor.destroy(),this._adaptor=null,this.state=null}}en.extension={type:[B.WebGLPipes,B.WebGPUPipes,B.CanvasPipes],name:"graphics"};const rn=class nn extends Ae{constructor(...t){super({});let e=t[0]??{};typeof e=="number"&&(A(I,"PlaneGeometry constructor changed please use { width, height, verticesX, verticesY } instead"),e={width:e,height:t[1],verticesX:t[2],verticesY:t[3]}),this.build(e)}build(t){t={...nn.defaultOptions,...t},this.verticesX=this.verticesX??t.verticesX,this.verticesY=this.verticesY??t.verticesY,this.width=this.width??t.width,this.height=this.height??t.height;const e=this.verticesX*this.verticesY,r=[],n=[],s=[],o=this.verticesX-1,a=this.verticesY-1,l=this.width/o,c=this.height/a;for(let u=0;u<e;u++){const f=u%this.verticesX,d=u/this.verticesX|0;r.push(f*l,d*c),n.push(f/o,d/a)}const h=o*a;for(let u=0;u<h;u++){const f=u%o,d=u/o|0,p=d*this.verticesX+f,m=d*this.verticesX+f+1,g=(d+1)*this.verticesX+f,x=(d+1)*this.verticesX+f+1;s.push(p,m,g,m,x,g)}this.buffers[0].data=new Float32Array(r),this.buffers[1].data=new Float32Array(n),this.indexBuffer.data=new Uint32Array(s),this.buffers[0].update(),this.buffers[1].update(),this.indexBuffer.update()}};rn.defaultOptions={width:100,height:100,verticesX:10,verticesY:10};let Fs=rn;class We{constructor(){this.batcherName="default",this.packAsQuad=!1,this.indexOffset=0,this.attributeOffset=0,this.roundPixels=0,this._batcher=null,this._batch=null,this._textureMatrixUpdateId=-1,this._uvUpdateId=-1}get blendMode(){return this.renderable.groupBlendMode}get topology(){return this._topology||this.geometry.topology}set topology(t){this._topology=t}reset(){this.renderable=null,this.texture=null,this._batcher=null,this._batch=null,this.geometry=null,this._uvUpdateId=-1,this._textureMatrixUpdateId=-1}setTexture(t){this.texture!==t&&(this.texture=t,this._textureMatrixUpdateId=-1)}get uvs(){const e=this.geometry.getBuffer("aUV"),r=e.data;let n=r;const s=this.texture.textureMatrix;return s.isSimple||(n=this._transformedUvs,(this._textureMatrixUpdateId!==s._updateID||this._uvUpdateId!==e._updateID)&&((!n||n.length<r.length)&&(n=this._transformedUvs=new Float32Array(r.length)),this._textureMatrixUpdateId=s._updateID,this._uvUpdateId=e._updateID,s.multiplyUvs(r,n))),n}get positions(){return this.geometry.positions}get indices(){return this.geometry.indices}get color(){return this.renderable.groupColorAlpha}get groupTransform(){return this.renderable.groupTransform}get attributeSize(){return this.geometry.positions.length/2}get indexSize(){return this.geometry.indices.length}}class _r{destroy(){}}class sn{constructor(t,e){this.localUniforms=new mt({uTransformMatrix:{value:new H,type:"mat3x3<f32>"},uColor:{value:new Float32Array([1,1,1,1]),type:"vec4<f32>"},uRound:{value:0,type:"f32"}}),this.localUniformsBindGroup=new vr({0:this.localUniforms}),this.renderer=t,this._adaptor=e,this._adaptor.init()}validateRenderable(t){const e=this._getMeshData(t),r=e.batched,n=t.batched;if(e.batched=n,r!==n)return!0;if(n){const s=t._geometry;if(s.indices.length!==e.indexSize||s.positions.length!==e.vertexSize)return e.indexSize=s.indices.length,e.vertexSize=s.positions.length,!0;const o=this._getBatchableMesh(t);return o.texture.uid!==t._texture.uid&&(o._textureMatrixUpdateId=-1),!o._batcher.checkAndUpdateTexture(o,t._texture)}return!1}addRenderable(t,e){const r=this.renderer.renderPipes.batch,{batched:n}=this._getMeshData(t);if(n){const s=this._getBatchableMesh(t);s.setTexture(t._texture),s.geometry=t._geometry,r.addToBatch(s,e)}else r.break(e),e.add(t)}updateRenderable(t){if(t.batched){const e=this._getBatchableMesh(t);e.setTexture(t._texture),e.geometry=t._geometry,e._batcher.updateElement(e)}}execute(t){if(!t.isRenderable)return;t.state.blendMode=Te(t.groupBlendMode,t.texture._source);const e=this.localUniforms;e.uniforms.uTransformMatrix=t.groupTransform,e.uniforms.uRound=this.renderer._roundPixels|t._roundPixels,e.update(),Qt(t.groupColorAlpha,e.uniforms.uColor,0),this._adaptor.execute(this,t)}_getMeshData(t){var e,r;return(e=t._gpuData)[r=this.renderer.uid]||(e[r]=new _r),t._gpuData[this.renderer.uid].meshData||this._initMeshData(t)}_initMeshData(t){var e,r;return t._gpuData[this.renderer.uid].meshData={batched:t.batched,indexSize:(e=t._geometry.indices)==null?void 0:e.length,vertexSize:(r=t._geometry.positions)==null?void 0:r.length},t._gpuData[this.renderer.uid].meshData}_getBatchableMesh(t){var e,r;return(e=t._gpuData)[r=this.renderer.uid]||(e[r]=new _r),t._gpuData[this.renderer.uid].batchableMesh||this._initBatchableMesh(t)}_initBatchableMesh(t){const e=new We;return e.renderable=t,e.setTexture(t._texture),e.transform=t.groupTransform,e.roundPixels=this.renderer._roundPixels|t._roundPixels,t._gpuData[this.renderer.uid].batchableMesh=e,e}destroy(){this.localUniforms=null,this.localUniformsBindGroup=null,this._adaptor.destroy(),this._adaptor=null,this.renderer=null}}sn.extension={type:[B.WebGLPipes,B.WebGPUPipes,B.CanvasPipes],name:"mesh"};class Bs{execute(t,e){const r=t.state,n=t.renderer,s=e.shader||t.defaultShader;s.resources.uTexture=e.texture._source,s.resources.uniforms=t.localUniforms;const o=n.gl,a=t.getBuffers(e);n.shader.bind(s),n.state.set(r),n.geometry.bind(a.geometry,s.glProgram);const c=a.geometry.indexBuffer.data.BYTES_PER_ELEMENT===2?o.UNSIGNED_SHORT:o.UNSIGNED_INT;o.drawElements(o.TRIANGLES,e.particleChildren.length*6,c,0)}}class Rs{execute(t,e){const r=t.renderer,n=e.shader||t.defaultShader;n.groups[0]=r.renderPipes.uniformBatch.getUniformBindGroup(t.localUniforms,!0),n.groups[1]=r.texture.getTextureBindGroup(e.texture);const s=t.state,o=t.getBuffers(e);r.encoder.draw({geometry:o.geometry,shader:e.shader||t.defaultShader,state:s,size:e.particleChildren.length*6})}}function br(i,t=null){const e=i*6;if(e>65535?t||(t=new Uint32Array(e)):t||(t=new Uint16Array(e)),t.length!==e)throw new Error(`Out buffer length is incorrect, got ${t.length} and expected ${e}`);for(let r=0,n=0;r<e;r+=6,n+=4)t[r+0]=n+0,t[r+1]=n+1,t[r+2]=n+2,t[r+3]=n+0,t[r+4]=n+2,t[r+5]=n+3;return t}function Gs(i){return{dynamicUpdate:Sr(i,!0),staticUpdate:Sr(i,!1)}}function Sr(i,t){const e=[];e.push(`

        var index = 0;

        for (let i = 0; i < ps.length; ++i)
        {
            const p = ps[i];

            `);let r=0;for(const s in i){const o=i[s];if(t!==o.dynamic)continue;e.push(`offset = index + ${r}`),e.push(o.code);const a=de(o.format);r+=a.stride/4}e.push(`
            index += stride * 4;
        }
    `),e.unshift(`
        var stride = ${r};
    `);const n=e.join(`
`);return new Function("ps","f32v","u32v",n)}class Us{constructor(t){this._size=0,this._generateParticleUpdateCache={};const e=this._size=t.size??1e3,r=t.properties;let n=0,s=0;for(const h in r){const u=r[h],f=de(u.format);u.dynamic?s+=f.stride:n+=f.stride}this._dynamicStride=s/4,this._staticStride=n/4,this.staticAttributeBuffer=new Lt(e*4*n),this.dynamicAttributeBuffer=new Lt(e*4*s),this.indexBuffer=br(e);const o=new Se;let a=0,l=0;this._staticBuffer=new Ct({data:new Float32Array(1),label:"static-particle-buffer",shrinkToFit:!1,usage:K.VERTEX|K.COPY_DST}),this._dynamicBuffer=new Ct({data:new Float32Array(1),label:"dynamic-particle-buffer",shrinkToFit:!1,usage:K.VERTEX|K.COPY_DST});for(const h in r){const u=r[h],f=de(u.format);u.dynamic?(o.addAttribute(u.attributeName,{buffer:this._dynamicBuffer,stride:this._dynamicStride*4,offset:a*4,format:u.format}),a+=f.size):(o.addAttribute(u.attributeName,{buffer:this._staticBuffer,stride:this._staticStride*4,offset:l*4,format:u.format}),l+=f.size)}o.addIndex(this.indexBuffer);const c=this.getParticleUpdate(r);this._dynamicUpload=c.dynamicUpdate,this._staticUpload=c.staticUpdate,this.geometry=o}getParticleUpdate(t){const e=As(t);return this._generateParticleUpdateCache[e]?this._generateParticleUpdateCache[e]:(this._generateParticleUpdateCache[e]=this.generateParticleUpdate(t),this._generateParticleUpdateCache[e])}generateParticleUpdate(t){return Gs(t)}update(t,e){t.length>this._size&&(e=!0,this._size=Math.max(t.length,this._size*1.5|0),this.staticAttributeBuffer=new Lt(this._size*this._staticStride*4*4),this.dynamicAttributeBuffer=new Lt(this._size*this._dynamicStride*4*4),this.indexBuffer=br(this._size),this.geometry.indexBuffer.setDataWithSize(this.indexBuffer,this.indexBuffer.byteLength,!0));const r=this.dynamicAttributeBuffer;if(this._dynamicUpload(t,r.float32View,r.uint32View),this._dynamicBuffer.setDataWithSize(this.dynamicAttributeBuffer.float32View,t.length*this._dynamicStride*4,!0),e){const n=this.staticAttributeBuffer;this._staticUpload(t,n.float32View,n.uint32View),this._staticBuffer.setDataWithSize(n.float32View,t.length*this._staticStride*4,!0)}}destroy(){this._staticBuffer.destroy(),this._dynamicBuffer.destroy(),this.geometry.destroy()}}function As(i){const t=[];for(const e in i){const r=i[e];t.push(e,r.code,r.dynamic?"d":"s")}return t.join("_")}var zs=`varying vec2 vUV;
varying vec4 vColor;

uniform sampler2D uTexture;

void main(void){
    vec4 color = texture2D(uTexture, vUV) * vColor;
    gl_FragColor = color;
}`,Ws=`attribute vec2 aVertex;
attribute vec2 aUV;
attribute vec4 aColor;

attribute vec2 aPosition;
attribute float aRotation;

uniform mat3 uTranslationMatrix;
uniform float uRound;
uniform vec2 uResolution;
uniform vec4 uColor;

varying vec2 vUV;
varying vec4 vColor;

vec2 roundPixels(vec2 position, vec2 targetSize)
{       
    return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
}

void main(void){
    float cosRotation = cos(aRotation);
    float sinRotation = sin(aRotation);
    float x = aVertex.x * cosRotation - aVertex.y * sinRotation;
    float y = aVertex.x * sinRotation + aVertex.y * cosRotation;

    vec2 v = vec2(x, y);
    v = v + aPosition;

    gl_Position = vec4((uTranslationMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);

    if(uRound == 1.0)
    {
        gl_Position.xy = roundPixels(gl_Position.xy, uResolution);
    }

    vUV = aUV;
    vColor = vec4(aColor.rgb * aColor.a, aColor.a) * uColor;
}
`,wr=`
struct ParticleUniforms {
  uProjectionMatrix:mat3x3<f32>,
  uColor:vec4<f32>,
  uResolution:vec2<f32>,
  uRoundPixels:f32,
};

@group(0) @binding(0) var<uniform> uniforms: ParticleUniforms;

@group(1) @binding(0) var uTexture: texture_2d<f32>;
@group(1) @binding(1) var uSampler : sampler;

struct VSOutput {
    @builtin(position) position: vec4<f32>,
    @location(0) uv : vec2<f32>,
    @location(1) color : vec4<f32>,
  };
@vertex
fn mainVertex(
  @location(0) aVertex: vec2<f32>,
  @location(1) aPosition: vec2<f32>,
  @location(2) aUV: vec2<f32>,
  @location(3) aColor: vec4<f32>,
  @location(4) aRotation: f32,
) -> VSOutput {
  
   let v = vec2(
       aVertex.x * cos(aRotation) - aVertex.y * sin(aRotation),
       aVertex.x * sin(aRotation) + aVertex.y * cos(aRotation)
   ) + aPosition;

   let position = vec4((uniforms.uProjectionMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);

    let vColor = vec4(aColor.rgb * aColor.a, aColor.a) * uniforms.uColor;

  return VSOutput(
   position,
   aUV,
   vColor,
  );
}

@fragment
fn mainFragment(
  @location(0) uv: vec2<f32>,
  @location(1) color: vec4<f32>,
  @builtin(position) position: vec4<f32>,
) -> @location(0) vec4<f32> {

    var sample = textureSample(uTexture, uSampler, uv) * color;
   
    return sample;
}`;class Ls extends Pe{constructor(){const t=Gn.from({vertex:Ws,fragment:zs}),e=Un.from({fragment:{source:wr,entryPoint:"mainFragment"},vertex:{source:wr,entryPoint:"mainVertex"}});super({glProgram:t,gpuProgram:e,resources:{uTexture:E.WHITE.source,uSampler:new Ft({}),uniforms:{uTranslationMatrix:{value:new H,type:"mat3x3<f32>"},uColor:{value:new L(16777215),type:"vec4<f32>"},uRound:{value:1,type:"f32"},uResolution:{value:[0,0],type:"vec2<f32>"}}}})}}class on{constructor(t,e){this.state=jt.for2d(),this.localUniforms=new mt({uTranslationMatrix:{value:new H,type:"mat3x3<f32>"},uColor:{value:new Float32Array(4),type:"vec4<f32>"},uRound:{value:1,type:"f32"},uResolution:{value:[0,0],type:"vec2<f32>"}}),this.renderer=t,this.adaptor=e,this.defaultShader=new Ls,this.state=jt.for2d()}validateRenderable(t){return!1}addRenderable(t,e){this.renderer.renderPipes.batch.break(e),e.add(t)}getBuffers(t){return t._gpuData[this.renderer.uid]||this._initBuffer(t)}_initBuffer(t){return t._gpuData[this.renderer.uid]=new Us({size:t.particleChildren.length,properties:t._properties}),t._gpuData[this.renderer.uid]}updateRenderable(t){}execute(t){const e=t.particleChildren;if(e.length===0)return;const r=this.renderer,n=this.getBuffers(t);t.texture||(t.texture=e[0].texture);const s=this.state;n.update(e,t._childrenDirty),t._childrenDirty=!1,s.blendMode=Te(t.blendMode,t.texture._source);const o=this.localUniforms.uniforms,a=o.uTranslationMatrix;t.worldTransform.copyTo(a),a.prepend(r.globalUniforms.globalUniformData.projectionMatrix),o.uResolution=r.globalUniforms.globalUniformData.resolution,o.uRound=r._roundPixels|t._roundPixels,Qt(t.groupColorAlpha,o.uColor,0),this.adaptor.execute(this,t)}destroy(){this.defaultShader&&(this.defaultShader.destroy(),this.defaultShader=null)}}class an extends on{constructor(t){super(t,new Bs)}}an.extension={type:[B.WebGLPipes],name:"particle"};class ln extends on{constructor(t){super(t,new Rs)}}ln.extension={type:[B.WebGPUPipes],name:"particle"};const hn=class cn extends Fs{constructor(t={}){t={...cn.defaultOptions,...t},super({width:t.width,height:t.height,verticesX:4,verticesY:4}),this.update(t)}update(t){var e,r;this.width=t.width??this.width,this.height=t.height??this.height,this._originalWidth=t.originalWidth??this._originalWidth,this._originalHeight=t.originalHeight??this._originalHeight,this._leftWidth=t.leftWidth??this._leftWidth,this._rightWidth=t.rightWidth??this._rightWidth,this._topHeight=t.topHeight??this._topHeight,this._bottomHeight=t.bottomHeight??this._bottomHeight,this._anchorX=(e=t.anchor)==null?void 0:e.x,this._anchorY=(r=t.anchor)==null?void 0:r.y,this.updateUvs(),this.updatePositions()}updatePositions(){const t=this.positions,{width:e,height:r,_leftWidth:n,_rightWidth:s,_topHeight:o,_bottomHeight:a,_anchorX:l,_anchorY:c}=this,h=n+s,u=e>h?1:e/h,f=o+a,d=r>f?1:r/f,p=Math.min(u,d),m=l*e,g=c*r;t[0]=t[8]=t[16]=t[24]=-m,t[2]=t[10]=t[18]=t[26]=n*p-m,t[4]=t[12]=t[20]=t[28]=e-s*p-m,t[6]=t[14]=t[22]=t[30]=e-m,t[1]=t[3]=t[5]=t[7]=-g,t[9]=t[11]=t[13]=t[15]=o*p-g,t[17]=t[19]=t[21]=t[23]=r-a*p-g,t[25]=t[27]=t[29]=t[31]=r-g,this.getBuffer("aPosition").update()}updateUvs(){const t=this.uvs;t[0]=t[8]=t[16]=t[24]=0,t[1]=t[3]=t[5]=t[7]=0,t[6]=t[14]=t[22]=t[30]=1,t[25]=t[27]=t[29]=t[31]=1;const e=1/this._originalWidth,r=1/this._originalHeight;t[2]=t[10]=t[18]=t[26]=e*this._leftWidth,t[9]=t[11]=t[13]=t[15]=r*this._topHeight,t[4]=t[12]=t[20]=t[28]=1-e*this._rightWidth,t[17]=t[19]=t[21]=t[23]=1-r*this._bottomHeight,this.getBuffer("aUV").update()}};hn.defaultOptions={width:100,height:100,leftWidth:10,topHeight:10,rightWidth:10,bottomHeight:10,originalWidth:100,originalHeight:100};let Ds=hn;class Is extends We{constructor(){super(),this.geometry=new Ds}destroy(){this.geometry.destroy()}}class un{constructor(t){this._renderer=t}addRenderable(t,e){const r=this._getGpuSprite(t);t.didViewUpdate&&this._updateBatchableSprite(t,r),this._renderer.renderPipes.batch.addToBatch(r,e)}updateRenderable(t){const e=this._getGpuSprite(t);t.didViewUpdate&&this._updateBatchableSprite(t,e),e._batcher.updateElement(e)}validateRenderable(t){const e=this._getGpuSprite(t);return!e._batcher.checkAndUpdateTexture(e,t._texture)}_updateBatchableSprite(t,e){e.geometry.update(t),e.setTexture(t._texture)}_getGpuSprite(t){return t._gpuData[this._renderer.uid]||this._initGPUSprite(t)}_initGPUSprite(t){const e=t._gpuData[this._renderer.uid]=new Is,r=e;return r.renderable=t,r.transform=t.groupTransform,r.texture=t._texture,r.roundPixels=this._renderer._roundPixels|t._roundPixels,t.didViewUpdate||this._updateBatchableSprite(t,r),e}destroy(){this._renderer=null}}un.extension={type:[B.WebGLPipes,B.WebGPUPipes,B.CanvasPipes],name:"nineSliceSprite"};const Hs={name:"tiling-bit",vertex:{header:`
            struct TilingUniforms {
                uMapCoord:mat3x3<f32>,
                uClampFrame:vec4<f32>,
                uClampOffset:vec2<f32>,
                uTextureTransform:mat3x3<f32>,
                uSizeAnchor:vec4<f32>
            };

            @group(2) @binding(0) var<uniform> tilingUniforms: TilingUniforms;
            @group(2) @binding(1) var uTexture: texture_2d<f32>;
            @group(2) @binding(2) var uSampler: sampler;
        `,main:`
            uv = (tilingUniforms.uTextureTransform * vec3(uv, 1.0)).xy;

            position = (position - tilingUniforms.uSizeAnchor.zw) * tilingUniforms.uSizeAnchor.xy;
        `},fragment:{header:`
            struct TilingUniforms {
                uMapCoord:mat3x3<f32>,
                uClampFrame:vec4<f32>,
                uClampOffset:vec2<f32>,
                uTextureTransform:mat3x3<f32>,
                uSizeAnchor:vec4<f32>
            };

            @group(2) @binding(0) var<uniform> tilingUniforms: TilingUniforms;
            @group(2) @binding(1) var uTexture: texture_2d<f32>;
            @group(2) @binding(2) var uSampler: sampler;
        `,main:`

            var coord = vUV + ceil(tilingUniforms.uClampOffset - vUV);
            coord = (tilingUniforms.uMapCoord * vec3(coord, 1.0)).xy;
            var unclamped = coord;
            coord = clamp(coord, tilingUniforms.uClampFrame.xy, tilingUniforms.uClampFrame.zw);

            var bias = 0.;

            if(unclamped.x == coord.x && unclamped.y == coord.y)
            {
                bias = -32.;
            }

            outColor = textureSampleBias(uTexture, uSampler, coord, bias);
        `}},Es={name:"tiling-bit",vertex:{header:`
            uniform mat3 uTextureTransform;
            uniform vec4 uSizeAnchor;

        `,main:`
            uv = (uTextureTransform * vec3(aUV, 1.0)).xy;

            position = (position - uSizeAnchor.zw) * uSizeAnchor.xy;
        `},fragment:{header:`
            uniform sampler2D uTexture;
            uniform mat3 uMapCoord;
            uniform vec4 uClampFrame;
            uniform vec2 uClampOffset;
        `,main:`

        vec2 coord = vUV + ceil(uClampOffset - vUV);
        coord = (uMapCoord * vec3(coord, 1.0)).xy;
        vec2 unclamped = coord;
        coord = clamp(coord, uClampFrame.xy, uClampFrame.zw);

        outColor = texture(uTexture, coord, unclamped == coord ? 0.0 : -32.0);// lod-bias very negative to force lod 0

        `}};let ae,le;class Vs extends Pe{constructor(){ae??(ae=Cr({name:"tiling-sprite-shader",bits:[An,Hs,Mr]})),le??(le=kr({name:"tiling-sprite-shader",bits:[zn,Es,Fr]}));const t=new mt({uMapCoord:{value:new H,type:"mat3x3<f32>"},uClampFrame:{value:new Float32Array([0,0,1,1]),type:"vec4<f32>"},uClampOffset:{value:new Float32Array([0,0]),type:"vec2<f32>"},uTextureTransform:{value:new H,type:"mat3x3<f32>"},uSizeAnchor:{value:new Float32Array([100,100,.5,.5]),type:"vec4<f32>"}});super({glProgram:le,gpuProgram:ae,resources:{localUniforms:new mt({uTransformMatrix:{value:new H,type:"mat3x3<f32>"},uColor:{value:new Float32Array([1,1,1,1]),type:"vec4<f32>"},uRound:{value:0,type:"f32"}}),tilingUniforms:t,uTexture:E.EMPTY.source,uSampler:E.EMPTY.source.style}})}updateUniforms(t,e,r,n,s,o){const a=this.resources.tilingUniforms,l=o.width,c=o.height,h=o.textureMatrix,u=a.uniforms.uTextureTransform;u.set(r.a*l/t,r.b*l/e,r.c*c/t,r.d*c/e,r.tx/t,r.ty/e),u.invert(),a.uniforms.uMapCoord=h.mapCoord,a.uniforms.uClampFrame=h.uClampFrame,a.uniforms.uClampOffset=h.uClampOffset,a.uniforms.uTextureTransform=u,a.uniforms.uSizeAnchor[0]=t,a.uniforms.uSizeAnchor[1]=e,a.uniforms.uSizeAnchor[2]=n,a.uniforms.uSizeAnchor[3]=s,o&&(this.resources.uTexture=o.source,this.resources.uSampler=o.source.style)}}class Os extends Ae{constructor(){super({positions:new Float32Array([0,0,1,0,1,1,0,1]),uvs:new Float32Array([0,0,1,0,1,1,0,1]),indices:new Uint32Array([0,1,2,0,2,3])})}}function $s(i,t){const e=i.anchor.x,r=i.anchor.y;t[0]=-e*i.width,t[1]=-r*i.height,t[2]=(1-e)*i.width,t[3]=-r*i.height,t[4]=(1-e)*i.width,t[5]=(1-r)*i.height,t[6]=-e*i.width,t[7]=(1-r)*i.height}function js(i,t,e,r){let n=0;const s=i.length/t,o=r.a,a=r.b,l=r.c,c=r.d,h=r.tx,u=r.ty;for(e*=t;n<s;){const f=i[e],d=i[e+1];i[e]=o*f+l*d+h,i[e+1]=a*f+c*d+u,e+=t,n++}}function Ys(i,t){const e=i.texture,r=e.frame.width,n=e.frame.height;let s=0,o=0;i.applyAnchorToTexture&&(s=i.anchor.x,o=i.anchor.y),t[0]=t[6]=-s,t[2]=t[4]=1-s,t[1]=t[3]=-o,t[5]=t[7]=1-o;const a=H.shared;a.copyFrom(i._tileTransform.matrix),a.tx/=i.width,a.ty/=i.height,a.invert(),a.scale(i.width/r,i.height/n),js(t,2,0,a)}const $t=new Os;class Ns{constructor(){this.canBatch=!0,this.geometry=new Ae({indices:$t.indices.slice(),positions:$t.positions.slice(),uvs:$t.uvs.slice()})}destroy(){var t;this.geometry.destroy(),(t=this.shader)==null||t.destroy()}}class dn{constructor(t){this._state=jt.default2d,this._renderer=t}validateRenderable(t){const e=this._getTilingSpriteData(t),r=e.canBatch;this._updateCanBatch(t);const n=e.canBatch;if(n&&n===r){const{batchableMesh:s}=e;return!s._batcher.checkAndUpdateTexture(s,t.texture)}return r!==n}addRenderable(t,e){const r=this._renderer.renderPipes.batch;this._updateCanBatch(t);const n=this._getTilingSpriteData(t),{geometry:s,canBatch:o}=n;if(o){n.batchableMesh||(n.batchableMesh=new We);const a=n.batchableMesh;t.didViewUpdate&&(this._updateBatchableMesh(t),a.geometry=s,a.renderable=t,a.transform=t.groupTransform,a.setTexture(t._texture)),a.roundPixels=this._renderer._roundPixels|t._roundPixels,r.addToBatch(a,e)}else r.break(e),n.shader||(n.shader=new Vs),this.updateRenderable(t),e.add(t)}execute(t){const{shader:e}=this._getTilingSpriteData(t);e.groups[0]=this._renderer.globalUniforms.bindGroup;const r=e.resources.localUniforms.uniforms;r.uTransformMatrix=t.groupTransform,r.uRound=this._renderer._roundPixels|t._roundPixels,Qt(t.groupColorAlpha,r.uColor,0),this._state.blendMode=Te(t.groupBlendMode,t.texture._source),this._renderer.encoder.draw({geometry:$t,shader:e,state:this._state})}updateRenderable(t){const e=this._getTilingSpriteData(t),{canBatch:r}=e;if(r){const{batchableMesh:n}=e;t.didViewUpdate&&this._updateBatchableMesh(t),n._batcher.updateElement(n)}else if(t.didViewUpdate){const{shader:n}=e;n.updateUniforms(t.width,t.height,t._tileTransform.matrix,t.anchor.x,t.anchor.y,t.texture)}}_getTilingSpriteData(t){return t._gpuData[this._renderer.uid]||this._initTilingSpriteData(t)}_initTilingSpriteData(t){const e=new Ns;return e.renderable=t,t._gpuData[this._renderer.uid]=e,e}_updateBatchableMesh(t){const e=this._getTilingSpriteData(t),{geometry:r}=e,n=t.texture.source.style;n.addressMode!=="repeat"&&(n.addressMode="repeat",n.update()),Ys(t,r.uvs),$s(t,r.positions)}destroy(){this._renderer=null}_updateCanBatch(t){const e=this._getTilingSpriteData(t),r=t.texture;let n=!0;return this._renderer.type===we.WEBGL&&(n=this._renderer.context.supports.nonPowOf2wrapping),e.canBatch=r.textureMatrix.isSimple&&(n||r.source.isPowerOfTwo),e.canBatch}}dn.extension={type:[B.WebGLPipes,B.WebGPUPipes,B.CanvasPipes],name:"tilingSprite"};const qs={name:"local-uniform-msdf-bit",vertex:{header:`
            struct LocalUniforms {
                uColor:vec4<f32>,
                uTransformMatrix:mat3x3<f32>,
                uDistance: f32,
                uRound:f32,
            }

            @group(2) @binding(0) var<uniform> localUniforms : LocalUniforms;
        `,main:`
            vColor *= localUniforms.uColor;
            modelMatrix *= localUniforms.uTransformMatrix;
        `,end:`
            if(localUniforms.uRound == 1)
            {
                vPosition = vec4(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);
            }
        `},fragment:{header:`
            struct LocalUniforms {
                uColor:vec4<f32>,
                uTransformMatrix:mat3x3<f32>,
                uDistance: f32
            }

            @group(2) @binding(0) var<uniform> localUniforms : LocalUniforms;
         `,main:`
            outColor = vec4<f32>(calculateMSDFAlpha(outColor, localUniforms.uColor, localUniforms.uDistance));
        `}},Xs={name:"local-uniform-msdf-bit",vertex:{header:`
            uniform mat3 uTransformMatrix;
            uniform vec4 uColor;
            uniform float uRound;
        `,main:`
            vColor *= uColor;
            modelMatrix *= uTransformMatrix;
        `,end:`
            if(uRound == 1.)
            {
                gl_Position.xy = roundPixels(gl_Position.xy, uResolution);
            }
        `},fragment:{header:`
            uniform float uDistance;
         `,main:`
            outColor = vec4(calculateMSDFAlpha(outColor, vColor, uDistance));
        `}},Ks={name:"msdf-bit",fragment:{header:`
            fn calculateMSDFAlpha(msdfColor:vec4<f32>, shapeColor:vec4<f32>, distance:f32) -> f32 {

                // MSDF
                var median = msdfColor.r + msdfColor.g + msdfColor.b -
                    min(msdfColor.r, min(msdfColor.g, msdfColor.b)) -
                    max(msdfColor.r, max(msdfColor.g, msdfColor.b));

                // SDF
                median = min(median, msdfColor.a);

                var screenPxDistance = distance * (median - 0.5);
                var alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);
                if (median < 0.01) {
                    alpha = 0.0;
                } else if (median > 0.99) {
                    alpha = 1.0;
                }

                // Gamma correction for coverage-like alpha
                var luma: f32 = dot(shapeColor.rgb, vec3<f32>(0.299, 0.587, 0.114));
                var gamma: f32 = mix(1.0, 1.0 / 2.2, luma);
                var coverage: f32 = pow(shapeColor.a * alpha, gamma);

                return coverage;

            }
        `}},Zs={name:"msdf-bit",fragment:{header:`
            float calculateMSDFAlpha(vec4 msdfColor, vec4 shapeColor, float distance) {

                // MSDF
                float median = msdfColor.r + msdfColor.g + msdfColor.b -
                                min(msdfColor.r, min(msdfColor.g, msdfColor.b)) -
                                max(msdfColor.r, max(msdfColor.g, msdfColor.b));

                // SDF
                median = min(median, msdfColor.a);

                float screenPxDistance = distance * (median - 0.5);
                float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);

                if (median < 0.01) {
                    alpha = 0.0;
                } else if (median > 0.99) {
                    alpha = 1.0;
                }

                // Gamma correction for coverage-like alpha
                float luma = dot(shapeColor.rgb, vec3(0.299, 0.587, 0.114));
                float gamma = mix(1.0, 1.0 / 2.2, luma);
                float coverage = pow(shapeColor.a * alpha, gamma);

                return coverage;
            }
        `}};let he,ce;class Qs extends Pe{constructor(t){const e=new mt({uColor:{value:new Float32Array([1,1,1,1]),type:"vec4<f32>"},uTransformMatrix:{value:new H,type:"mat3x3<f32>"},uDistance:{value:4,type:"f32"},uRound:{value:0,type:"f32"}});he??(he=Cr({name:"sdf-shader",bits:[Wn,Ln(t),qs,Ks,Mr]})),ce??(ce=kr({name:"sdf-shader",bits:[Dn,In(t),Xs,Zs,Fr]})),super({glProgram:ce,gpuProgram:he,resources:{localUniforms:e,batchSamplers:Hn(t)}})}}class Js extends Xt{destroy(){this.context.customShader&&this.context.customShader.destroy(),super.destroy()}}class fn{constructor(t){this._renderer=t,this._renderer.renderableGC.addManagedHash(this,"_gpuBitmapText")}validateRenderable(t){const e=this._getGpuBitmapText(t);return t._didTextUpdate&&(t._didTextUpdate=!1,this._updateContext(t,e)),this._renderer.renderPipes.graphics.validateRenderable(e)}addRenderable(t,e){const r=this._getGpuBitmapText(t);Tr(t,r),t._didTextUpdate&&(t._didTextUpdate=!1,this._updateContext(t,r)),this._renderer.renderPipes.graphics.addRenderable(r,e),r.context.customShader&&this._updateDistanceField(t)}updateRenderable(t){const e=this._getGpuBitmapText(t);Tr(t,e),this._renderer.renderPipes.graphics.updateRenderable(e),e.context.customShader&&this._updateDistanceField(t)}_updateContext(t,e){const{context:r}=e,n=ys.getFont(t.text,t._style);r.clear(),n.distanceField.type!=="none"&&(r.customShader||(r.customShader=new Qs(this._renderer.limits.maxBatchableTextures)));const s=Z.graphemeSegmenter(t.text),o=t._style;let a=n.baseLineOffset;const l=Nr(s,o,n,!0),c=o.padding,h=l.scale;let u=l.width,f=l.height+l.offsetY;o._stroke&&(u+=o._stroke.width/h,f+=o._stroke.width/h),r.translate(-t._anchor._x*u-c,-t._anchor._y*f-c).scale(h,h);const d=n.applyFillAsTint?o._fill.color:16777215;for(let p=0;p<l.lines.length;p++){const m=l.lines[p];for(let g=0;g<m.charPositions.length;g++){const x=m.chars[g],y=n.chars[x];y!=null&&y.texture&&r.texture(y.texture,d||"black",Math.round(m.charPositions[g]+y.xOffset),Math.round(a+y.yOffset))}a+=n.lineHeight}}_getGpuBitmapText(t){return t._gpuData[this._renderer.uid]||this.initGpuText(t)}initGpuText(t){const e=new Js;return t._gpuData[this._renderer.uid]=e,this._updateContext(t,e),e}_updateDistanceField(t){const e=this._getGpuBitmapText(t).context,r=t._style.fontFamily,n=q.get(`${r}-bitmap`),{a:s,b:o,c:a,d:l}=t.groupTransform,c=Math.sqrt(s*s+o*o),h=Math.sqrt(a*a+l*l),u=(Math.abs(c)+Math.abs(h))/2,f=n.baseRenderedFontSize/t._style.fontSize,d=u*n.distanceField.range*(1/f);e.customShader.resources.localUniforms.uniforms.uDistance=d}destroy(){this._renderer=null}}fn.extension={type:[B.WebGLPipes,B.WebGPUPipes,B.CanvasPipes],name:"bitmapText"};function Tr(i,t){t.groupTransform=i.groupTransform,t.groupColorAlpha=i.groupColorAlpha,t.groupColor=i.groupColor,t.groupBlendMode=i.groupBlendMode,t.globalDisplayStatus=i.globalDisplayStatus,t.groupTransform=i.groupTransform,t.localDisplayStatus=i.localDisplayStatus,t.groupAlpha=i.groupAlpha,t._roundPixels=i._roundPixels}class to extends Br{constructor(t){super(),this.generatingTexture=!1,this._renderer=t,t.runners.resolutionChange.add(this)}resolutionChange(){const t=this.renderable;t._autoResolution&&t.onViewUpdate()}destroy(){this._renderer.htmlText.returnTexturePromise(this.texturePromise),this.texturePromise=null,this._renderer=null}}function _e(i,t){const{texture:e,bounds:r}=i,n=t._style._getFinalPadding();Fn(r,t._anchor,e);const s=t._anchor._x*n*2,o=t._anchor._y*n*2;r.minX-=n-s,r.minY-=n-o,r.maxX-=n-s,r.maxY-=n-o}class pn{constructor(t){this._renderer=t}validateRenderable(t){return t._didTextUpdate}addRenderable(t,e){const r=this._getGpuText(t);t._didTextUpdate&&(this._updateGpuText(t).catch(n=>{console.error(n)}),t._didTextUpdate=!1,_e(r,t)),this._renderer.renderPipes.batch.addToBatch(r,e)}updateRenderable(t){const e=this._getGpuText(t);e._batcher.updateElement(e)}async _updateGpuText(t){t._didTextUpdate=!1;const e=this._getGpuText(t);if(e.generatingTexture)return;e.texturePromise&&(this._renderer.htmlText.returnTexturePromise(e.texturePromise),e.texturePromise=null),e.generatingTexture=!0,t._resolution=t._autoResolution?this._renderer.resolution:t.resolution;const r=this._renderer.htmlText.getTexturePromise(t);e.texturePromise=r,e.texture=await r;const n=t.renderGroup||t.parentRenderGroup;n&&(n.structureDidChange=!0),e.generatingTexture=!1,_e(e,t)}_getGpuText(t){return t._gpuData[this._renderer.uid]||this.initGpuText(t)}initGpuText(t){const e=new to(this._renderer);return e.renderable=t,e.transform=t.groupTransform,e.texture=E.EMPTY,e.bounds={minX:0,maxX:1,minY:0,maxY:0},e.roundPixels=this._renderer._roundPixels|t._roundPixels,t._resolution=t._autoResolution?this._renderer.resolution:t.resolution,t._gpuData[this._renderer.uid]=e,e}destroy(){this._renderer=null}}pn.extension={type:[B.WebGLPipes,B.WebGPUPipes,B.CanvasPipes],name:"htmlText"};function eo(){const{userAgent:i}=dt.get().getNavigator();return/^((?!chrome|android).)*safari/i.test(i)}const ro=new Zt;function gn(i,t,e,r){const n=ro;n.minX=0,n.minY=0,n.maxX=i.width/r|0,n.maxY=i.height/r|0;const s=X.getOptimalTexture(n.width,n.height,r,!1);return s.source.uploadMethodId="image",s.source.resource=i,s.source.alphaMode="premultiply-alpha-on-upload",s.frame.width=t/r,s.frame.height=e/r,s.source.emit("update",s.source),s.updateUvs(),s}function no(i,t){const e=t.fontFamily,r=[],n={},s=/font-family:([^;"\s]+)/g,o=i.match(s);function a(l){n[l]||(r.push(l),n[l]=!0)}if(Array.isArray(e))for(let l=0;l<e.length;l++)a(e[l]);else a(e);o&&o.forEach(l=>{const c=l.split(":")[1].trim();a(c)});for(const l in t.tagStyles){const c=t.tagStyles[l].fontFamily;a(c)}return r}async function io(i){const e=await(await dt.get().fetch(i)).blob(),r=new FileReader;return await new Promise((s,o)=>{r.onloadend=()=>s(r.result),r.onerror=o,r.readAsDataURL(e)})}async function Pr(i,t){const e=await io(t);return`@font-face {
        font-family: "${i.fontFamily}";
        src: url('${e}');
        font-weight: ${i.fontWeight};
        font-style: ${i.fontStyle};
    }`}const Ot=new Map;async function so(i,t,e){const r=i.filter(n=>q.has(`${n}-and-url`)).map((n,s)=>{if(!Ot.has(n)){const{url:o}=q.get(`${n}-and-url`);s===0?Ot.set(n,Pr({fontWeight:t.fontWeight,fontStyle:t.fontStyle,fontFamily:n},o)):Ot.set(n,Pr({fontWeight:e.fontWeight,fontStyle:e.fontStyle,fontFamily:n},o))}return Ot.get(n)});return(await Promise.all(r)).join(`
`)}function oo(i,t,e,r,n){const{domElement:s,styleElement:o,svgRoot:a}=n;s.innerHTML=`<style>${t.cssStyle}</style><div style='padding:0;'>${i}</div>`,s.setAttribute("style",`transform: scale(${e});transform-origin: top left; display: inline-block`),o.textContent=r;const{width:l,height:c}=n.image;return a.setAttribute("width",l.toString()),a.setAttribute("height",c.toString()),new XMLSerializer().serializeToString(a)}function ao(i,t){const e=xt.getOptimalCanvasAndContext(i.width,i.height,t),{context:r}=e;return r.clearRect(0,0,i.width,i.height),r.drawImage(i,0,0),e}function lo(i,t,e){return new Promise(async r=>{e&&await new Promise(n=>setTimeout(n,100)),i.onload=()=>{r()},i.src=`data:image/svg+xml;charset=utf8,${encodeURIComponent(t)}`,i.crossOrigin="anonymous"})}class xn{constructor(t){this._renderer=t,this._createCanvas=t.type===we.WEBGPU}getTexture(t){return this.getTexturePromise(t)}getTexturePromise(t){return this._buildTexturePromise(t)}async _buildTexturePromise(t){const{text:e,style:r,resolution:n,textureStyle:s}=t,o=tt.get(tn),a=no(e,r),l=await so(a,r,ze.defaultTextStyle),c=Ms(e,r,l,o),h=Math.ceil(Math.ceil(Math.max(1,c.width)+r.padding*2)*n),u=Math.ceil(Math.ceil(Math.max(1,c.height)+r.padding*2)*n),f=o.image,d=2;f.width=(h|0)+d,f.height=(u|0)+d;const p=oo(e,r,n,l,o);await lo(f,p,eo()&&a.length>0);const m=f;let g;this._createCanvas&&(g=ao(f,n));const x=gn(g?g.canvas:m,f.width-d,f.height-d,n);return s&&(x.source.style=s),this._createCanvas&&(this._renderer.texture.initSource(x.source),xt.returnCanvasAndContext(g)),tt.return(o),x}returnTexturePromise(t){t.then(e=>{this._cleanUp(e)}).catch(()=>{Y("HTMLTextSystem: Failed to clean texture")})}_cleanUp(t){X.returnTexture(t,!0),t.source.resource=null,t.source.uploadMethodId="unknown"}destroy(){this._renderer=null}}xn.extension={type:[B.WebGLSystem,B.WebGPUSystem,B.CanvasSystem],name:"htmlText"};class ho extends Br{constructor(t){super(),this._renderer=t,t.runners.resolutionChange.add(this)}resolutionChange(){const t=this.renderable;t._autoResolution&&t.onViewUpdate()}destroy(){this._renderer.canvasText.returnTexture(this.texture),this._renderer=null}}class mn{constructor(t){this._renderer=t}validateRenderable(t){return t._didTextUpdate}addRenderable(t,e){const r=this._getGpuText(t);t._didTextUpdate&&(this._updateGpuText(t),t._didTextUpdate=!1),this._renderer.renderPipes.batch.addToBatch(r,e)}updateRenderable(t){const e=this._getGpuText(t);e._batcher.updateElement(e)}_updateGpuText(t){const e=this._getGpuText(t);e.texture&&this._renderer.canvasText.returnTexture(e.texture),t._resolution=t._autoResolution?this._renderer.resolution:t.resolution,e.texture=e.texture=this._renderer.canvasText.getTexture(t),_e(e,t)}_getGpuText(t){return t._gpuData[this._renderer.uid]||this.initGpuText(t)}initGpuText(t){const e=new ho(this._renderer);return e.renderable=t,e.transform=t.groupTransform,e.bounds={minX:0,maxX:1,minY:0,maxY:0},e.roundPixels=this._renderer._roundPixels|t._roundPixels,t._gpuData[this._renderer.uid]=e,e}destroy(){this._renderer=null}}mn.extension={type:[B.WebGLPipes,B.WebGPUPipes,B.CanvasPipes],name:"text"};class yn{constructor(t){this._renderer=t}getTexture(t,e,r,n){typeof t=="string"&&(A("8.0.0","CanvasTextSystem.getTexture: Use object TextOptions instead of separate arguments"),t={text:t,style:r,resolution:e}),t.style instanceof nt||(t.style=new nt(t.style)),t.textureStyle instanceof Ft||(t.textureStyle=new Ft(t.textureStyle)),typeof t.text!="string"&&(t.text=t.text.toString());const{text:s,style:o,textureStyle:a}=t,l=t.resolution??this._renderer.resolution,{frame:c,canvasAndContext:h}=oe.getCanvasAndContext({text:s,style:o,resolution:l}),u=gn(h.canvas,c.width,c.height,l);if(a&&(u.source.style=a),o.trim&&(c.pad(o.padding),u.frame.copyFrom(c),u.updateUvs()),o.filters){const f=this._applyFilters(u,o.filters);return this.returnTexture(u),oe.returnCanvasAndContext(h),f}return this._renderer.texture.initSource(u._source),oe.returnCanvasAndContext(h),u}returnTexture(t){const e=t.source;e.resource=null,e.uploadMethodId="unknown",e.alphaMode="no-premultiply-alpha",X.returnTexture(t,!0)}renderTextToCanvas(){A("8.10.0","CanvasTextSystem.renderTextToCanvas: no longer supported, use CanvasTextSystem.getTexture instead")}_applyFilters(t,e){const r=this._renderer.renderTarget.renderTarget,n=this._renderer.filter.generateFilteredTexture({texture:t,filters:e});return this._renderer.renderTarget.bind(r,!1),n}destroy(){this._renderer=null}}yn.extension={type:[B.WebGLSystem,B.WebGPUSystem,B.CanvasSystem],name:"canvasText"};V.add(zr);V.add(Wr);V.add(en);V.add(Re);V.add(sn);V.add(an);V.add(ln);V.add(yn);V.add(mn);V.add(fn);V.add(xn);V.add(pn);V.add(dn);V.add(un);V.add(Xr);V.add(qr);
