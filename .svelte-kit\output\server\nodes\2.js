

export const index = 2;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/_page.svelte.js')).default;
export const universal = {
  "ssr": false
};
export const universal_id = "src/routes/+page.js";
export const imports = ["_app/immutable/nodes/2.x68yuLCA.js","_app/immutable/chunks/DfsOTG4T.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/CfAXL0yB.js","_app/immutable/chunks/DAVv5PCD.js","_app/immutable/chunks/BT-7oyhX.js","_app/immutable/chunks/DX-db2lG.js"];
export const stylesheets = ["_app/immutable/assets/2.C-jGBG-c.css"];
export const fonts = [];
