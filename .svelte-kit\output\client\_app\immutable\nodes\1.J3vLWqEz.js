import"../chunks/CWj6FrbW.js";import{i as h}from"../chunks/CfAXL0yB.js";import{p as g,l,f as v,t as d,a as _,m as x,n as e,o,q as $}from"../chunks/DAVv5PCD.js";import{s as p}from"../chunks/BT-7oyhX.js";import{s as k,p as m}from"../chunks/C7fiD0P4.js";const b={get error(){return m.error},get status(){return m.status}};k.updated.check;const i=b;var q=l("<h1> </h1> <p> </p>",1);function A(n,f){g(f,!1),h();var r=q(),t=v(r),c=e(t,!0);o(t);var s=$(t,2),u=e(s,!0);o(s),d(()=>{var a;p(c,i.status),p(u,(a=i.error)==null?void 0:a.message)}),_(n,r),x()}export{A as component};
