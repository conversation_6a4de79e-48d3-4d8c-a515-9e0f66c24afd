<script lang="ts">
  import { taskStore, userStore } from '$lib/stores';
  import type { Task } from '$lib/types';

  export let task: Task;
  
  let isEditing = false;
  let editTitle = task.title;
  let editDescription = task.description;
  let editPriority = task.priority;
  let editCategory = task.category;
  let editDueDate = task.dueDate || '';
  
  // Format due date for display
  $: formattedDueDate = task.dueDate ? new Date(task.dueDate).toLocaleDateString() : '';
  $: isOverdue = task.dueDate && !task.completed && new Date(task.dueDate) < new Date();
  $: isDueSoon = task.dueDate && !task.completed && 
    new Date(task.dueDate).getTime() - new Date().getTime() < 24 * 60 * 60 * 1000;
  
  function toggleComplete() {
    if (!task.completed) {
      // Task is being completed - award bananas
      userStore.addBananas(task.bananaReward);
    }
    taskStore.updateTask(task.id, { completed: !task.completed });
  }
  
  function startEdit() {
    isEditing = true;
    editTitle = task.title;
    editDescription = task.description;
    editPriority = task.priority;
    editCategory = task.category;
    editDueDate = task.dueDate || '';
  }
  
  function saveEdit() {
    if (!editTitle.trim()) return;
    
    taskStore.updateTask(task.id, {
      title: editTitle.trim(),
      description: editDescription.trim(),
      priority: editPriority,
      category: editCategory.trim(),
      dueDate: editDueDate || undefined
    });
    
    isEditing = false;
  }
  
  function cancelEdit() {
    isEditing = false;
  }
  
  function deleteTask() {
    if (confirm('Are you sure you want to delete this task?')) {
      taskStore.deleteTask(task.id);
    }
  }
  
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      saveEdit();
    } else if (event.key === 'Escape') {
      cancelEdit();
    }
  }
  
  function getPriorityColor(priority: string) {
    switch (priority) {
      case 'high': return '#EF4444';
      case 'medium': return '#F59E0B';
      case 'low': return '#10B981';
      default: return '#6B7280';
    }
  }
</script>

<div class="task-item" class:completed={task.completed} class:overdue={isOverdue}>
  <div class="task-main">
    <label class="checkbox-container">
      <input 
        type="checkbox" 
        checked={task.completed} 
        on:change={toggleComplete}
      />
      <span class="checkmark"></span>
    </label>
    
    <div class="task-content">
      {#if isEditing}
        <div class="edit-form">
          <input
            type="text"
            bind:value={editTitle}
            on:keydown={handleKeydown}
            placeholder="Task title"
            class="edit-title"
          />
          <textarea
            bind:value={editDescription}
            on:keydown={handleKeydown}
            placeholder="Description (optional)"
            class="edit-description"
            rows="2"
          ></textarea>
          <div class="edit-controls">
            <select bind:value={editPriority} class="edit-priority">
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
            </select>
            <input
              type="text"
              bind:value={editCategory}
              placeholder="Category"
              class="edit-category"
            />
            <input
              type="date"
              bind:value={editDueDate}
              class="edit-due-date"
            />
          </div>
          <div class="edit-actions">
            <button on:click={saveEdit} class="save-btn">Save</button>
            <button on:click={cancelEdit} class="cancel-btn">Cancel</button>
          </div>
        </div>
      {:else}
        <div class="task-info">
          <h4 class="task-title" class:completed={task.completed}>
            {task.title}
          </h4>
          
          {#if task.description}
            <p class="task-description">{task.description}</p>
          {/if}
          
          <div class="task-meta">
            <span 
              class="priority-badge" 
              style="background-color: {getPriorityColor(task.priority)}"
            >
              {task.priority}
            </span>
            
            {#if task.category}
              <span class="category-badge">📂 {task.category}</span>
            {/if}
            
            {#if task.dueDate}
              <span 
                class="due-date-badge" 
                class:overdue={isOverdue}
                class:due-soon={isDueSoon}
              >
                📅 {formattedDueDate}
              </span>
            {/if}
            
            <span class="banana-reward">🍌 {task.bananaReward}</span>
          </div>
        </div>
      {/if}
    </div>
    
    {#if !isEditing}
      <div class="task-actions">
        <button on:click={startEdit} class="edit-btn" title="Edit task">
          ✏️
        </button>
        <button on:click={deleteTask} class="delete-btn" title="Delete task">
          🗑️
        </button>
      </div>
    {/if}
  </div>
</div>

<style>
  .task-item {
    border-bottom: 1px solid #E5E7EB;
    transition: background-color 0.2s;
  }
  
  .task-item:hover {
    background-color: #F9FAFB;
  }
  
  .task-item.completed {
    opacity: 0.7;
  }
  
  .task-item.overdue {
    border-left: 4px solid #EF4444;
  }
  
  .task-main {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 1.5rem;
  }
  
  .checkbox-container {
    position: relative;
    cursor: pointer;
    margin-top: 0.25rem;
  }
  
  .checkbox-container input {
    opacity: 0;
    position: absolute;
    cursor: pointer;
  }
  
  .checkmark {
    display: block;
    width: 20px;
    height: 20px;
    background-color: white;
    border: 2px solid #D1D5DB;
    border-radius: 4px;
    transition: all 0.2s;
  }
  
  .checkbox-container:hover .checkmark {
    border-color: #10B981;
  }
  
  .checkbox-container input:checked ~ .checkmark {
    background-color: #10B981;
    border-color: #10B981;
  }
  
  .checkbox-container input:checked ~ .checkmark::after {
    content: '✓';
    color: white;
    font-size: 14px;
    font-weight: bold;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  
  .task-content {
    flex: 1;
    min-width: 0;
  }
  
  .task-title {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    color: #374151;
    word-wrap: break-word;
  }
  
  .task-title.completed {
    text-decoration: line-through;
    color: #9CA3AF;
  }
  
  .task-description {
    margin: 0 0 0.75rem 0;
    color: #6B7280;
    font-size: 0.9rem;
    line-height: 1.4;
  }
  
  .task-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
  }
  
  .priority-badge, .category-badge, .due-date-badge, .banana-reward {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
  }
  
  .priority-badge {
    color: white;
    text-transform: capitalize;
  }
  
  .category-badge {
    background-color: #E0E7FF;
    color: #3730A3;
  }
  
  .due-date-badge {
    background-color: #FEF3C7;
    color: #92400E;
  }
  
  .due-date-badge.overdue {
    background-color: #FEE2E2;
    color: #991B1B;
  }
  
  .due-date-badge.due-soon {
    background-color: #FED7AA;
    color: #9A3412;
  }
  
  .banana-reward {
    background-color: #FEF7CD;
    color: #92400E;
  }
  
  .task-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.25rem;
  }
  
  .edit-btn, .delete-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.2s;
    font-size: 1rem;
  }
  
  .edit-btn:hover {
    background-color: #E0E7FF;
  }
  
  .delete-btn:hover {
    background-color: #FEE2E2;
  }
  
  .edit-form {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .edit-title, .edit-description, .edit-priority, .edit-category, .edit-due-date {
    padding: 0.5rem;
    border: 1px solid #D1D5DB;
    border-radius: 6px;
    font-size: 0.9rem;
  }
  
  .edit-title {
    font-weight: 500;
  }
  
  .edit-description {
    resize: vertical;
    min-height: 60px;
  }
  
  .edit-controls {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 0.5rem;
  }
  
  .edit-actions {
    display: flex;
    gap: 0.5rem;
  }
  
  .save-btn, .cancel-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .save-btn {
    background-color: #10B981;
    color: white;
  }
  
  .save-btn:hover {
    background-color: #059669;
  }
  
  .cancel-btn {
    background-color: #6B7280;
    color: white;
  }
  
  .cancel-btn:hover {
    background-color: #4B5563;
  }
  
  @media (max-width: 768px) {
    .task-main {
      padding: 1rem;
    }
    
    .task-meta {
      flex-direction: column;
      align-items: flex-start;
    }
    
    .edit-controls {
      grid-template-columns: 1fr;
    }
  }
</style>
