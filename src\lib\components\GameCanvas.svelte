<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { browser } from '$app/environment';
  import * as PIXI from 'pixi.js';
  import { gameStore, userStore } from '$lib/stores';
  import { updateInputFromKeyboard, createBananaParticles, updateParticles, type Particle } from '$lib/utils/gameUtils';

  // Canvas element reference
  let canvasContainer: HTMLDivElement;
  let pixiApp: PIXI.Application;
  let gameInitialized = false;
  
  // Game objects
  let monkey: PIXI.Sprite;
  const bananas: PIXI.Sprite[] = [];
  let particles: Particle[] = [];
  let particleContainer: PIXI.Container;

  // Input handling
  const keysPressed = new Set<string>();

  // Game state
  const monkeyVelocity = { x: 0, y: 0 };
  const GRAVITY = 0.5;
  const JUMP_FORCE = -12;
  const MOVE_SPEED = 5;
  const GROUND_Y = 500;

  onMount(async () => {
    if (browser) {
      await initializeGame();
    }
  });

  onDestroy(() => {
    cleanup();
  });

  async function initializeGame() {
    try {
      // Create PixiJS application
      pixiApp = new PIXI.Application();
      await pixiApp.init({
        width: 800,
        height: 600,
        backgroundColor: 0x228B22, // Forest green
        antialias: true
      });

      // Add canvas to container
      // eslint-disable-next-line svelte/no-dom-manipulating
      canvasContainer.appendChild(pixiApp.canvas);

      // Create game objects
      await createGameObjects();
      
      // Set up input handling
      setupInputHandling();
      
      // Start game loop
      pixiApp.ticker.add(gameLoop);
      
      gameInitialized = true;
      // eslint-disable-next-line no-console
      console.log('Game initialized successfully!');
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to initialize game:', error);
    }
  }

  async function createGameObjects() {
    // Create monkey sprite (placeholder rectangle for now)
    monkey = new PIXI.Sprite(PIXI.Texture.WHITE);
    monkey.width = 32;
    monkey.height = 32;
    monkey.tint = 0x8B4513; // Brown color for monkey
    monkey.x = 100;
    monkey.y = GROUND_Y - 32;
    pixiApp.stage.addChild(monkey);

    // Create particle container
    particleContainer = new PIXI.Container();
    pixiApp.stage.addChild(particleContainer);

    // Create some bananas to collect
    createBananas();
    
    // Create ground (simple rectangle)
    const ground = new PIXI.Sprite(PIXI.Texture.WHITE);
    ground.width = 800;
    ground.height = 20;
    ground.tint = 0x654321; // Brown ground
    ground.x = 0;
    ground.y = GROUND_Y + 12;
    pixiApp.stage.addChild(ground);
  }

  function createBananas() {
    // Create a few bananas to collect
    const bananaPositions = [
      { x: 200, y: 450 },
      { x: 400, y: 350 },
      { x: 600, y: 400 },
      { x: 300, y: 300 }
    ];

    bananaPositions.forEach(pos => {
      const banana = new PIXI.Sprite(PIXI.Texture.WHITE);
      banana.width = 20;
      banana.height = 20;
      banana.tint = 0xFFD700; // Gold color for banana
      banana.x = pos.x;
      banana.y = pos.y;
      bananas.push(banana);
      pixiApp.stage.addChild(banana);
    });
  }

  function setupInputHandling() {
    // Keyboard event listeners
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
  }

  function handleKeyDown(event: KeyboardEvent) {
    keysPressed.add(event.code);
    event.preventDefault();
  }

  function handleKeyUp(event: KeyboardEvent) {
    keysPressed.delete(event.code);
    event.preventDefault();
  }

  function gameLoop(ticker: PIXI.Ticker) {
    if (!gameInitialized) return;

    // Update input
    const input = updateInputFromKeyboard({
      left: false,
      right: false,
      up: false,
      down: false,
      jump: false,
      interact: false
    }, keysPressed);

    // Update monkey movement
    updateMonkeyMovement(input, ticker.deltaTime);
    
    // Check banana collisions
    checkBananaCollisions();
    
    // Update particles
    updateParticleSystem();
    
    // Update stores
    gameStore.updateMonkeyPosition(monkey.x, monkey.y);
  }

  function updateMonkeyMovement(input: { left: boolean; right: boolean; up: boolean; jump: boolean }, deltaTime: number) {
    // Horizontal movement
    if (input.left) {
      monkeyVelocity.x = -MOVE_SPEED;
    } else if (input.right) {
      monkeyVelocity.x = MOVE_SPEED;
    } else {
      monkeyVelocity.x *= 0.8; // Friction
    }

    // Jumping
    if (input.jump && monkey.y >= GROUND_Y - 32) {
      monkeyVelocity.y = JUMP_FORCE;
    }

    // Apply gravity
    monkeyVelocity.y += GRAVITY * deltaTime;

    // Update position
    monkey.x += monkeyVelocity.x * deltaTime;
    monkey.y += monkeyVelocity.y * deltaTime;

    // Boundary checks
    monkey.x = Math.max(0, Math.min(monkey.x, 800 - 32));
    
    // Ground collision
    if (monkey.y >= GROUND_Y - 32) {
      monkey.y = GROUND_Y - 32;
      monkeyVelocity.y = 0;
    }
  }

  function checkBananaCollisions() {
    bananas.forEach((banana) => {
      if (banana.visible && 
          monkey.x < banana.x + banana.width &&
          monkey.x + monkey.width > banana.x &&
          monkey.y < banana.y + banana.height &&
          monkey.y + monkey.height > banana.y) {
        
        // Collect banana
        banana.visible = false;
        
        // Add bananas to user store
        userStore.addBananas(5);
        
        // Create particle effect
        const newParticles = createBananaParticles(banana.x + 10, banana.y + 10, 8);
        particles.push(...newParticles);
        
        // eslint-disable-next-line no-console
        console.log('Banana collected! +5 bananas');
      }
    });
  }

  function updateParticleSystem() {
    // Update existing particles
    particles = updateParticles(particles);
    
    // Clear particle container
    particleContainer.removeChildren();
    
    // Render particles
    particles.forEach(particle => {
      const particleSprite = new PIXI.Sprite(PIXI.Texture.WHITE);
      particleSprite.width = 4 * particle.scale;
      particleSprite.height = 4 * particle.scale;
      particleSprite.tint = particle.color;
      particleSprite.x = particle.x;
      particleSprite.y = particle.y;
      particleSprite.alpha = particle.life / particle.maxLife;
      particleContainer.addChild(particleSprite);
    });
  }

  function cleanup() {
    if (pixiApp) {
      pixiApp.destroy(true);
    }

    // Remove event listeners (only if in browser)
    if (browser) {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    }

    gameInitialized = false;
  }
</script>

{#if browser}
  <div class="game-canvas-container">
    <div bind:this={canvasContainer} class="canvas-wrapper"></div>

    <div class="game-controls">
      <p><strong>Controls:</strong></p>
      <p>Arrow Keys or WASD: Move</p>
      <p>Space: Jump</p>
      <p>Collect golden bananas to earn rewards!</p>
    </div>
  </div>
{:else}
  <div class="game-loading">
    <p>🐒 Loading jungle adventure...</p>
  </div>
{/if}

<style>
  .game-canvas-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
  
  .canvas-wrapper {
    border: 2px solid #10B981;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .game-controls {
    background: #F3F4F6;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    font-size: 0.9rem;
    color: #374151;
  }
  
  .game-controls p {
    margin: 0.25rem 0;
  }
  
  .game-controls strong {
    color: #10B981;
  }

  .game-loading {
    background: #F3F4F6;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    color: #6B7280;
    border: 2px dashed #D1D5DB;
  }
</style>
