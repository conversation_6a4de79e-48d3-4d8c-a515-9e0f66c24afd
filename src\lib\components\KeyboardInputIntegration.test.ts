import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, fireEvent, screen } from '@testing-library/svelte';
import userEvent from '@testing-library/user-event';
import TaskForm from './TaskForm.svelte';
import GameCanvas from './GameCanvas.svelte';

// Mock PIXI.js for GameCanvas
vi.mock('pixi.js', () => ({
  Application: vi.fn(() => ({
    canvas: (() => {
      const canvas = document.createElement('canvas');
      canvas.tabIndex = 0;
      return canvas;
    })(),
    ticker: {
      add: vi.fn(),
      remove: vi.fn()
    },
    destroy: vi.fn()
  })),
  Sprite: {
    from: vi.fn(() => ({
      x: 0,
      y: 0,
      width: 32,
      height: 32,
      anchor: { set: vi.fn() }
    }))
  },
  Container: vi.fn(() => ({
    addChild: vi.fn(),
    removeChild: vi.fn()
  })),
  Assets: {
    load: vi.fn(() => Promise.resolve({}))
  }
}));

// Mock browser environment
vi.mock('$app/environment', () => ({
  browser: true
}));

// Mock stores
vi.mock('$lib/stores', () => ({
  taskStore: {
    add: vi.fn(),
    subscribe: vi.fn()
  },
  gameStore: {
    subscribe: vi.fn(),
    update: vi.fn()
  },
  userStore: {
    addBananas: vi.fn()
  }
}));

// Mock game utils
vi.mock('$lib/utils/gameUtils', () => ({
  updateInputFromKeyboard: vi.fn(() => ({ left: false, right: false, up: false, jump: false })),
  createBananaParticles: vi.fn(() => []),
  updateParticles: vi.fn()
}));

// Mock banana calculator
vi.mock('$lib/utils/bananaCalculator', () => ({
  BananaCalculator: {
    getInstance: () => ({
      calculateTaskReward: vi.fn(() => 10)
    })
  }
}));

describe('Keyboard Input Integration Tests', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    user = userEvent.setup();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should allow typing in task form while game canvas is present', async () => {
    // Create a test component that includes both TaskForm and GameCanvas
    const TestComponent = `
      <script>
        import TaskForm from './TaskForm.svelte';
        import GameCanvas from './GameCanvas.svelte';
      </script>
      
      <div>
        <TaskForm />
        <GameCanvas />
      </div>
    `;

    // Render both components together
    const { container } = render(TaskForm);
    render(GameCanvas);
    
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Find the task title input
    const titleInput = screen.getByLabelText(/task title/i);
    expect(titleInput).toBeInTheDocument();
    
    // Type in the input field
    await user.type(titleInput, 'Test Task Title');
    
    // Verify the text was entered correctly
    expect(titleInput).toHaveValue('Test Task Title');
  });

  it('should not interfere with form submission when game is present', async () => {
    const { container } = render(TaskForm);
    render(GameCanvas);
    
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Fill out the form
    const titleInput = screen.getByLabelText(/task title/i);
    const descriptionInput = screen.getByLabelText(/description/i);
    const submitButton = screen.getByRole('button', { name: /add task/i });
    
    await user.type(titleInput, 'Integration Test Task');
    await user.type(descriptionInput, 'This is a test description');
    
    // Submit the form
    await user.click(submitButton);
    
    // Verify form was submitted (input should be cleared)
    expect(titleInput).toHaveValue('');
    expect(descriptionInput).toHaveValue('');
  });

  it('should handle keyboard events correctly when switching focus', async () => {
    const { container } = render(TaskForm);
    const gameContainer = render(GameCanvas);
    
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const titleInput = screen.getByLabelText(/task title/i);
    const canvasWrapper = gameContainer.container.querySelector('.canvas-wrapper');
    
    // Start by typing in the form
    await user.type(titleInput, 'Test');
    expect(titleInput).toHaveValue('Test');
    
    // Click on the game canvas to focus it
    if (canvasWrapper) {
      await user.click(canvasWrapper);
    }
    
    // Type more in the input (should still work)
    await user.type(titleInput, ' More Text');
    expect(titleInput).toHaveValue('Test More Text');
  });

  it('should prevent game input when focused on form elements', async () => {
    const { container } = render(TaskForm);
    render(GameCanvas);
    
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const titleInput = screen.getByLabelText(/task title/i);
    
    // Focus the input
    await user.click(titleInput);
    
    // Simulate game control keys (these should not be captured by the game)
    const arrowLeftEvent = new KeyboardEvent('keydown', { 
      code: 'ArrowLeft',
      bubbles: true,
      cancelable: true 
    });
    
    const spaceEvent = new KeyboardEvent('keydown', { 
      code: 'Space',
      bubbles: true,
      cancelable: true 
    });
    
    // Spy on preventDefault to ensure it's not called for form inputs
    const arrowPreventSpy = vi.spyOn(arrowLeftEvent, 'preventDefault');
    const spacePreventSpy = vi.spyOn(spaceEvent, 'preventDefault');
    
    // Dispatch events while input is focused
    titleInput.dispatchEvent(arrowLeftEvent);
    titleInput.dispatchEvent(spaceEvent);
    
    // Game should not capture these events
    expect(arrowPreventSpy).not.toHaveBeenCalled();
    expect(spacePreventSpy).not.toHaveBeenCalled();
  });

  it('should handle special keys in form fields correctly', async () => {
    const { container } = render(TaskForm);
    render(GameCanvas);
    
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const titleInput = screen.getByLabelText(/task title/i);
    
    // Type text with spaces and special characters
    await user.type(titleInput, 'Task with spaces and symbols!@#');
    
    expect(titleInput).toHaveValue('Task with spaces and symbols!@#');
    
    // Test backspace
    await user.type(titleInput, '{backspace}{backspace}{backspace}');
    expect(titleInput).toHaveValue('Task with spaces and symbols');
    
    // Test arrow keys for navigation
    await user.type(titleInput, '{arrowleft}{arrowleft}');
    await user.type(titleInput, 'XX');
    expect(titleInput).toHaveValue('Task with spaces and symboXXls');
  });

  it('should maintain game functionality when not focused on forms', async () => {
    const gameComponent = render(GameCanvas);
    
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const canvasWrapper = gameComponent.container.querySelector('.canvas-wrapper');
    expect(canvasWrapper).toBeInTheDocument();
    
    // The canvas should be clickable and focusable
    expect(canvasWrapper).toHaveClass('canvas-wrapper');
    
    // Should show focus hint initially
    expect(screen.getByText('💡 Click on the game area to start playing!')).toBeInTheDocument();
  });
});
