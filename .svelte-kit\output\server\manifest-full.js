export const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set(["assets/README.md","favicon.svg"]),
	mimeTypes: {".md":"text/markdown",".svg":"image/svg+xml"},
	_: {
		client: {start:"_app/immutable/entry/start.C4InPzeU.js",app:"_app/immutable/entry/app.BB20F95X.js",imports:["_app/immutable/entry/start.C4InPzeU.js","_app/immutable/chunks/C7fiD0P4.js","_app/immutable/chunks/BT-7oyhX.js","_app/immutable/chunks/DAVv5PCD.js","_app/immutable/entry/app.BB20F95X.js","_app/immutable/chunks/DX-db2lG.js","_app/immutable/chunks/DAVv5PCD.js","_app/immutable/chunks/BT-7oyhX.js","_app/immutable/chunks/CWj6FrbW.js"],stylesheets:[],fonts:[],uses_env_dynamic_public:false},
		nodes: [
			__memo(() => import('./nodes/0.js')),
			__memo(() => import('./nodes/1.js')),
			__memo(() => import('./nodes/2.js'))
		],
		routes: [
			{
				id: "/",
				pattern: /^\/$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 2 },
				endpoint: null
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();
