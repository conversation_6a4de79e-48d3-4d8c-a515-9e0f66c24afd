

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.Df5LMrJl.js","_app/immutable/chunks/CWj6FrbW.js","_app/immutable/chunks/CfAXL0yB.js","_app/immutable/chunks/DAVv5PCD.js","_app/immutable/chunks/BT-7oyhX.js","_app/immutable/chunks/2NTtrG5-.js"];
export const stylesheets = [];
export const fonts = [];
