import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, fireEvent } from '@testing-library/svelte';
import GameCanvas from './GameCanvas.svelte';

// Mock PIXI.js
vi.mock('pixi.js', () => ({
  Application: vi.fn(() => ({
    canvas: document.createElement('canvas'),
    ticker: {
      add: vi.fn(),
      remove: vi.fn()
    },
    destroy: vi.fn()
  })),
  Sprite: {
    from: vi.fn(() => ({
      x: 0,
      y: 0,
      width: 32,
      height: 32,
      anchor: { set: vi.fn() }
    }))
  },
  Container: vi.fn(() => ({
    addChild: vi.fn(),
    removeChild: vi.fn()
  })),
  Assets: {
    load: vi.fn(() => Promise.resolve({}))
  }
}));

// Mock browser environment
vi.mock('$app/environment', () => ({
  browser: true
}));

// Mock stores
vi.mock('$lib/stores', () => ({
  gameStore: {
    subscribe: vi.fn(),
    update: vi.fn()
  },
  userStore: {
    addBananas: vi.fn()
  }
}));

// Mock game utils
vi.mock('$lib/utils/gameUtils', () => ({
  updateInputFromKeyboard: vi.fn(() => ({ left: false, right: false, up: false, jump: false })),
  createBananaParticles: vi.fn(() => []),
  updateParticles: vi.fn()
}));

describe('GameCanvas Keyboard Input Management', () => {
  let container: HTMLElement;
  let addEventListenerSpy: ReturnType<typeof vi.spyOn>;
  let removeEventListenerSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    // Spy on window event listeners
    addEventListenerSpy = vi.spyOn(window, 'addEventListener');
    removeEventListenerSpy = vi.spyOn(window, 'removeEventListener');
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should register keyboard event listeners on mount', async () => {
    render(GameCanvas);
    
    // Wait for component to mount and initialize
    await new Promise(resolve => setTimeout(resolve, 100));
    
    expect(addEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function));
    expect(addEventListenerSpy).toHaveBeenCalledWith('keyup', expect.any(Function));
  });

  it('should not capture keyboard input when canvas is not focused', async () => {
    const { component } = render(GameCanvas);
    
    // Wait for initialization
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Simulate keydown event when canvas is not focused
    const keydownEvent = new KeyboardEvent('keydown', { 
      code: 'ArrowLeft',
      bubbles: true,
      cancelable: true 
    });
    
    const preventDefaultSpy = vi.spyOn(keydownEvent, 'preventDefault');
    window.dispatchEvent(keydownEvent);
    
    // Should not prevent default when canvas is not focused
    expect(preventDefaultSpy).not.toHaveBeenCalled();
  });

  it('should not capture keyboard input when typing in form elements', async () => {
    // Create a mock input element
    const input = document.createElement('input');
    input.type = 'text';
    document.body.appendChild(input);
    
    render(GameCanvas);
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Focus the input
    input.focus();
    
    // Simulate keydown event on the input
    const keydownEvent = new KeyboardEvent('keydown', { 
      code: 'KeyA',
      bubbles: true,
      cancelable: true 
    });
    
    Object.defineProperty(keydownEvent, 'target', {
      value: input,
      writable: false
    });
    
    const preventDefaultSpy = vi.spyOn(keydownEvent, 'preventDefault');
    window.dispatchEvent(keydownEvent);
    
    // Should not prevent default when typing in form
    expect(preventDefaultSpy).not.toHaveBeenCalled();
    
    // Cleanup
    document.body.removeChild(input);
  });

  it('should detect form elements correctly', async () => {
    const { container } = render(GameCanvas);
    
    // Create various form elements
    const input = document.createElement('input');
    const textarea = document.createElement('textarea');
    const select = document.createElement('select');
    const contentEditable = document.createElement('div');
    contentEditable.contentEditable = 'true';
    
    const form = document.createElement('form');
    const buttonInForm = document.createElement('button');
    form.appendChild(buttonInForm);
    
    document.body.appendChild(input);
    document.body.appendChild(textarea);
    document.body.appendChild(select);
    document.body.appendChild(contentEditable);
    document.body.appendChild(form);
    
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Test each form element type
    const testElements = [
      { element: input, name: 'input' },
      { element: textarea, name: 'textarea' },
      { element: select, name: 'select' },
      { element: contentEditable, name: 'contentEditable' },
      { element: buttonInForm, name: 'element in form' }
    ];
    
    for (const { element, name } of testElements) {
      const keydownEvent = new KeyboardEvent('keydown', { 
        code: 'KeyA',
        bubbles: true,
        cancelable: true 
      });
      
      Object.defineProperty(keydownEvent, 'target', {
        value: element,
        writable: false
      });
      
      const preventDefaultSpy = vi.spyOn(keydownEvent, 'preventDefault');
      window.dispatchEvent(keydownEvent);
      
      expect(preventDefaultSpy).not.toHaveBeenCalled();
    }
    
    // Cleanup
    document.body.removeChild(input);
    document.body.removeChild(textarea);
    document.body.removeChild(select);
    document.body.removeChild(contentEditable);
    document.body.removeChild(form);
  });

  it('should show focus hint when canvas is not focused', async () => {
    const { getByText } = render(GameCanvas);
    
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Should show the focus hint
    expect(getByText('💡 Click on the game area to start playing!')).toBeInTheDocument();
  });

  it('should remove event listeners on destroy', async () => {
    const { component } = render(GameCanvas);
    
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Destroy the component
    component.$destroy();
    
    expect(removeEventListenerSpy).toHaveBeenCalledWith('keydown', expect.any(Function));
    expect(removeEventListenerSpy).toHaveBeenCalledWith('keyup', expect.any(Function));
  });

  it('should make canvas focusable and handle focus events', async () => {
    const { container } = render(GameCanvas);
    
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Find the canvas element (it should be created by PIXI mock)
    const canvasWrapper = container.querySelector('.canvas-wrapper');
    expect(canvasWrapper).toBeInTheDocument();
  });
});
