{"name": "banana-checklist", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.30.1", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/kit": "^2.22.2", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.8", "@testing-library/user-event": "^14.6.1", "@types/eslint__js": "^8.42.3", "@types/node": "^24.0.10", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "eslint": "^9.30.1", "eslint-plugin-svelte": "^3.10.1", "jsdom": "^26.1.0", "svelte": "^5.35.2", "svelte-check": "^4.2.2", "svelte-eslint-parser": "^1.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1", "vite": "^6.2.6", "vitest": "^3.2.4"}, "dependencies": {"pixi.js": "^8.11.0"}}